<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_usage_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->date('usage_date'); // Date of usage
            $table->integer('prompts_used')->default(0); // Number of prompts used on this date
            $table->enum('usage_type', ['chat', 'product_description', 'seo_optimization', 'chatbot_training'])->default('chat');
            $table->text('context')->nullable(); // Additional context about the usage
            $table->timestamps();

            // Ensure unique entry per user per date per type
            $table->unique(['user_id', 'usage_date', 'usage_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_usage_logs');
    }
};
