<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Course extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'seller_id',
        'title',
        'slug',
        'description',
        'short_description',
        'category_id',
        'subcategory_id',
        'detailed_category_id',
        'price',
        'discount_price',
        'difficulty_level',
        'status',
        'thumbnail',
        'images',
        'what_you_will_learn',
        'requirements',
        'target_audience',
        'estimated_duration',
        'average_rating',
        'reviews_count',
        'students_count',
        'is_featured',
        'published_at',
    ];

    protected $casts = [
        'price' => 'integer',
        'discount_price' => 'integer',
        'images' => 'array',
        'what_you_will_learn' => 'array',
        'requirements' => 'array',
        'target_audience' => 'array',
        'estimated_duration' => 'integer',
        'average_rating' => 'decimal:2',
        'reviews_count' => 'integer',
        'students_count' => 'integer',
        'is_featured' => 'boolean',
        'published_at' => 'datetime',
    ];

    // Difficulty level constants
    const DIFFICULTY_BEGINNER = 'beginner';
    const DIFFICULTY_INTERMEDIATE = 'intermediate';
    const DIFFICULTY_ADVANCED = 'advanced';

    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * Get the seller that owns the course
     */
    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    /**
     * Get the category that the course belongs to
     */
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the subcategory that the course belongs to
     */
    public function subcategory()
    {
        return $this->belongsTo(ProductSubcategory::class, 'subcategory_id');
    }

    /**
     * Get the detailed category that the course belongs to
     */
    public function detailedCategory()
    {
        return $this->belongsTo(ProductDetailedCategory::class, 'detailed_category_id');
    }

    /**
     * Get the sections for the course (Udemy-style)
     */
    public function sections()
    {
        return $this->hasMany(CourseSection::class)->orderBy('sort_order');
    }

    /**
     * Get the active sections for the course
     */
    public function activeSections()
    {
        return $this->hasMany(CourseSection::class)->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get all curriculum items for the course
     */
    public function curriculumItems()
    {
        return $this->hasMany(CourseCurriculumItem::class)->orderBy('sort_order');
    }

    /**
     * Get orders for this course
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get successful orders for this course
     */
    public function successfulOrders()
    {
        return $this->hasMany(Order::class)->where('status', 'success');
    }

    /**
     * Get all active curriculum items for the course
     */
    public function activeCurriculumItems()
    {
        return $this->hasMany(CourseCurriculumItem::class)->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get preview curriculum items for the course
     */
    public function previewCurriculumItems()
    {
        return $this->hasMany(CourseCurriculumItem::class)->where('is_preview', true)->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Get user progress records for this course
     */
    public function userProgress()
    {
        return $this->hasMany(UserCourseProgress::class);
    }

    /**
     * Get progress for a specific user
     */
    public function getProgressForUser($userId)
    {
        return $this->userProgress()->where('user_id', $userId)->first();
    }

    /**
     * Backward compatibility: alias for sections
     */
    public function chapters()
    {
        return $this->sections();
    }

    /**
     * Scope to get only active courses
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get only published courses
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)->whereNotNull('published_at');
    }

    /**
     * Scope to get featured courses
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by difficulty level
     */
    public function scopeByDifficulty($query, $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * Get the effective price (discount price if available, otherwise regular price)
     */
    public function getEffectivePriceAttribute()
    {
        return $this->discount_price ?? $this->price;
    }

    /**
     * Check if the course has a discount
     */
    public function getHasDiscountAttribute()
    {
        return !is_null($this->discount_price) && $this->discount_price < $this->price;
    }

    /**
     * Get the discount percentage
     */
    public function getDiscountPercentageAttribute()
    {
        if (!$this->has_discount) {
            return 0;
        }

        return round((($this->price - $this->discount_price) / $this->price) * 100);
    }

    /**
     * Get the thumbnail URL
     */
    public function getThumbnailUrlAttribute()
    {
        if (!$this->thumbnail) {
            return 'https://via.placeholder.com/400x225/6366f1/ffffff?text=Course';
        }

        return Storage::url($this->thumbnail);
    }

    /**
     * Get the total duration of the course in minutes
     */
    public function getTotalDurationAttribute()
    {
        try {
            return $this->curriculumItems()->sum('estimated_duration') ?? $this->estimated_duration ?? 0;
        } catch (\Exception) {
            return $this->estimated_duration ?? 0;
        }
    }

    /**
     * Get the total number of curriculum items in the course
     */
    public function getTotalItemsAttribute()
    {
        try {
            return $this->curriculumItems()->count();
        } catch (\Exception) {
            return 0;
        }
    }

    /**
     * Get the total number of sections in the course
     */
    public function getTotalSectionsAttribute()
    {
        return $this->sections()->count();
    }

    /**
     * Generate a unique slug for the course
     */
    public static function generateUniqueSlug($title, $excludeId = null)
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        $query = self::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        while ($query->exists()) {
            $slug = "{$baseSlug}-{$counter}";
            $counter++;
            $query = self::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
        }

        return $slug;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($course) {
            if (!$course->slug) {
                $course->slug = self::generateUniqueSlug($course->title);
            }
        });

        static::updating(function ($course) {
            if ($course->isDirty('title')) {
                $course->slug = self::generateUniqueSlug($course->title, $course->id);
            }
        });

        static::deleting(function ($course) {
            // Delete thumbnail
            if ($course->thumbnail) {
                Storage::delete($course->thumbnail);
            }

            // Delete additional images
            if ($course->images) {
                foreach ($course->images as $image) {
                    if (isset($image['path'])) {
                        Storage::delete($image['path']);
                    }
                }
            }

            // Delete all course curriculum item files
            foreach ($course->curriculumItems as $item) {
                if ($item->file_path) {
                    Storage::delete($item->file_path);
                }
            }
        });
    }
}
