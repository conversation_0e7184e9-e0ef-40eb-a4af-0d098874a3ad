<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserIsSeller
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!$request->user() || !$request->user()->isSeller()) {
            // If it's an AJAX request or expects JSON, return JSON error
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'error' => 'You do not have access to the seller dashboard.',
                    'redirect' => route('home')
                ], 403);
            }

            return redirect()->route('home')->with('error', 'You do not have access to the seller dashboard.');
        }

        return $next($request);
    }
}