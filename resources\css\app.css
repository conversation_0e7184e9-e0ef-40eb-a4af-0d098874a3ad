@tailwind base;
@tailwind components;
@tailwind utilities;


/* Base Styles */
body {
    font-family: 'Inter', sans-serif;
    color: #1a202c;
    line-height: 1.6;
}

.font-inter {
    font-family: 'Inter', sans-serif;
}

/* Decorative Circles in Hero */
.circle {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(147, 51, 234, 0.15), transparent);
    animation: float 10s ease-in-out infinite;
    opacity: 0.6;
}

.circle-1 {
    width: 450px;
    height: 450px;
    top: -10%;
    left: -5%;
    animation-delay: 0s;
}

.circle-2 {
    width: 350px;
    height: 350px;
    bottom: 5%;
    right: 0%;
    animation-delay: 2s;
}

.circle-3 {
    width: 250px;
    height: 250px;
    top: 35%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-30px) scale(1.03);
    }
}

/* Animations */
.animate-fade-in {
    opacity: 0;
    animation: fadeIn 1s ease-out forwards;
}

.animate-fade-in-up {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 1s ease-out forwards;
}

.animate-scale-in {
    opacity: 0;
    transform: scale(0.95);
    animation: scaleIn 1s ease-out forwards;
}

.animation-delay-200 {
    animation-delay: 200ms;
}

.animation-delay-400 {
    animation-delay: 400ms;
}

.animation-delay-600 {
    animation-delay: 600ms;
}

.animation-delay-800 {
    animation-delay: 800ms;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.4s ease;
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(90deg, #7c3aed, #4f46e5);
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 9999px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(124, 58, 237, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(90deg, #6d28d9, #4338ca);
    box-shadow: 0 6px 15px rgba(124, 58, 237, 0.3);
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: transparent;
    color: #7c3aed;
    border: 2px solid #7c3aed;
    padding: 0.875rem 2rem;
    border-radius: 9999px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(90deg, #7c3aed, #4f46e5);
    color: white;
    box-shadow: 0 4px 10px rgba(124, 58, 237, 0.2);
}

/* Navigation Menu Animation */
#nav-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-out;
}

#nav-menu.open {
    max-height: 500px;
}

/* Line Clamp for Text Overflow */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c4b5fd;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a78bfa;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    .circle {
        opacity: 0.3;
        transform: scale(0.6);
    }

    .hero-text {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* At the end of app.css */
@media (max-width: 767px) {
    .custom-hidden-md {
        display: none !important;
    }
}

@media (min-width: 768px) {
    .custom-md-flex {
        display: flex !important;
    }
}