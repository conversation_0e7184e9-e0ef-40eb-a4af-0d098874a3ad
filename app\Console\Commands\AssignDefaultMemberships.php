<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MembershipTier;
use App\Models\UserMembership;

class AssignDefaultMemberships extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'membership:assign-default';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign default starter membership to existing users who don\'t have any membership';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Assigning default memberships to existing users...');
        
        // Get the starter tier
        $starterTier = MembershipTier::where('slug', 'starter')->first();
        
        if (!$starterTier) {
            $this->error('Starter membership tier not found! Please run the MembershipTierSeeder first.');
            return Command::FAILURE;
        }
        
        // Get users without any membership
        $usersWithoutMembership = User::whereDoesntHave('memberships')->get();
        
        $this->info("Found {$usersWithoutMembership->count()} users without membership.");
        
        if ($usersWithoutMembership->count() === 0) {
            $this->info('All users already have memberships.');
            return Command::SUCCESS;
        }
        
        $bar = $this->output->createProgressBar($usersWithoutMembership->count());
        $bar->start();
        
        $assignedCount = 0;
        
        foreach ($usersWithoutMembership as $user) {
            try {
                UserMembership::create([
                    'user_id' => $user->id,
                    'membership_tier_id' => $starterTier->id,
                    'started_at' => now(),
                    'status' => 'active',
                ]);
                
                $assignedCount++;
            } catch (\Exception $e) {
                $this->error("Failed to assign membership to user {$user->email}: " . $e->getMessage());
            }
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        
        $this->info("Successfully assigned default memberships to {$assignedCount} users.");
        
        return Command::SUCCESS;
    }
}
