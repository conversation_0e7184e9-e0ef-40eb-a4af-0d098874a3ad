@extends('layouts.main')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">Help Center</h1>
            <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">
                Find answers to your questions and learn how to get the most out of Digitora.
            </p>
        </div>

        <!-- Search Section -->
        <div class="max-w-3xl mx-auto mb-12">
            <form action="{{ route('help') }}" method="GET" class="relative">
                <div class="flex items-center">
                    <input type="text" name="search" value="{{ $search ?? '' }}" placeholder="Search for help articles..."
                        class="block w-full rounded-l-full border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 py-3 px-6">
                    <button type="submit"
                        class="bg-purple-600 text-white px-6 py-3 rounded-r-full font-medium hover:bg-purple-700 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>

        @if($search)
            <!-- Search Results -->
            <div class="max-w-5xl mx-auto mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Search Results for "{{ $search }}"</h2>
                
                @php
                    $hasResults = false;
                    foreach($categories as $category) {
                        if($category->articles->count() > 0) {
                            $hasResults = true;
                            break;
                        }
                    }
                @endphp
                
                @if($hasResults)
                    <div class="space-y-6">
                        @foreach($categories as $category)
                            @if($category->articles->count() > 0)
                                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                                    <div class="p-6">
                                        <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                                            <span class="mr-2">{!! $category->icon !!}</span>
                                            {{ $category->name }}
                                        </h3>
                                        <ul class="space-y-4">
                                            @foreach($category->articles as $article)
                                                <li>
                                                    <a href="{{ route('help.article', $article->slug) }}" class="flex items-start hover:bg-gray-50 p-2 rounded-lg transition-colors duration-200">
                                                        <svg class="h-5 w-5 text-purple-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                        <div>
                                                            <h4 class="font-medium text-gray-900">{{ $article->title }}</h4>
                                                            <p class="text-sm text-gray-600 mt-1">
                                                                {{ Str::limit(strip_tags($article->content), 100) }}
                                                            </p>
                                                        </div>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                @else
                    <div class="bg-white p-8 rounded-xl shadow-sm text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-lg font-medium text-gray-900">No results found</h3>
                        <p class="mt-1 text-gray-500">Try different keywords or browse our help categories below.</p>
                    </div>
                @endif
                
                <div class="mt-6 text-center">
                    <a href="{{ route('help') }}" class="text-purple-600 font-medium hover:text-purple-700 transition-colors duration-300">
                        Clear search and view all help articles
                    </a>
                </div>
            </div>
        @else
            <!-- Help Categories -->
            <div class="max-w-5xl mx-auto mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Browse Help Categories</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($categories as $category)
                        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                            <div class="p-6">
                                <h3 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                                    <span class="mr-2">{!! $category->icon !!}</span>
                                    {{ $category->name }}
                                </h3>
                                <ul class="space-y-3">
                                    @foreach($category->articles as $article)
                                        <li>
                                            <a href="{{ route('help.article', $article->slug) }}" class="flex items-center text-gray-700 hover:text-purple-600 transition-colors duration-200">
                                                <svg class="h-4 w-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                                {{ $article->title }}
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                                <div class="mt-4">
                                    <a href="#" class="text-sm text-purple-600 font-medium hover:text-purple-700 transition-colors duration-300">
                                        View all in {{ $category->name }} →
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Frequently Asked Questions -->
        <div class="max-w-5xl mx-auto mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Frequently Asked Questions</h2>
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="divide-y divide-gray-200">
                    @foreach($faqs as $faq)
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $faq->question }}</h3>
                            <p class="text-gray-600">{{ $faq->answer }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="max-w-5xl mx-auto">
            <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-8 text-center text-white">
                <h2 class="text-2xl font-bold mb-4">Still Need Help?</h2>
                <p class="mb-6 max-w-2xl mx-auto">Can't find what you're looking for? Our support team is here to help.</p>
                <a href="{{ route('contact') }}" class="inline-block bg-white text-purple-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
                    Contact Support
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
