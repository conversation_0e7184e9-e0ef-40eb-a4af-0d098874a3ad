<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Analytics Report</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/digitora-logo.png') }}">
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }

        .container {
            width: 100%;
            margin: 0 auto;
        }

        h1 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #4F46E5;
        }

        h2 {
            font-size: 16px;
            margin-top: 20px;
            margin-bottom: 10px;
            color: #4F46E5;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }

        .summary-box {
            background-color: #f9fafb;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .summary-item {
            margin-bottom: 10px;
        }

        .summary-label {
            font-weight: bold;
            display: inline-block;
            width: 150px;
        }

        .summary-value {
            display: inline-block;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .footer {
            margin-top: 30px;
            font-size: 10px;
            color: #666;
            text-align: center;
        }

        .text-right {
            text-align: right;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Analytics Report</h1>
        <p>Period: {{ $timeRange }} | Generated: {{ date('Y-m-d H:i:s') }}</p>

        <div class="summary-box">
            <h2>Summary</h2>
            <div class="summary-item">
                <span class="summary-label">Total Revenue:</span>
                <span class="summary-value">Rp {{ number_format($totalRevenue, 0, ',', '.') }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Total Sales:</span>
                <span class="summary-value">{{ $totalSales }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Average Order Value:</span>
                <span class="summary-value">Rp {{ number_format($averageOrderValue, 0, ',', '.') }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Revenue Change:</span>
                <span class="summary-value">{{ number_format($revenueChange, 1) }}%</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Sales Change:</span>
                <span class="summary-value">{{ number_format($salesChange, 1) }}%</span>
            </div>
        </div>

        <h2>Top Products</h2>
        <table>
            <thead>
                <tr>
                    <th>Product Name</th>
                    <th>Category</th>
                    <th class="text-right">Price</th>
                    <th class="text-right">Sales</th>
                    <th class="text-right">Revenue</th>
                </tr>
            </thead>
            <tbody>
                @forelse($topProducts as $product)
                    <tr>
                        <td>{{ $product->name }}</td>
                        <td>{{ ucfirst($product->category ?? 'Uncategorized') }}</td>
                        <td class="text-right">Rp {{ number_format($product->price, 0, ',', '.') }}</td>
                        <td class="text-right">{{ $product->orders_count }}</td>
                        <td class="text-right">Rp {{ number_format($product->orders_sum_amount, 0, ',', '.') }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" style="text-align: center;">No products data available</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <h2>Category Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>Category</th>
                    <th class="text-right">Sales Count</th>
                    <th class="text-right">Revenue</th>
                </tr>
            </thead>
            <tbody>
                @forelse($categoryData as $item)
                    <tr>
                        <td>{{ ucfirst($item->category ?? 'Uncategorized') }}</td>
                        <td class="text-right">{{ $item->orders_count }}</td>
                        <td class="text-right">Rp {{ number_format($item->orders_sum_amount, 0, ',', '.') }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="3" style="text-align: center;">No category data available</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <h2>Revenue by Date</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th class="text-right">Revenue</th>
                </tr>
            </thead>
            <tbody>
                @forelse($revenueData as $item)
                    <tr>
                        <td>{{ \Carbon\Carbon::parse($item->date)->format('Y-m-d') }}</td>
                        <td class="text-right">Rp {{ number_format($item->revenue, 0, ',', '.') }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="2" style="text-align: center;">No revenue data available</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <h2>Sales by Date</h2>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th class="text-right">Number of Sales</th>
                </tr>
            </thead>
            <tbody>
                @forelse($salesData as $item)
                    <tr>
                        <td>{{ \Carbon\Carbon::parse($item->date)->format('Y-m-d') }}</td>
                        <td class="text-right">{{ $item->count }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="2" style="text-align: center;">No sales data available</td>
                    </tr>
                @endforelse
            </tbody>
        </table>

        <div class="footer">
            <p>This report was generated automatically. For more detailed analytics, please visit the analytics
                dashboard.</p>
        </div>
    </div>
</body>

</html>
