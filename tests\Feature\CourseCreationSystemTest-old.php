<?php

namespace Tests\Feature;

use App\Models\Product;
use App\Models\ProductResource;
use App\Models\User;
use App\Models\Order;
use App\Models\SellerApplication;
use App\Models\Role;
use App\Models\UserRole;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CourseCreationSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $seller;
    protected $buyer;
    protected $sellerApplication;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        $sellerRole = Role::create(['name' => 'Seller', 'slug' => 'seller']);
        $buyerRole = Role::create(['name' => 'Buyer', 'slug' => 'buyer']);
        
        // Create seller user
        $this->seller = User::factory()->create([
            'name' => 'Test Seller',
            'email' => '<EMAIL>',
        ]);
        
        UserRole::create([
            'user_id' => $this->seller->id,
            'role_id' => $sellerRole->id,
            'is_active' => true,
        ]);
        
        // Create seller application
        $this->sellerApplication = SellerApplication::create([
            'user_id' => $this->seller->id,
            'store_name' => 'Test Store',
            'store_name_slug' => 'test-store',
            'store_description' => 'Test store description',
            'status' => 'approved',
            'id_type' => 'ktp',
            'id_number' => '**********123456',
            'phone' => '08**********',
            'address' => 'Test Address',
            'bank_name' => 'Test Bank',
            'account_number' => '**********',
            'account_holder_name' => 'Test Seller',
        ]);
        
        // Create buyer user
        $this->buyer = User::factory()->create([
            'name' => 'Test Buyer',
            'email' => '<EMAIL>',
        ]);
        
        UserRole::create([
            'user_id' => $this->buyer->id,
            'role_id' => $buyerRole->id,
            'is_active' => true,
        ]);
        
        Storage::fake('public');
    }

    /** @test */
    public function seller_can_create_simple_product()
    {
        $this->actingAs($this->seller);
        
        $file = UploadedFile::fake()->create('test-file.pdf', 1000);
        $image = UploadedFile::fake()->image('product.jpg');
        
        $response = $this->post(route('seller.products.store'), [
            'name' => 'Test Simple Product',
            'description' => 'This is a test simple product',
            'category' => 'digital-marketing',
            'price' => 50000,
            'status' => 'active',
            'content_type' => 'simple',
            'images' => [$image],
            'files' => [$file],
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $this->assertDatabaseHas('products', [
            'name' => 'Test Simple Product',
            'content_type' => 'simple',
            'seller_id' => $this->seller->id,
        ]);
        
        $product = Product::where('name', 'Test Simple Product')->first();
        $this->assertNotNull($product);
        $this->assertTrue($product->isSimple());
        $this->assertFalse($product->isCourse());
    }

    /** @test */
    public function seller_can_create_course_product_with_multiple_resources()
    {
        $this->actingAs($this->seller);
        
        $image = UploadedFile::fake()->image('course.jpg');
        $pdfFile = UploadedFile::fake()->create('lesson1.pdf', 1000);
        
        $response = $this->post(route('seller.products.store'), [
            'name' => 'Test Course Product',
            'description' => 'This is a comprehensive course',
            'category' => 'education',
            'price' => 100000,
            'status' => 'active',
            'content_type' => 'course',
            'images' => [$image],
            'resources' => [
                1 => [
                    'title' => 'Introduction Video',
                    'description' => 'Course introduction',
                    'type' => 'video',
                    'content' => 'https://youtube.com/watch?v=test',
                    'sort_order' => 1,
                    'is_preview' => '1',
                ],
                2 => [
                    'title' => 'Course Materials',
                    'description' => 'PDF materials',
                    'type' => 'pdf',
                    'sort_order' => 2,
                ],
                3 => [
                    'title' => 'Course Notes',
                    'description' => 'Text-based notes',
                    'type' => 'text',
                    'content' => 'These are the course notes with important information.',
                    'sort_order' => 3,
                ],
            ],
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $product = Product::where('name', 'Test Course Product')->first();
        $this->assertNotNull($product);
        $this->assertTrue($product->isCourse());
        $this->assertFalse($product->isSimple());
        
        // Check resources were created
        $this->assertEquals(3, $product->resources()->count());
        
        $videoResource = $product->resources()->where('type', 'video')->first();
        $this->assertNotNull($videoResource);
        $this->assertEquals('Introduction Video', $videoResource->title);
        $this->assertTrue($videoResource->is_preview);
        
        $textResource = $product->resources()->where('type', 'text')->first();
        $this->assertNotNull($textResource);
        $this->assertEquals('Course Notes', $textResource->title);
        $this->assertStringContainsString('important information', $textResource->content);
    }

    /** @test */
    public function buyer_can_access_course_content_after_purchase()
    {
        // Create a course product
        $product = Product::create([
            'seller_id' => $this->seller->id,
            'name' => 'Test Course',
            'slug' => 'test-course',
            'description' => 'Test course description',
            'category' => 'education',
            'price' => 75000,
            'status' => 'active',
            'content_type' => 'course',
        ]);
        
        // Add resources
        $product->resources()->create([
            'title' => 'Lesson 1',
            'description' => 'First lesson',
            'type' => 'video',
            'content' => 'https://youtube.com/watch?v=lesson1',
            'sort_order' => 1,
            'is_active' => true,
            'is_preview' => false,
        ]);
        
        $product->resources()->create([
            'title' => 'Course Notes',
            'description' => 'Important notes',
            'type' => 'text',
            'content' => 'These are important course notes.',
            'sort_order' => 2,
            'is_active' => true,
            'is_preview' => false,
        ]);
        
        // Create successful order
        $order = Order::create([
            'order_id' => 'test-order-123',
            'buyer_id' => $this->buyer->id,
            'product_id' => $product->id,
            'amount' => 75000,
            'status' => 'success',
        ]);
        
        $this->actingAs($this->buyer);
        
        // Test course access
        $response = $this->get(route('user.course-access', $product->id));
        $response->assertStatus(200);
        $response->assertSee('Test Course');
        $response->assertSee('Lesson 1');
        $response->assertSee('Course Notes');
        
        // Test resource access
        $videoResource = $product->resources()->where('type', 'video')->first();
        $response = $this->get(route('user.download-resource', [$product->id, $videoResource->id]));
        $response->assertRedirect('https://youtube.com/watch?v=lesson1');
        
        $textResource = $product->resources()->where('type', 'text')->first();
        $response = $this->get(route('user.download-resource', [$product->id, $textResource->id]));
        $response->assertStatus(200);
        $response->assertSee('Course Notes');
    }

    /** @test */
    public function buyer_cannot_access_course_without_purchase()
    {
        $product = Product::create([
            'seller_id' => $this->seller->id,
            'name' => 'Paid Course',
            'slug' => 'paid-course',
            'description' => 'This is a paid course',
            'category' => 'education',
            'price' => 100000,
            'status' => 'active',
            'content_type' => 'course',
        ]);
        
        $this->actingAs($this->buyer);
        
        $response = $this->get(route('user.course-access', $product->id));
        $response->assertRedirect();
        $response->assertSessionHas('error', 'You do not have permission to access this course');
    }

    /** @test */
    public function seller_can_edit_course_resources()
    {
        $product = Product::create([
            'seller_id' => $this->seller->id,
            'name' => 'Editable Course',
            'slug' => 'editable-course',
            'description' => 'Course for editing test',
            'category' => 'education',
            'price' => 80000,
            'status' => 'draft',
            'content_type' => 'course',
        ]);
        
        $existingResource = $product->resources()->create([
            'title' => 'Original Resource',
            'description' => 'Original description',
            'type' => 'text',
            'content' => 'Original content',
            'sort_order' => 1,
            'is_active' => true,
        ]);
        
        $this->actingAs($this->seller);
        
        $response = $this->put(route('seller.products.update', $product), [
            'name' => 'Updated Course',
            'description' => 'Updated course description',
            'category' => 'education',
            'price' => 90000,
            'status' => 'active',
            'content_type' => 'course',
            'resources' => [
                1 => [
                    'id' => $existingResource->id,
                    'title' => 'Updated Resource',
                    'description' => 'Updated description',
                    'type' => 'text',
                    'content' => 'Updated content',
                    'sort_order' => 1,
                ],
                2 => [
                    'title' => 'New Resource',
                    'description' => 'New resource description',
                    'type' => 'link',
                    'content' => 'https://example.com',
                    'sort_order' => 2,
                ],
            ],
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $product->refresh();
        $this->assertEquals('Updated Course', $product->name);
        $this->assertEquals(2, $product->resources()->count());
        
        $updatedResource = $product->resources()->find($existingResource->id);
        $this->assertEquals('Updated Resource', $updatedResource->title);
        $this->assertEquals('Updated content', $updatedResource->content);
        
        $newResource = $product->resources()->where('title', 'New Resource')->first();
        $this->assertNotNull($newResource);
        $this->assertEquals('link', $newResource->type);
    }

    /** @test */
    public function system_maintains_backward_compatibility_with_simple_products()
    {
        // Create old-style simple product
        $product = Product::create([
            'seller_id' => $this->seller->id,
            'name' => 'Legacy Product',
            'slug' => 'legacy-product',
            'description' => 'Legacy product description',
            'category' => 'digital-marketing',
            'price' => 25000,
            'status' => 'active',
            'content_type' => 'simple',
            'files' => json_encode([
                ['name' => 'legacy-file.pdf', 'path' => 'products/files/legacy-file.pdf', 'size' => 1024]
            ]),
        ]);
        
        // Create successful order
        Order::create([
            'order_id' => 'legacy-order-123',
            'buyer_id' => $this->buyer->id,
            'product_id' => $product->id,
            'amount' => 25000,
            'status' => 'success',
        ]);
        
        $this->actingAs($this->buyer);
        
        // Test that legacy download still works
        $response = $this->post(route('product.download', $product->id));
        
        // Should redirect to course access for course products, but handle simple products normally
        $this->assertTrue($product->isSimple());
        $this->assertFalse($product->isCourse());
        $this->assertFalse($product->hasResources());
        $this->assertTrue($product->hasLegacyFiles());
    }
}
