<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Models\UserRole;
use App\Models\SellerApplication;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CourseSessionAutoSaveTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $seller;
    protected $sellerApplication;
    protected $category;
    protected $subcategory;
    protected $detailedCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create seller role
        $sellerRole = Role::create(['name' => 'Seller', 'slug' => 'seller']);

        // Create seller user
        $this->seller = User::factory()->create([
            'name' => 'Test Seller',
            'email' => '<EMAIL>',
        ]);

        UserRole::create([
            'user_id' => $this->seller->id,
            'role_id' => $sellerRole->id,
            'is_active' => true,
        ]);

        // Create seller application
        $this->sellerApplication = SellerApplication::create([
            'user_id' => $this->seller->id,
            'store_name' => 'Test Store',
            'store_name_slug' => 'test-store',
            'store_description' => 'Test store description',
            'status' => 'approved',
            'id_type' => 'ktp',
            'id_number' => '**********123456',
            'phone' => '************',
            'address' => 'Test Address',
            'bank_name' => 'Test Bank',
            'account_number' => '**********',
            'account_holder_name' => 'Test Seller',
        ]);

        // Create test categories
        $this->category = ProductCategory::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'Test category description',
            'product_type' => 'course',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $this->subcategory = ProductSubcategory::create([
            'category_id' => $this->category->id,
            'name' => 'Test Subcategory',
            'slug' => 'test-subcategory',
            'description' => 'Test subcategory description',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        $this->detailedCategory = ProductDetailedCategory::create([
            'subcategory_id' => $this->subcategory->id,
            'name' => 'Test Detailed Category',
            'slug' => 'test-detailed-category',
            'description' => 'Test detailed category description',
            'is_active' => true,
            'sort_order' => 1,
        ]);

        Storage::fake('public');
    }

    /** @test */
    public function course_step1_data_persists_in_session()
    {
        $this->actingAs($this->seller);
        
        // Save step 1 data
        $response = $this->post(route('seller.products.save-course-step', ['step' => 1]), [
            'name' => 'Test Course',
            'description' => 'Test course description',
            'category' => 'test-category',
            'category_id' => $this->category->id,
            'subcategory_id' => $this->subcategory->id,
            'detailed_category_id' => $this->detailedCategory->id,
            'price' => 100000,
        ]);
        
        $response->assertRedirect(route('seller.products.create-course-step', ['step' => 2]));
        
        // Check session data
        $courseData = session('course_creation_data');
        $this->assertNotNull($courseData);
        $this->assertArrayHasKey('step1', $courseData);
        $this->assertEquals('Test Course', $courseData['step1']['name']);
        $this->assertEquals('Test course description', $courseData['step1']['description']);
        $this->assertEquals(100000, $courseData['step1']['price']);
    }

    /** @test */
    public function course_images_auto_save_to_session()
    {
        $this->actingAs($this->seller);
        
        // Prepare image data as it would come from JavaScript
        $imageData = [
            [
                'name' => 'test-image.jpg',
                'size' => 1024,
                'type' => 'image/jpeg',
                'data' => 'data:image/jpeg;base64,' . base64_encode('fake-image-data'),
                'index' => 0
            ]
        ];
        
        $response = $this->postJson(route('seller.products.auto-save-images'), [
            'images' => $imageData,
            'step' => 1
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        // Check session data
        $courseData = session('course_creation_data');
        $this->assertNotNull($courseData);
        $this->assertArrayHasKey('step1', $courseData);
        $this->assertArrayHasKey('images', $courseData['step1']);
        $this->assertCount(1, $courseData['step1']['images']);
        $this->assertEquals('test-image.jpg', $courseData['step1']['images'][0]['name']);
    }

    /** @test */
    public function course_step2_data_auto_saves_to_session()
    {
        $this->actingAs($this->seller);
        
        // Prepare course structure data
        $formData = [
            'chapters' => [
                [
                    'title' => 'Chapter 1',
                    'description' => 'First chapter',
                    'sub_chapters' => [
                        [
                            'title' => 'Lesson 1',
                            'description' => 'First lesson'
                        ]
                    ]
                ]
            ],
            'content' => [
                [
                    'chapter_index' => 0,
                    'sub_chapter_index' => 0,
                    'resources' => [
                        [
                            'title' => 'Video Resource',
                            'type' => 'video',
                            'description' => 'Test video',
                            'content' => 'https://youtube.com/watch?v=test',
                            'url' => 'https://youtube.com/watch?v=test',
                            'is_preview' => false
                        ]
                    ]
                ]
            ]
        ];
        
        $response = $this->postJson(route('seller.products.auto-save-course-data'), [
            'step' => 2,
            'data' => $formData
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        // Check session data
        $courseData = session('course_creation_data');
        $this->assertNotNull($courseData);
        $this->assertArrayHasKey('step2', $courseData);
        $this->assertArrayHasKey('step3', $courseData);
        $this->assertEquals('Chapter 1', $courseData['step2']['chapters'][0]['title']);
        $this->assertEquals('https://youtube.com/watch?v=test', $courseData['step3']['content'][0]['resources'][0]['url']);
    }

    /** @test */
    public function existing_images_are_preserved_when_returning_to_step1()
    {
        $this->actingAs($this->seller);
        
        // First, save some images
        $imageData = [
            [
                'name' => 'existing-image.jpg',
                'size' => 2048,
                'type' => 'image/jpeg',
                'data' => 'data:image/jpeg;base64,' . base64_encode('existing-image-data'),
                'index' => 0
            ]
        ];
        
        $this->postJson(route('seller.products.auto-save-images'), [
            'images' => $imageData,
            'step' => 1
        ]);
        
        // Now test auto-save with existing images
        $existingImageData = [
            [
                'name' => 'existing-image.jpg',
                'size' => 2048,
                'type' => 'image/jpeg',
                'data' => '',
                'index' => 0,
                'isExisting' => true,
                'path' => 'temp/course-images/existing-image.jpg',
                'url' => asset('storage/temp/course-images/existing-image.jpg')
            ]
        ];
        
        $response = $this->postJson(route('seller.products.auto-save-images'), [
            'images' => $existingImageData,
            'step' => 1
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        // Check that existing image is preserved
        $courseData = session('course_creation_data');
        $this->assertNotNull($courseData);
        $this->assertArrayHasKey('step1', $courseData);
        $this->assertArrayHasKey('images', $courseData['step1']);
        $this->assertCount(1, $courseData['step1']['images']);
        $this->assertEquals('existing-image.jpg', $courseData['step1']['images'][0]['name']);
        $this->assertStringContainsString('existing-image.jpg', $courseData['step1']['images'][0]['path']);
    }

    /** @test */
    public function course_data_persists_across_step_navigation()
    {
        $this->actingAs($this->seller);

        // Save step 1 data and check session immediately
        $response = $this->post(route('seller.products.save-course-step', ['step' => 1]), [
            'name' => 'Persistent Course',
            'description' => 'Course that persists',
            'category' => 'test-category',
            'category_id' => $this->category->id,
            'subcategory_id' => $this->subcategory->id,
            'detailed_category_id' => $this->detailedCategory->id,
            'price' => 150000,
        ]);

        $response->assertRedirect(route('seller.products.create-course-step', ['step' => 2]));

        // Check that data was saved to session
        $courseData = session('course_creation_data');
        $this->assertNotNull($courseData);
        $this->assertArrayHasKey('step1', $courseData);
        $this->assertEquals('Persistent Course', $courseData['step1']['name']);
        $this->assertEquals(150000, $courseData['step1']['price']);

        // Test that step 2 page loads successfully (indicating session data is available)
        $response = $this->get(route('seller.products.create-course-step', ['step' => 2]));
        $response->assertStatus(200);
        $response->assertSee('Persistent Course'); // Should display course name from session
    }
}
