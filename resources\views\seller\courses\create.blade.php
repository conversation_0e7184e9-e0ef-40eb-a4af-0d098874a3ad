@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header with <PERSON><PERSON> -->
    <div class="flex items-center gap-4">
        <a href="{{ route('seller.courses.index') }}"
           class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                <path d="m15 18-6-6 6-6"></path>
            </svg>
            <span class="sr-only">Back</span>
        </a>
        <div>
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">Create New Course</h1>
            <p class="text-gray-600">Create a structured learning experience for your students</p>
        </div>
    </div>

    <!-- Progress Steps -->
    <div class="rounded-xl border bg-white shadow-lg p-6">
        <div class="flex items-center space-x-4">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-indigo-600 text-white rounded-full text-sm font-medium">
                    1
                </div>
                <span class="ml-2 text-sm font-medium text-indigo-600">Course Information</span>
            </div>
            <div class="flex-1 h-px bg-gray-200"></div>
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-500 rounded-full text-sm font-medium">
                    2
                </div>
                <span class="ml-2 text-sm text-gray-500">Add Content</span>
            </div>
        </div>
    </div>

    <!-- Auto-save Recovery Notice -->
    @if(isset($hasAutoSave) && $hasAutoSave)
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4" id="auto-save-notice">
        <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <div class="flex-1">
                <h4 class="text-sm font-medium text-blue-900">Auto-saved data found</h4>
                <p class="text-sm text-blue-700 mt-1">
                    We found some unsaved course data from {{ \Carbon\Carbon::parse($autoSavedData['timestamp'])->diffForHumans() }}.
                    Would you like to restore it?
                </p>
                <div class="mt-3 flex space-x-3">
                    <button type="button"
                            onclick="restoreAutoSavedData()"
                            class="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded font-medium transition-colors">
                        Restore Data
                    </button>
                    <button type="button"
                            onclick="clearAutoSavedData()"
                            class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        Start Fresh
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Course Creation Form -->
    <form action="{{ route('seller.courses.store') }}" method="POST" enctype="multipart/form-data" id="course-creation-form">
        @csrf

        <div class="grid gap-6 md:grid-cols-6">
            <div class="space-y-6 md:col-span-4">
                <!-- Basic Information -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Course Information</h3>
                        <p class="text-sm text-gray-600">Basic information about your course</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="space-y-2">
                            <label for="title" class="block text-sm font-medium text-gray-700">
                                Course Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   value="{{ old('title') }}"
                                   required
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                   placeholder="e.g., Complete Guide to Digital Marketing for Beginners">
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="space-y-2">
                            <label for="short_description" class="block text-sm font-medium text-gray-700">
                                Short Description
                            </label>
                            <textarea id="short_description"
                                      name="short_description"
                                      rows="3"
                                      class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                      placeholder="A concise overview of what this course covers and who it's for...">{{ old('short_description') }}</textarea>
                            <p class="text-xs text-gray-500">
                                Brief summary for course listings (recommended: 100-200 characters)
                            </p>
                            @error('short_description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="space-y-2">
                            <label for="description" class="block text-sm font-medium text-gray-700">
                                Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description"
                                      name="description"
                                      rows="8"
                                      required
                                      class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                      placeholder="Provide a comprehensive description of your course including what students will learn, course objectives, prerequisites, and target audience...">{{ old('description') }}</textarea>
                            <p class="text-xs text-gray-500">
                                Provide a detailed description of your course, including its features and benefits.
                            </p>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Learning Outcomes and Requirements -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Learning Outcomes & Requirements</h3>
                        <p class="text-sm text-gray-600">Define what students will learn and what they need to know</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <!-- Learning Outcomes -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                What You Will Learn <span class="text-red-500">*</span>
                            </label>
                            <div id="what-you-will-learn-container" class="space-y-3">
                                <div class="what-you-will-learn-item flex items-center space-x-3">
                                    <input type="text"
                                           name="what_you_will_learn[]"
                                           required
                                           class="flex-1 block rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                           placeholder="e.g., Master the fundamentals of digital marketing">
                                    <button type="button"
                                            onclick="removeItem(this)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <button type="button"
                                    onclick="addLearningOutcome()"
                                    class="mt-3 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Learning Outcome
                            </button>
                            <p class="text-xs text-gray-500">
                                Key learning outcomes for students
                            </p>
                        </div>

                        <!-- Requirements -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                Requirements <span class="text-red-500">*</span>
                            </label>
                            <div id="requirements-container" class="space-y-3">
                                <div class="requirements-item flex items-center space-x-3">
                                    <input type="text"
                                           name="requirements[]"
                                           required
                                           class="flex-1 block rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                           placeholder="e.g., Basic computer skills required">
                                    <button type="button"
                                            onclick="removeItem(this)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <button type="button"
                                    onclick="addRequirement()"
                                    class="mt-3 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Requirement
                            </button>
                            <p class="text-xs text-gray-500">
                                Prerequisites and what students need
                            </p>
                        </div>

                        <!-- Target Audience -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                Target Audience <span class="text-red-500">*</span>
                            </label>
                            <div id="target-audience-container" class="space-y-3">
                                <div class="target-audience-item flex items-center space-x-3">
                                    <input type="text"
                                           name="target_audience[]"
                                           required
                                           class="flex-1 block rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                           placeholder="e.g., Business owners looking to grow their online presence">
                                    <button type="button"
                                            onclick="removeItem(this)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <button type="button"
                                    onclick="addTargetAudience()"
                                    class="mt-3 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Target Audience
                            </button>
                            <p class="text-xs text-gray-500">
                                Who this course is designed for
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Category Selection -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Category Selection</h3>
                        <p class="text-sm text-gray-600">Choose the most appropriate category for your course</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="space-y-2">
                                <label for="category_id" class="block text-sm font-medium text-gray-700">
                                    Main Category <span class="text-red-500">*</span>
                                </label>
                                <select id="category_id"
                                        name="category_id"
                                        required
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                    <option value="">Choose main category...</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="subcategory_id" class="block text-sm font-medium text-gray-700">
                                    Subcategory <span class="text-red-500">*</span>
                                </label>
                                <select id="subcategory_id"
                                        name="subcategory_id"
                                        required
                                        disabled
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors disabled:bg-gray-50 disabled:text-gray-500">
                                    <option value="">Select main category first...</option>
                                </select>
                                @error('subcategory_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="detailed_category_id" class="block text-sm font-medium text-gray-700">
                                    Detailed Category <span class="text-red-500">*</span>
                                </label>
                                <select id="detailed_category_id"
                                        name="detailed_category_id"
                                        required
                                        disabled
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors disabled:bg-gray-50 disabled:text-gray-500">
                                    <option value="">Select subcategory first...</option>
                                </select>
                                @error('detailed_category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                <div>
                                    <h4 class="text-sm font-medium text-blue-900">Category Selection Tips</h4>
                                    <p class="text-sm text-blue-700 mt-1">Choose categories that best represent your course content. This helps students find your course more easily and improves discoverability.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="space-y-6 md:col-span-2">
                <!-- Pricing -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Pricing</h3>
                        <p class="text-sm text-gray-600">Set your course pricing</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="space-y-2">
                            <label for="price" class="block text-sm font-medium text-gray-700">
                                Price (Rp) <span class="text-red-500">*</span>
                            </label>
                            <input type="number"
                                   id="price"
                                   name="price"
                                   value="{{ old('price') }}"
                                   min="5000"
                                   required
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                   placeholder="50000">
                            <p class="mt-1 text-xs text-gray-500">Minimum price is Rp 5,000</p>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="space-y-2">
                            <label for="discount_price" class="block text-sm font-medium text-gray-700">
                                Discount Price (Rp)
                            </label>
                            <input type="number"
                                   id="discount_price"
                                   name="discount_price"
                                   value="{{ old('discount_price') }}"
                                   min="5000"
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                   placeholder="40000">
                            <p class="mt-1 text-xs text-gray-500">Optional discount price</p>
                            @error('discount_price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Course Details -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Course Details</h3>
                        <p class="text-sm text-gray-600">Additional course information</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="space-y-2">
                            <label for="difficulty_level" class="block text-sm font-medium text-gray-700">
                                Difficulty Level <span class="text-red-500">*</span>
                            </label>
                            <select id="difficulty_level"
                                    name="difficulty_level"
                                    required
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                <option value="">Select Difficulty</option>
                                <option value="beginner" {{ old('difficulty_level') === 'beginner' ? 'selected' : '' }}>Beginner</option>
                                <option value="intermediate" {{ old('difficulty_level') === 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                                <option value="advanced" {{ old('difficulty_level') === 'advanced' ? 'selected' : '' }}>Advanced</option>
                            </select>
                            @error('difficulty_level')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="space-y-2">
                            <label for="estimated_duration" class="block text-sm font-medium text-gray-700">
                                Estimated Duration (minutes)
                            </label>
                            <input type="number"
                                   id="estimated_duration"
                                   name="estimated_duration"
                                   value="{{ old('estimated_duration') }}"
                                   min="1"
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                   placeholder="120">
                            <p class="mt-1 text-xs text-gray-500">Total estimated course duration</p>
                            @error('estimated_duration')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Course Images -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Course Images</h3>
                        <p class="text-sm text-gray-600">Upload images for your course</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="space-y-2">
                            <label for="thumbnail" class="block text-sm font-medium text-gray-700">
                                Course Thumbnail
                            </label>
                            <input type="file"
                                   id="thumbnail"
                                   name="thumbnail"
                                   accept="image/*"
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                            <p class="mt-1 text-xs text-gray-500">Recommended: 1280x720px, max 2MB</p>
                            @error('thumbnail')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="space-y-2">
                            <label for="images" class="block text-sm font-medium text-gray-700">
                                Additional Images
                            </label>
                            <input type="file"
                                   id="images"
                                   name="images[]"
                                   accept="image/*"
                                   multiple
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                            <p class="mt-1 text-xs text-gray-500">Max 5 images, 2MB each</p>
                            @error('images')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="rounded-xl border bg-white shadow-lg p-6">
            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('seller.courses.index') }}"
                       class="inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Cancel
                    </a>
                    <button type="button"
                            id="save-draft-btn"
                            class="inline-flex items-center rounded-lg border border-indigo-300 bg-white px-4 py-2 text-sm font-medium text-indigo-700 shadow-sm hover:bg-indigo-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Save as Draft
                    </button>
                </div>
                <button type="submit"
                        class="inline-flex items-center rounded-lg bg-indigo-600 px-6 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Course
                </button>
            </div>

            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-gray-600 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Next Steps</h4>
                        <p class="text-sm text-gray-600 mt-1">After creating your course, you'll be able to add chapters, lessons, and materials to build your complete course structure.</p>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
// Enhanced Course Creation JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Character counting functionality
    const shortDescTextarea = document.getElementById('short_description');
    const descTextarea = document.getElementById('description');
    const shortDescCounter = document.getElementById('short-desc-count');
    const descCounter = document.getElementById('desc-count');

    function updateCharacterCount(textarea, counter, maxLength = null) {
        const length = textarea.value.length;
        if (maxLength) {
            counter.textContent = `${length}/${maxLength}`;
            if (length > maxLength * 0.9) {
                counter.classList.add('text-yellow-600');
            } else {
                counter.classList.remove('text-yellow-600');
            }
        } else {
            counter.textContent = `${length} characters`;
        }
    }

    if (shortDescTextarea && shortDescCounter) {
        shortDescTextarea.addEventListener('input', () => updateCharacterCount(shortDescTextarea, shortDescCounter, 500));
        updateCharacterCount(shortDescTextarea, shortDescCounter, 500);
    }

    if (descTextarea && descCounter) {
        descTextarea.addEventListener('input', () => updateCharacterCount(descTextarea, descCounter));
        updateCharacterCount(descTextarea, descCounter);
    }

    // Enhanced category cascade functionality
    const categories = @json($categories);
    const categorySelect = document.getElementById('category_id');
    const subcategorySelect = document.getElementById('subcategory_id');
    const detailedCategorySelect = document.getElementById('detailed_category_id');

    function enableSelect(select, placeholder) {
        select.disabled = false;
        select.classList.remove('disabled:bg-gray-50', 'disabled:text-gray-500');
        if (placeholder) {
            select.innerHTML = `<option value="">${placeholder}</option>`;
        }
    }

    function disableSelect(select, placeholder) {
        select.disabled = true;
        select.classList.add('disabled:bg-gray-50', 'disabled:text-gray-500');
        select.innerHTML = `<option value="">${placeholder}</option>`;
    }

    categorySelect.addEventListener('change', function() {
        const categoryId = this.value;

        // Reset subcategory and detailed category
        disableSelect(subcategorySelect, 'Select main category first...');
        disableSelect(detailedCategorySelect, 'Select subcategory first...');

        if (categoryId) {
            const category = categories.find(cat => cat.id === categoryId);
            if (category && category.active_subcategories) {
                enableSelect(subcategorySelect, 'Choose subcategory...');

                category.active_subcategories.forEach(subcategory => {
                    const option = document.createElement('option');
                    option.value = subcategory.id;
                    option.textContent = subcategory.name;
                    subcategorySelect.appendChild(option);
                });
            }
        }
    });

    subcategorySelect.addEventListener('change', function() {
        const subcategoryId = this.value;

        // Reset detailed category
        disableSelect(detailedCategorySelect, 'Select subcategory first...');

        if (subcategoryId) {
            const categoryId = categorySelect.value;
            const category = categories.find(cat => cat.id === categoryId);
            if (category && category.active_subcategories) {
                const subcategory = category.active_subcategories.find(sub => sub.id === subcategoryId);
                if (subcategory && subcategory.active_detailed_categories) {
                    enableSelect(detailedCategorySelect, 'Choose detailed category...');

                    subcategory.active_detailed_categories.forEach(detailedCategory => {
                        const option = document.createElement('option');
                        option.value = detailedCategory.id;
                        option.textContent = detailedCategory.name;
                        detailedCategorySelect.appendChild(option);
                    });
                }
            }
        }
    });

    // Form state tracking
    let isFormDirty = false;
    const form = document.getElementById('course-creation-form');
    const saveDraftBtn = document.getElementById('save-draft-btn');

    // Create auto-save indicator
    function createAutoSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'auto-save-indicator';
        indicator.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform translate-y-full opacity-0 z-50';
        document.body.appendChild(indicator);
        return indicator;
    }

    const autoSaveIndicator = createAutoSaveIndicator();

    function showAutoSaveStatus(message, type = 'info') {
        const colors = {
            info: 'bg-blue-500 text-white',
            success: 'bg-green-500 text-white',
            error: 'bg-red-500 text-white',
            saving: 'bg-yellow-500 text-white'
        };

        autoSaveIndicator.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 z-50 ${colors[type]}`;
        autoSaveIndicator.textContent = message;
        autoSaveIndicator.style.transform = 'translateY(0)';
        autoSaveIndicator.style.opacity = '1';

        if (type !== 'saving') {
            setTimeout(() => {
                autoSaveIndicator.style.transform = 'translateY(100%)';
                autoSaveIndicator.style.opacity = '0';
            }, 3000);
        }
    }

    function collectFormData() {
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            // Handle array fields
            if (key.endsWith('[]')) {
                const arrayKey = key.slice(0, -2);
                if (!data[arrayKey]) {
                    data[arrayKey] = [];
                }
                data[arrayKey].push(value);
            } else {
                data[key] = value;
            }
        }

        return data;
    }



    // Track form changes
    if (form) {
        form.addEventListener('input', function() {
            isFormDirty = true;
        });
    }

    // Save as draft functionality
    if (saveDraftBtn) {
        saveDraftBtn.addEventListener('click', async function(e) {
            e.preventDefault();

            try {
                showAutoSaveStatus('Saving draft...', 'saving');

                // Save current form data as draft
                const data = collectFormData();
                
                const response = await fetch('{{ route('seller.courses.auto-save') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    // Then save as draft
                    const draftResponse = await fetch('{{ route('seller.courses.save-draft') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json'
                        }
                    });

                    const result = await draftResponse.json();

                    if (result.success) {
                        showAutoSaveStatus('Draft saved successfully!', 'success');
                        isFormDirty = false;

                        // Redirect after a short delay
                        setTimeout(() => {
                            window.location.href = result.redirect_url;
                        }, 1500);
                    } else {
                        showAutoSaveStatus('Failed to save draft: ' + result.message, 'error');
                    }
                } else {
                    showAutoSaveStatus('Failed to save draft', 'error');
                }
            } catch (error) {
                console.error('Save draft error:', error);
                showAutoSaveStatus('Failed to save draft', 'error');
            }
        });
    }

    // Handle form submission to clear dirty state
    if (form) {
        form.addEventListener('submit', function() {
            isFormDirty = false;
        });
    }

    // Form validation enhancements
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.classList.add('border-red-500');
            } else {
                this.classList.remove('border-red-500');
            }
        });
    });

    // Auto-save data restoration functions
    @if(isset($hasAutoSave) && $hasAutoSave)
    window.restoreAutoSavedData = function() {
        const autoSavedData = @json($autoSavedData['data']);

        // Populate form fields with auto-saved data
        Object.keys(autoSavedData).forEach(key => {
            const field = document.querySelector(`[name="${key}"]`);
            if (field && autoSavedData[key]) {
                field.value = autoSavedData[key];

                // Trigger change event for category cascade
                if (key === 'category_id' || key === 'subcategory_id' || key === 'detailed_category_id') {
                    field.dispatchEvent(new Event('change'));
                }
            }
        });

        // Update character counters
        if (shortDescTextarea && shortDescCounter) {
            updateCharacterCount(shortDescTextarea, shortDescCounter, 500);
        }
        if (descTextarea && descCounter) {
            updateCharacterCount(descTextarea, descCounter);
        }

        // Hide the notice
        document.getElementById('auto-save-notice').style.display = 'none';

        showAutoSaveStatus('Auto-saved data restored', 'success');
    };

    window.clearAutoSavedData = function() {
        // Clear auto-saved data from session
        fetch('{{ route('seller.courses.auto-save') }}', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        }).then(() => {
            document.getElementById('auto-save-notice').style.display = 'none';
            showAutoSaveStatus('Auto-saved data cleared', 'info');
        });
    };
    @endif

    // Dynamic field management functions
    window.addLearningOutcome = function() {
        const container = document.getElementById('what-you-will-learn-container');
        const newItem = document.createElement('div');
        newItem.className = 'what-you-will-learn-item flex items-center space-x-3';
        newItem.innerHTML = `
            <input type="text"
                   name="what_you_will_learn[]"
                   required
                   class="flex-1 block rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                   placeholder="e.g., Master the fundamentals of digital marketing">
            <button type="button"
                    onclick="removeItem(this)"
                    class="text-red-600 hover:text-red-800 p-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        `;
        container.appendChild(newItem);
        
        // Add event listener to new input for form change tracking
        const newInput = newItem.querySelector('input');
        if (newInput) {
            newInput.addEventListener('input', function() {
                isFormDirty = true;
            });
        }
    };

    window.addRequirement = function() {
        const container = document.getElementById('requirements-container');
        const newItem = document.createElement('div');
        newItem.className = 'requirements-item flex items-center space-x-3';
        newItem.innerHTML = `
            <input type="text"
                   name="requirements[]"
                   required
                   class="flex-1 block rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                   placeholder="e.g., Basic computer skills required">
            <button type="button"
                    onclick="removeItem(this)"
                    class="text-red-600 hover:text-red-800 p-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        `;
        container.appendChild(newItem);
        
        // Add event listener to new input for form change tracking
        const newInput = newItem.querySelector('input');
        if (newInput) {
            newInput.addEventListener('input', function() {
                isFormDirty = true;
            });
        }
    };

    window.addTargetAudience = function() {
        const container = document.getElementById('target-audience-container');
        const newItem = document.createElement('div');
        newItem.className = 'target-audience-item flex items-center space-x-3';
        newItem.innerHTML = `
            <input type="text"
                   name="target_audience[]"
                   required
                   class="flex-1 block rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                   placeholder="e.g., Business owners looking to grow their online presence">
            <button type="button"
                    onclick="removeItem(this)"
                    class="text-red-600 hover:text-red-800 p-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        `;
        container.appendChild(newItem);
        
        // Add event listener to new input for form change tracking
        const newInput = newItem.querySelector('input');
        if (newInput) {
            newInput.addEventListener('input', function() {
                isFormDirty = true;
            });
        }
    };

    window.removeItem = function(button) {
        const item = button.closest('.what-you-will-learn-item, .requirements-item, .target-audience-item');
        const container = item.parentNode;

        // Don't remove if it's the last item
        if (container.children.length > 1) {
            item.remove();
            isFormDirty = true;
        }
    };
});
</script>
@endpush
@endsection
