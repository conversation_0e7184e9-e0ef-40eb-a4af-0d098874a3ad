#!/bin/sh

# <PERSON>ript to obfuscate JavaScript files
# This script will obfuscate all JS files from public/dev-js to public/js

echo "Obfuscating JavaScript files..."

# Check if javascript-obfuscator is installed
if ! command -v javascript-obfuscator &> /dev/null; then
    echo "Error: javascript-obfuscator is not installed. Please install it with 'npm install -g javascript-obfuscator'"
    exit 1
fi

# Create the public/js directory if it doesn't exist
mkdir -p public/js

# Obfuscate all JS files from public/dev-js to public/js
javascript-obfuscator public/dev-js --output public/js --compact true --control-flow-flattening false --dead-code-injection false --debug-protection false --debug-protection-interval 0 --disable-console-output true --identifier-names-generator hexadecimal --log false --numbers-to-expressions false --rename-globals false --self-defending true --simplify true --split-strings false --string-array true --string-array-calls-transform false --string-array-encoding none --string-array-index-shift true --string-array-rotate true --string-array-shuffle true --string-array-wrappers-count 1 --string-array-wrappers-chained-calls true --string-array-wrappers-parameters-max-count 2 --string-array-wrappers-type variable --string-array-threshold 0.75 --unicode-escape-sequence false

# Check if the obfuscation was successful
if [ $? -ne 0 ]; then
    echo "Error: Failed to obfuscate JavaScript files"
    exit 1
fi

echo "JavaScript files successfully obfuscated"
exit 0
