<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_memberships', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignUuid('membership_tier_id')->references('id')->on('membership_tiers')->onUpdate('cascade')->onDelete('cascade');
            $table->timestamp('started_at');
            $table->timestamp('expires_at')->nullable(); // null for lifetime/free tiers
            $table->enum('status', ['active', 'expired', 'cancelled'])->default('active');
            $table->decimal('amount_paid', 10, 2)->default(0); // Amount paid for this membership
            $table->string('payment_method')->nullable(); // How they paid
            $table->string('transaction_id')->nullable(); // Payment transaction reference
            $table->timestamps();

            // Ensure one active membership per user (but allow multiple cancelled/expired)
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_memberships');
    }
};
