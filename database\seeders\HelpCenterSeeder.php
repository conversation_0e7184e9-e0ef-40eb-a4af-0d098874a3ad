<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\HelpCategory;
use App\Models\HelpArticle;
use App\Models\Faq;

class HelpCenterSeeder extends Seeder
{
    public function run()
    {

        // Seed Categories
        $categoriesData = [
            ['name' => 'Getting Started', 'icon' => 'rocket'],
            ['name' => 'Products', 'icon' => 'package'],
            ['name' => 'Orders & Customers', 'icon' => 'shopping-cart'],
            ['name' => 'Payments', 'icon' => 'credit-card'],
            ['name' => 'Marketing', 'icon' => 'trending-up'],
            ['name' => 'Account & Security', 'icon' => 'shield'],
        ];

        $categories = [];
        foreach ($categoriesData as $data) {
            $categories[] = HelpCategory::create($data);
        }

        // Seed Articles
        $articlesData = [
            // Getting Started articles
            [
                'category_id' => $categories[0]->id,
                'title' => 'Setting Up Your Seller Account',
                'slug' => 'setting-up-your-seller-account',
                'content' => '<p>Welcome to Digitora. This guide outlines the steps to set up your seller account and begin selling digital products on our platform.</p>
                            <h2>Step 1: Account Registration</h2>
                            <p>Visit the Digitora website and click "Sign Up". Complete the following steps:</p>
                            <ol>
                                <li>Enter your email address and create a secure password.</li>
                                <li>Complete the CAPTCHA verification.</li>
                                <li>Click "Register" and verify your email via the activation link sent to your inbox.</li>
                            </ol>
                            <p>Use a professional email address to establish credibility with buyers.</p>

                            <h2>Step 2: Complete Your Seller Profile</h2>
                            <p>Log in and navigate to your profile settings to provide the following details:</p>
                            <ul>
                                <li><strong>Full Name:</strong> Your legal name for identification purposes.</li>
                                <li><strong>Profile Picture:</strong> Upload a clear image (recommended size: 300x300px).</li>
                                <li><strong>Bio:</strong> Write a concise 50-100 word description of your business or expertise.</li>
                                <li><strong>Contact Information:</strong> Optionally add a phone number or secondary email.</li>
                            </ul>
                            <p>A complete profile enhances trust and visibility with potential buyers.</p>

                            <h2>Step 3: Configure Your Store</h2>
                            <p>Set up your store under Settings > Store Customization with the following:</p>
                            <ul>
                                <li><strong>Store Name:</strong> Choose a unique and memorable name for your store.</li>
                                <li><strong>Store Logo:</strong> Upload a high-resolution logo (500x500px, PNG or JPG).</li>
                                <li><strong>Store Description:</strong> Provide a 100-150 word description of your offerings.</li>
                                <li><strong>Store Category:</strong> Select the category that best aligns with your products.</li>
                            </ul>

                            <h2>Step 4: Set Up Payment Methods</h2>
                            <p>Configure your payment method to receive earnings:</p>
                            <ol>
                                <li>Go to Settings > Billing.</li>
                                <li>Select a payment method (e.g., PayPal or bank transfer).</li>
                                <li>Enter and verify your payment details.</li>
                            </ol>
                            <p>Payouts are processed on the 15th of each month for the previous month’s revenue, with a minimum threshold of Rp 500,000.</p>

                            <h2>Step 5: Review Platform Policies</h2>
                            <p>Familiarize yourself with our policies to ensure compliance:</p>
                            <ul>
                                <li><a href="/terms">Terms of Service</a></li>
                                <li><a href="/seller-guidelines">Seller Guidelines</a></li>
                                <li><a href="/acceptable-use">Acceptable Use Policy</a></li>
                                <li><a href="/digital-policy">Digital Product Policy</a></li>
                            </ul>
                            <p>Compliance with these policies ensures a seamless selling experience.</p>

                            <h2>Step 6: Publish Your First Product</h2>
                            <p>Navigate to Products > Add New to create your first product. Refer to the "Creating Your First Product" article for detailed instructions.</p>',
            ],
            [
                'category_id' => $categories[0]->id,
                'title' => 'Creating Your First Product',
                'slug' => 'creating-your-first-product',
                'content' => '<p>Follow these steps to create and publish your first product on Digitora.</p>
                            <h2>Step 1: Access the Products Section</h2>
                            <p>From your seller dashboard, go to Products and select "Add New Product."</p>
                            <h2>Step 2: Enter Product Details</h2>
                            <p>Provide the required information, including title, description, price, and category.</p>
                            <h2>Step 3: Upload Digital Files</h2>
                            <p>Upload your product files in supported formats as defined by our system, ensuring they do not exceed the system-defined maximum size.</p>
                            <h2>Step 4: Preview and Publish</h2>
                            <p>Review your product details and click "Publish" to make it available to buyers.</p>',
            ],
            [
                'category_id' => $categories[0]->id,
                'title' => 'Understanding the Seller Dashboard',
                'slug' => 'understanding-the-seller-dashboard',
                'content' => '<p>The seller dashboard is your central hub for managing your Digitora store. Below are the key sections available:</p>
                            <ul>
                                <li><strong>Dashboard:</strong> View an overview of your sales, revenue, and recent activity.</li>
                                <li><strong>Products:</strong> Manage your product listings, including adding, editing, or removing items.</li>
                                <li><strong>Orders:</strong> Track and manage customer orders and respond to inquiries.</li>
                                <li><strong>Analytics:</strong> Access detailed reports on sales, customer demographics, and product performance.</li>
                                <li><strong>Payments:</strong> Monitor your earnings and payout schedules.</li>
                                <li><strong>Settings:</strong> Configure your store, payment methods, and account preferences.</li>
                                <li><strong>User Dashboard:</strong> Manage your personal account details and settings.</li>
                                <li><strong>Help Center:</strong> Access support resources and guides.</li>
                                <li><strong>Documentation:</strong> Review platform documentation for advanced features.</li>
                            </ul>
                            <p>Use these sections to efficiently manage your selling activities and monitor your store’s performance.</p>',
            ],
            [
                'category_id' => $categories[0]->id,
                'title' => 'Best Practices for Product Listings',
                'slug' => 'best-practices-for-product-listings',
                'content' => '<p>Optimize your product listings to attract buyers with these best practices:</p>
                            <ul>
                                <li>Upload high-quality images that clearly represent your product.</li>
                                <li>Write detailed and accurate descriptions to set clear expectations.</li>
                                <li>Price your products competitively based on market trends.</li>
                                <li>Use relevant categories and tags to improve discoverability.</li>
                            </ul>',
            ],
            // Products articles
            [
                'category_id' => $categories[1]->id,
                'title' => 'Managing Your Product Inventory',
                'slug' => 'managing-your-product-inventory',
                'content' => '<p>Effectively manage your digital product inventory to ensure a smooth selling experience.</p>
                            <h2>Step 1: Access Your Product List</h2>
                            <p>Navigate to Products in your seller dashboard to view all your listings.</p>
                            <h2>Step 2: Edit or Update Products</h2>
                            <p>Select a product to update its details, files, or pricing as needed.</p>
                            <h2>Step 3: Archive or Delete Products</h2>
                            <p>Remove outdated or underperforming products by archiving or deleting them.</p>
                            <h2>Step 4: Monitor Stock Status</h2>
                            <p>For digital products with limited licenses, track availability and update as necessary.</p>',
            ],
            [
                'category_id' => $categories[1]->id,
                'title' => 'Supported File Formats and Size Limits',
                'slug' => 'supported-file-formats-and-size-limits',
                'content' => '<p>Digitora supports a range of digital file formats as defined by our system requirements. Ensure your files meet these standards:</p>
                            <ul>
                                <li>File formats must comply with system-supported types.</li>
                                <li>File sizes must not exceed the system-defined maximum limit.</li>
                            </ul>
                            <p>Check the Documentation section for a detailed list of supported formats and size restrictions.</p>',
            ],
            // Orders & Customers articles
            [
                'category_id' => $categories[2]->id,
                'title' => 'Handling Customer Inquiries',
                'slug' => 'handling-customer-inquiries',
                'content' => '<p>Respond to customer inquiries promptly to maintain satisfaction.</p>
                            <h2>Step 1: Access Inquiries</h2>
                            <p>Go to Orders in your dashboard to view customer messages.</p>
                            <h2>Step 2: Review and Respond</h2>
                            <p>Read the inquiry and provide a clear, professional response via the messaging system.</p>
                            <h2>Step 3: Follow Up</h2>
                            <p>Ensure the customer’s issue is resolved and follow up if necessary.</p>',
            ],
            [
                'category_id' => $categories[2]->id,
                'title' => 'Managing Customer Disputes',
                'slug' => 'managing-customer-disputes',
                'content' => '<p>Handle customer disputes professionally to maintain trust.</p>
                            <h2>Step 1: Review the Dispute</h2>
                            <p>Check the order details and customer message in the Orders section.</p>
                            <h2>Step 2: Communicate with the Customer</h2>
                            <p>Respond promptly, offering a resolution or clarification.</p>
                            <h2>Step 3: Escalate if Needed</h2>
                            <p>If unresolved, contact support via the Help Center to escalate the issue.</p>',
            ],
            // Payments articles
            [
                'category_id' => $categories[3]->id,
                'title' => 'Understanding Payment Schedules',
                'slug' => 'understanding-payment-schedules',
                'content' => '<p>Digitora processes payouts on the 15th of each month for the previous month’s revenue.</p>
                            <ul>
                                <li>A 5% platform fee is deducted after payment processing fees.</li>
                                <li>The minimum payout threshold is Rp 500,000.</li>
                            </ul>
                            <p>Ensure your payment method is configured in Settings > Billing to receive funds.</p>',
            ],
            [
                'category_id' => $categories[3]->id,
                'title' => 'Tracking Your Earnings',
                'slug' => 'tracking-your-earnings',
                'content' => '<p>Monitor your earnings and payout history efficiently.</p>
                            <h2>Step 1: Access Payments</h2>
                            <p>Navigate to Payments in your seller dashboard.</p>
                            <h2>Step 2: Review Earnings</h2>
                            <p>View your total revenue, deductions, and payout history.</p>
                            <h2>Step 3: Check Payout Status</h2>
                            <p>Track upcoming payouts and ensure your balance meets the minimum threshold.</p>',
            ],
            // Marketing articles
            [
                'category_id' => $categories[4]->id,
                'title' => 'Promoting Your Products',
                'slug' => 'promoting-your-products',
                'content' => '<p>Increase your product visibility with these promotional strategies.</p>
                            <ul>
                                <li>Leverage social media platforms to share your listings.</li>
                                <li>Use Digitora’s promotional tools to offer discounts or bundles.</li>
                                <li>Engage with customers through newsletters or updates.</li>
                            </ul>',
            ],
            [
                'category_id' => $categories[4]->id,
                'title' => 'Using Analytics to Boost Sales',
                'slug' => 'using-analytics-to-boost-sales',
                'content' => '<p>Use analytics to optimize your marketing efforts.</p>
                            <h2>Step 1: Access Analytics</h2>
                            <p>Go to Analytics in your dashboard to view performance data.</p>
                            <h2>Step 2: Analyze Trends</h2>
                            <p>Identify top-performing products and customer demographics.</p>
                            <h2>Step 3: Adjust Strategies</h2>
                            <p>Focus marketing efforts on high-demand products or regions.</p>',
            ],
            // Account & Security articles
            [
                'category_id' => $categories[5]->id,
                'title' => 'Securing Your Account',
                'slug' => 'securing-your-account',
                'content' => '<p>Protect your Digitora account with these security practices.</p>
                            <ul>
                                <li>Enable two-factor authentication in Settings > Security.</li>
                                <li>Use a strong, unique password and update it regularly.</li>
                                <li>Monitor account activity for unauthorized access.</li>
                            </ul>',
            ],
            [
                'category_id' => $categories[5]->id,
                'title' => 'Troubleshooting Account Issues',
                'slug' => 'troubleshooting-account-issues',
                'content' => '<p>Resolve common account issues with these steps.</p>
                            <h2>Step 1: Check Login Credentials</h2>
                            <p>Ensure your email and password are correct.</p>
                            <h2>Step 2: Reset Password</h2>
                            <p>Use the "Forgot Password" link if you cannot log in.</p>
                            <h2>Step 3: Contact Support</h2>
                            <p>If issues persist, reach out via the Help Center.</p>',
            ],
        ];

        $articles = [];
        foreach ($articlesData as $data) {
            $articles[] = HelpArticle::create($data);
        }

        // Seed Related Articles
        $articles[0]->relatedArticles()->attach([
            $articles[1]->id, // Creating Your First Product
            $articles[2]->id, // Understanding the Seller Dashboard
            $articles[3]->id, // Best Practices for Product Listings
        ]);

        // Seed FAQs
        $faqsData = [
            [
                'question' => 'How do I add a new product?',
                'answer' => 'Go to Products in your seller dashboard and click "Add Product". Fill in the required details, upload your files, set a price, and click "Publish" or "Save as Draft".',
            ],
            [
                'question' => 'When and how do I get paid?',
                'answer' => 'Payouts are processed on the 15th of each month for the previous month’s revenue, with a 5% platform fee deducted after payment processing fees. The minimum payout threshold is Rp 500,000. Configure your payment method in Settings > Billing.',
            ],
            [
                'question' => 'What file formats and size limits are supported?',
                'answer' => 'Digitora supports file formats as defined by our system requirements, with a maximum size limit also determined by the system. Refer to the Documentation section for details.',
            ],
            [
                'question' => 'How do I respond to customer inquiries?',
                'answer' => 'Navigate to Orders in your dashboard, locate the customer’s message, and reply using the messaging system. Respond promptly to maintain customer satisfaction.',
            ],
            [
                'question' => 'What fees does Digitora charge?',
                'answer' => 'A 5% platform fee is charged on each sale after payment processing fees. There are no monthly or listing fees.',
            ],
            [
                'question' => 'What is the refund policy for digital products?',
                'answer' => 'Due to the nature of digital products, refunds are not offered once a product is purchased and downloaded. Customers should review product descriptions carefully before buying.',
            ],
            [
                'question' => 'How can I increase my sales on Digitora?',
                'answer' => 'Optimize your listings with high-quality images and detailed descriptions, price competitively, and use promotional tools like discounts. Leverage analytics to target high-demand products and engage with customers via social media.',
            ],
            [
                'question' => 'How do I track my store’s performance?',
                'answer' => 'Use the Analytics section in your dashboard to view sales, revenue, popular products, and customer demographics. Use these insights to refine your selling strategy.',
            ],
            [
                'question' => 'Can I sell physical products on Digitora?',
                'answer' => 'Currently, Digitora focuses primarily on digital products. However, we are exploring options to support physical products in the future. Stay tuned for announcements as we expand our platform capabilities to include shipping and handling features for physical goods.',
            ],
            [
                'question' => 'What should I do if I encounter a login issue?',
                'answer' => 'Verify your login credentials, reset your password if needed using the "Forgot Password" link, or contact support via the Help Center if the issue persists.',
            ],
        ];

        foreach ($faqsData as $data) {
            Faq::create($data);
        }
    }
}