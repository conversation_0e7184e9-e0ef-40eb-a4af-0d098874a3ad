/**
 * Category Filter Script for Browse Page
 * Handles dynamic filtering of subcategories and detailed categories
 * based on selected parent categories
 */
document.addEventListener("DOMContentLoaded", function () {
    // Get filter elements
    const categorySelect = document.getElementById("category");
    const subcategorySelect = document.getElementById("subcategory");
    const detailedCategorySelect = document.getElementById("detailed_category");
    const clearFiltersBtn = document.querySelector('.md\\:col-span-3 a[href*="user.browse"]') ||
                            document.querySelector('a[href*="user.browse"]');

    if (!categorySelect || !subcategorySelect || !detailedCategorySelect) {
        return; // Exit if any element is not found (not on browse page)
    }

    // Initialize the filters on page load
    initializeFilters();

    // Add event listeners for category changes
    categorySelect.addEventListener("change", function () {
        filterSubcategories();
        filterDetailedCategories();
    });

    subcategorySelect.addEventListener("change", function () {
        filterDetailedCategories();
    });

    // Add animation to clear filters button if it exists
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener("mouseenter", function() {
            this.classList.add("animate-pulse");
        });

        clearFiltersBtn.addEventListener("mouseleave", function() {
            this.classList.remove("animate-pulse");
        });
    }

    /**
     * Initialize filters on page load
     */
    function initializeFilters() {
        // Hide subcategory options that don't match the selected category
        filterSubcategories();

        // Hide detailed category options that don't match the selected subcategory
        filterDetailedCategories();
    }

    /**
     * Filter subcategories based on selected category
     */
    function filterSubcategories() {
        const selectedCategory = categorySelect.value;

        // Show all subcategories if no category is selected
        if (!selectedCategory) {
            showAllOptions(subcategorySelect);
            return;
        }

        // Hide subcategories that don't match the selected category
        Array.from(subcategorySelect.options).forEach(option => {
            if (option.value === "") {
                // Always show the "All Subcategories" option
                option.style.display = "";
            } else {
                const categorySlug = option.getAttribute("data-category");
                option.style.display = (categorySlug === selectedCategory) ? "" : "none";
            }
        });

        // If the currently selected subcategory is now hidden, reset to "All Subcategories"
        if (subcategorySelect.selectedOptions[0].style.display === "none") {
            subcategorySelect.value = "";
        }
    }

    /**
     * Filter detailed categories based on selected subcategory
     */
    function filterDetailedCategories() {
        const selectedSubcategory = subcategorySelect.value;

        // Show all detailed categories if no subcategory is selected
        if (!selectedSubcategory) {
            showAllOptions(detailedCategorySelect);
            return;
        }

        // Hide detailed categories that don't match the selected subcategory
        Array.from(detailedCategorySelect.options).forEach(option => {
            if (option.value === "") {
                // Always show the "All Detailed Categories" option
                option.style.display = "";
            } else {
                const subcategorySlug = option.getAttribute("data-subcategory");
                option.style.display = (subcategorySlug === selectedSubcategory) ? "" : "none";
            }
        });

        // If the currently selected detailed category is now hidden, reset to "All Detailed Categories"
        if (detailedCategorySelect.selectedOptions[0].style.display === "none") {
            detailedCategorySelect.value = "";
        }
    }

    /**
     * Show all options in a select element
     */
    function showAllOptions(selectElement) {
        Array.from(selectElement.options).forEach(option => {
            option.style.display = "";
        });
    }
});
