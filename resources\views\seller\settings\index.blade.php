@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div>
        <h1 class="text-3xl font-bold tracking-tight">Settings</h1>
        <p class="text-gray-500">Manage your account and store settings</p>
    </div>

    @if(session('success'))
    <div class="rounded-md bg-green-50 p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
            </div>
        </div>
    </div>
    @endif

    <div class="grid gap-6 md:grid-cols-5">
        <div class="md:col-span-1">
            <div class="sticky top-6">
                <nav class="space-y-1" aria-label="Settings" x-data="{ activeSection: 'profile' }">
                    <a href="#profile" @click="activeSection = 'profile'" :class="{ 'bg-indigo-50 text-indigo-700': activeSection === 'profile', 'text-gray-700 hover:bg-gray-50 hover:text-gray-900': activeSection !== 'profile' }" class="flex items-center rounded-md px-3 py-2 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" :class="{ 'text-indigo-500': activeSection === 'profile', 'text-gray-400': activeSection !== 'profile' }" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <span>Profile</span>
                    </a>
                    <a href="#store" @click="activeSection = 'store'" :class="{ 'bg-indigo-50 text-indigo-700': activeSection === 'store', 'text-gray-700 hover:bg-gray-50 hover:text-gray-900': activeSection !== 'store' }" class="flex items-center rounded-md px-3 py-2 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" :class="{ 'text-indigo-500': activeSection === 'store', 'text-gray-400': activeSection !== 'store' }" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                        <span>Store</span>
                    </a>
                    <a href="#security" @click="activeSection = 'security'" :class="{ 'bg-indigo-50 text-indigo-700': activeSection === 'security', 'text-gray-700 hover:bg-gray-50 hover:text-gray-900': activeSection !== 'security' }" class="flex items-center rounded-md px-3 py-2 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" :class="{ 'text-indigo-500': activeSection === 'security', 'text-gray-400': activeSection !== 'security' }" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        <span>Security</span>
                    </a>
                    <a href="#notifications" @click="activeSection = 'notifications'" :class="{ 'bg-indigo-50 text-indigo-700': activeSection === 'notifications', 'text-gray-700 hover:bg-gray-50 hover:text-gray-900': activeSection !== 'notifications' }" class="flex items-center rounded-md px-3 py-2 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" :class="{ 'text-indigo-500': activeSection === 'notifications', 'text-gray-400': activeSection !== 'notifications' }" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                        <span>Notifications</span>
                    </a>
                    <a href="#billing" @click="activeSection = 'billing'" :class="{ 'bg-indigo-50 text-indigo-700': activeSection === 'billing', 'text-gray-700 hover:bg-gray-50 hover:text-gray-900': activeSection !== 'billing' }" class="flex items-center rounded-md px-3 py-2 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" :class="{ 'text-indigo-500': activeSection === 'billing', 'text-gray-400': activeSection !== 'billing' }" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                        </svg>
                        <span>Billing</span>
                    </a>
                </nav>
            </div>
        </div>
        <div class="md:col-span-4 space-y-6">
            <!-- Profile Settings -->
            <div id="profile" class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Profile Information</h3>
                    <p class="text-sm text-gray-500">Update your personal information and profile picture</p>
                </div>
                <div class="p-6">
                    @if ($errors->hasAny(['name', 'email', 'phone', 'bio', 'avatar']))
                        <div class="mb-4 rounded-md bg-red-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">There were some errors with your submission</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc pl-5 space-y-1">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('seller.settings.updateProfile') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="space-y-6">
                            <div class="flex items-center gap-6">
                                <div class="h-20 w-20 flex-shrink-0 overflow-hidden rounded-full">
                                    <img src="{{ $user->avatar ? asset('storage/' . $user->avatar) : asset('images/default-avatar.png') }}" alt="{{ $user->name }}" class="h-full w-full object-cover">
                                </div>
                                <div>
                                    <label for="avatar" class="block text-sm font-medium text-gray-700">Profile Picture</label>
                                    <div class="mt-1 flex items-center">
                                        <input type="file" id="avatar" name="avatar" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">JPG, PNG or GIF. Max size 2MB.</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                                    <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                    <div class="relative">
                                        <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm cursor-not-allowed" readonly>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none mt-1">
                                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">Email address cannot be changed as it's used for account identification.</p>
                                </div>
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                                    <input type="text" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                </div>
                            </div>

                            <div>
                                <label for="bio" class="block text-sm font-medium text-gray-700">Bio</label>
                                <textarea id="bio" name="bio" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">{{ old('bio', $user->bio) }}</textarea>
                                <p class="mt-1 text-xs text-gray-500">Brief description for your profile. URLs are hyperlinked.</p>
                            </div>

                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700">
                                    Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Store Settings -->
            <div id="store" class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Store Information</h3>
                    <p class="text-sm text-gray-500">Update your store details and branding</p>
                </div>
                <div class="p-6">
                    @if (!$sellerApplication)
                        <div class="rounded-md bg-yellow-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">Seller Application Not Found</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>You need to complete your seller application before you can update your store information.</p>
                                    </div>
                                    <div class="mt-4">
                                        <div class="-mx-2 -my-1.5 flex">
                                            <a href="{{ route('seller.apply') }}" class="rounded-md bg-yellow-50 px-2 py-1.5 text-sm font-medium text-yellow-800 hover:bg-yellow-100">Apply as a Seller</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @elseif ($errors->hasAny(['store_name', 'store_description', 'store_logo']))
                        <div class="mb-4 rounded-md bg-red-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">There were some errors with your submission</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc pl-5 space-y-1">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if ($sellerApplication)
                        <form action="{{ route('seller.settings.updateStore') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="space-y-6">
                                <div class="flex items-center gap-6">
                                    <div class="h-20 w-20 flex-shrink-0 overflow-hidden rounded-md">
                                        <img src="{{ $sellerApplication->store_logo ? route('store.logo', $sellerApplication->store_name_slug) : asset('images/default-logo.png') }}" alt="{{ $sellerApplication->store_name ?? 'Store Logo' }}" class="h-full w-full object-cover">
                                    </div>
                                    <div>
                                        <label for="store_logo" class="block text-sm font-medium text-gray-700">Store Logo</label>
                                        <div class="mt-1 flex items-center">
                                            <input type="file" id="store_logo" name="store_logo" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                        </div>
                                        <p class="mt-1 text-xs text-gray-500">JPG, PNG or GIF. Max size 2MB.</p>
                                    </div>
                                </div>

                                <div>
                                    <label for="store_name" class="block text-sm font-medium text-gray-700">Store Name</label>
                                    <input type="text" id="store_name" name="store_name" value="{{ old('store_name', $sellerApplication->store_name) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                </div>

                                <div>
                                    <label for="store_description" class="block text-sm font-medium text-gray-700">Store Description</label>
                                    <textarea id="store_description" name="store_description" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">{{ old('store_description', $sellerApplication->store_description) }}</textarea>
                                    <p class="mt-1 text-xs text-gray-500">Describe what you sell and what makes your products unique.</p>
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700">
                                        Save Changes
                                    </button>
                                </div>
                            </div>
                        </form>
                    @endif
                </div>
            </div>

            <!-- Security Settings -->
            <div id="security" class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Security</h3>
                    <p class="text-sm text-gray-500">Update your password and security settings</p>
                </div>
                <div class="p-6">
                    @if ($errors->hasAny(['password', 'password_confirmation']))
                        <div class="mb-4 rounded-md bg-red-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">There were some errors with your submission</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc pl-5 space-y-1">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('seller.settings.updatePassword') }}" method="POST">
                        @csrf
                        <div class="space-y-6">
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700">New Password</label>
                                <input type="password" id="password" name="password" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                                <input type="password" id="password_confirmation" name="password_confirmation" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>

                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700">
                                    Update Password
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Notification Settings -->
            <div id="notifications" class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Notification Preferences</h3>
                    <p class="text-sm text-gray-500">Manage how you receive notifications</p>
                </div>
                <div class="p-6">
                    @if ($errors->hasAny(['notification_order', 'notification_payment', 'notification_product', 'notification_marketing']))
                        <div class="mb-4 rounded-md bg-red-50 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">There were some errors with your submission</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc pl-5 space-y-1">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <form action="{{ route('seller.settings.updateNotifications') }}" method="POST">
                        @csrf
                        <div class="space-y-6">
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex h-5 items-center">
                                        <input id="notification_order" name="notification_order" type="checkbox" {{ $user->notification_order ? 'checked' : '' }} class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="notification_order" class="font-medium text-gray-700">Order Notifications</label>
                                        <p class="text-gray-500">Receive notifications when you get new orders or order status changes.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex h-5 items-center">
                                        <input id="notification_payment" name="notification_payment" type="checkbox" {{ $user->notification_payment ? 'checked' : '' }} class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="notification_payment" class="font-medium text-gray-700">Payment Notifications</label>
                                        <p class="text-gray-500">Receive notifications about payments and payouts.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex h-5 items-center">
                                        <input id="notification_product" name="notification_product" type="checkbox" {{ $user->notification_product ? 'checked' : '' }} class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="notification_product" class="font-medium text-gray-700">Product Notifications</label>
                                        <p class="text-gray-500">Receive notifications about product reviews and updates.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex h-5 items-center">
                                        <input id="notification_marketing" name="notification_marketing" type="checkbox" {{ $user->notification_marketing ? 'checked' : '' }} class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="notification_marketing" class="font-medium text-gray-700">Marketing Notifications</label>
                                        <p class="text-gray-500">Receive marketing and promotional emails from us.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end">
                                <button type="submit" class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700">
                                    Save Preferences
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Billing Settings -->
            <div id="billing" class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Billing Information</h3>
                    <p class="text-sm text-gray-500">Manage your billing details and subscription</p>
                </div>
                <div class="p-6">
                    <div class="rounded-md bg-indigo-50 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-indigo-800">Current Plan: Free</h3>
                                <div class="mt-2 text-sm text-indigo-700">
                                    <p>You are currently on the free plan. Upgrade to access more features and increase your selling limits.</p>
                                </div>
                                <div class="mt-4">
                                    <div class="-mx-2 -my-1.5 flex">
                                        <a href="{{ route('seller.settings') }}" class="rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-indigo-700">Upgrade Plan</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-900">Payment Method</h4>
                        <p class="mt-1 text-sm text-gray-500">No payment method added yet.</p>
                        <div class="mt-4">
                            <button type="button" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium leading-4 text-gray-700 shadow-sm hover:bg-gray-50" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4" />
                                </svg>
                                Add Payment Method (Coming Soon)
                            </button>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-900">Billing History</h4>
                        <p class="mt-1 text-sm text-gray-500">You have no billing history yet.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
