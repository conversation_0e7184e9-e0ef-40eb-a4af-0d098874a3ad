<?php

namespace App\Services;

use App\Models\Order;
use App\Models\User;
use App\Models\MembershipTier;

class FeeCalculationService
{
    /**
     * Platform base fee percentage (applied to all transactions)
     */
    const PLATFORM_BASE_FEE = 5;

    /**
     * Minimum payout threshold in IDR
     */
    const MINIMUM_PAYOUT_THRESHOLD = 500000;

    /**
     * Calculate seller revenue after all fees for an order
     *
     * @param Order $order
     * @return array
     */
    public function calculateSellerRevenue(Order $order): array
    {
        $grossAmount = $order->amount;
        $seller = $order->seller;
        
        if (!$seller) {
            throw new \Exception('Order must have a seller to calculate revenue');
        }

        $membershipTier = $seller->getCurrentMembershipTier();
        
        // Calculate platform base fee (5%)
        $platformBaseFee = $grossAmount * (self::PLATFORM_BASE_FEE / 100);
        
        // Calculate membership-based transaction fee
        $transactionFeeRate = $this->getTransactionFeeRate($order, $membershipTier);
        $transactionFee = $grossAmount * ($transactionFeeRate / 100);
        
        // Calculate total fees and net amount
        $totalFees = $platformBaseFee + $transactionFee;
        $netAmount = $grossAmount - $totalFees;
        
        return [
            'gross_amount' => $grossAmount,
            'platform_base_fee' => $platformBaseFee,
            'platform_base_fee_rate' => self::PLATFORM_BASE_FEE,
            'transaction_fee' => $transactionFee,
            'transaction_fee_rate' => $transactionFeeRate,
            'total_fees' => $totalFees,
            'net_amount' => $netAmount,
            'order_type' => $order->isCourseOrder() ? 'course' : 'product',
            'membership_tier' => $membershipTier ? $membershipTier->slug : 'starter',
        ];
    }

    /**
     * Get the appropriate transaction fee rate based on order type and membership tier
     *
     * @param Order $order
     * @param MembershipTier|null $membershipTier
     * @return int
     */
    public function getTransactionFeeRate(Order $order, ?MembershipTier $membershipTier = null): int
    {
        // Default to starter tier if no membership tier found
        if (!$membershipTier) {
            $membershipTier = MembershipTier::where('slug', 'starter')->first();
        }

        if ($order->isCourseOrder()) {
            return $membershipTier->course_transaction_fee ?? 10;
        } else {
            return $membershipTier->digital_product_transaction_fee ?? 5;
        }
    }

    /**
     * Calculate total seller revenue for multiple orders
     *
     * @param \Illuminate\Database\Eloquent\Collection $orders
     * @return array
     */
    public function calculateTotalSellerRevenue($orders): array
    {
        $totalGross = 0;
        $totalPlatformFees = 0;
        $totalTransactionFees = 0;
        $totalNet = 0;
        $orderCount = 0;

        foreach ($orders as $order) {
            $revenue = $this->calculateSellerRevenue($order);
            $totalGross += $revenue['gross_amount'];
            $totalPlatformFees += $revenue['platform_base_fee'];
            $totalTransactionFees += $revenue['transaction_fee'];
            $totalNet += $revenue['net_amount'];
            $orderCount++;
        }

        return [
            'total_gross_amount' => $totalGross,
            'total_platform_fees' => $totalPlatformFees,
            'total_transaction_fees' => $totalTransactionFees,
            'total_fees' => $totalPlatformFees + $totalTransactionFees,
            'total_net_amount' => $totalNet,
            'order_count' => $orderCount,
            'meets_minimum_payout' => $totalNet >= self::MINIMUM_PAYOUT_THRESHOLD,
            'minimum_payout_threshold' => self::MINIMUM_PAYOUT_THRESHOLD,
        ];
    }

    /**
     * Get fee structure for a specific membership tier
     *
     * @param MembershipTier $tier
     * @return array
     */
    public function getFeeStructure(MembershipTier $tier): array
    {
        return [
            'platform_base_fee' => self::PLATFORM_BASE_FEE,
            'digital_product_transaction_fee' => $tier->digital_product_transaction_fee,
            'course_transaction_fee' => $tier->course_transaction_fee,
            'tier_name' => $tier->name,
            'tier_slug' => $tier->slug,
        ];
    }

    /**
     * Get all fee structures for display purposes
     *
     * @return array
     */
    public function getAllFeeStructures(): array
    {
        $tiers = MembershipTier::active()->orderBy('sort_order')->get();
        $structures = [];

        foreach ($tiers as $tier) {
            $structures[] = $this->getFeeStructure($tier);
        }

        return $structures;
    }

    /**
     * Format currency for display
     *
     * @param float $amount
     * @return string
     */
    public function formatCurrency(float $amount): string
    {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }
}
