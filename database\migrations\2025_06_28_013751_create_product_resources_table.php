<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_resources', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->references('id')->on('products')->onUpdate('cascade')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['file', 'link', 'pdf', 'text', 'video', 'audio', 'document', 'image']);
            $table->text('content')->nullable(); // File path or URL content
            $table->json('metadata')->nullable(); // File metadata (size, mime_type, etc.)
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_preview')->default(false); // Whether this resource is available as preview
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['product_id', 'is_active']);
            $table->index(['product_id', 'is_preview']);
            $table->index(['product_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_resources');
    }
};
