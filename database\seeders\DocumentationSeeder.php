<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\DocumentationSection;
use App\Models\DocumentationTopic;
use App\Models\VideoTutorial;

class DocumentationSeeder extends Seeder
{
    public function run()
    {
        // Seed Documentation Sections
        $sectionsData = [
            [
                'title' => 'Getting Started',
                'icon' => 'rocket',
                'description' => 'Learn the basics of using the Digitora seller platform.',
            ],
            [
                'title' => 'Product Management',
                'icon' => 'package',
                'description' => 'Create and manage your digital products effectively.',
            ],
            [
                'title' => 'Order Management',
                'icon' => 'shopping-cart',
                'description' => 'Process and manage customer orders efficiently.',
            ],
            [
                'title' => 'Payments',
                'icon' => 'credit-card',
                'description' => 'Understand payment processing and payout schedules.',
            ],
            [
                'title' => 'Analytics & Reporting',
                'icon' => 'chart-bar',
                'description' => 'Track performance and generate insightful reports.',
            ],
            [
                'title' => 'Account Settings',
                'icon' => 'cog',
                'description' => 'Manage your account and store settings.',
            ],
        ];

        $sections = [];
        foreach ($sectionsData as $data) {
            $sections[$data['title']] = DocumentationSection::create($data);
        }

        // Seed Documentation Topics
        $topicsData = [
            // Getting Started
            [
                'section_title' => 'Getting Started',
                'title' => 'Platform Overview',
                'slug' => 'platform-overview',
                'content' => '<p class="lead">Digitora is a platform for selling digital products, designed to help creators and entrepreneurs reach a global audience.</p>
                            <h2>What is Digitora?</h2>
                            <p>Digitora enables sellers to list, manage, and sell digital products such as templates, graphics, and educational content. Key features include:</p>
                            <ul>
                                <li>Easy product listing and management tools.</li>
                                <li>Secure payment processing with payouts on the 15th of each month for the previous month’s revenue.</li>
                                <li>Analytics to track sales and customer behavior.</li>
                                <li>Support for system-defined file formats with a maximum size limit.</li>
                            </ul>
                            <h2>Who Can Use Digitora?</h2>
                            <p>Digitora is ideal for:</p>
                            <ul>
                                <li>Digital creators (e.g., designers, writers, educators).</li>
                                <li>Small businesses offering digital goods.</li>
                                <li>Freelancers looking to monetize their work.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Proceed to the "Account Setup" topic to get started, or explore other sections for detailed guides on product management and payments.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Getting Started',
                'title' => 'Account Setup',
                'slug' => 'account-setup',
                'content' => '<p class="lead">Set up your Digitora seller account to start selling digital products.</p>
                            <h2>Step 1: Register Your Account</h2>
                            <p>Visit the Digitora website and click "Sign Up". Complete the following:</p>
                            <ol>
                                <li>Enter your email address and create a secure password.</li>
                                <li>Complete the CAPTCHA verification.</li>
                                <li>Click "Register" and verify your email via the activation link.</li>
                            </ol>
                            <h2>Step 2: Complete Your Profile</h2>
                            <p>Log in and navigate to Settings > Profile to add:</p>
                            <ul>
                                <li>Full Name: Your legal name for identification.</li>
                                <li>Profile Picture: A clear image (recommended size: 300x300px).</li>
                                <li>Bio: A 50-100 word description of your business or expertise.</li>
                            </ul>
                            <h2>Step 3: Configure Your Store</h2>
                            <p>Go to Settings > Store Customization to set up:</p>
                            <ul>
                                <li>Store Name: A unique name for your store.</li>
                                <li>Store Logo: A high-resolution logo (500x500px, PNG or JPG).</li>
                                <li>Store Description: A 100-150 word overview of your offerings.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Refer to the "Dashboard Navigation" topic to explore the seller dashboard, or proceed to "Creating Products" in Product Management.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Getting Started',
                'title' => 'Dashboard Navigation',
                'slug' => 'dashboard-navigation',
                'content' => '<p class="lead">Learn how to navigate the Digitora seller dashboard to manage your store.</p>
                            <h2>Dashboard Sections</h2>
                            <p>The seller dashboard includes the following sections:</p>
                            <ul>
                                <li><strong>Dashboard:</strong> Overview of sales and recent activity.</li>
                                <li><strong>Products:</strong> Manage your product listings.</li>
                                <li><strong>Orders:</strong> Track and manage customer orders.</li>
                                <li><strong>Analytics:</strong> View sales and performance reports.</li>
                                <li><strong>Payments:</strong> Monitor earnings and payouts.</li>
                                <li><strong>Settings:</strong> Configure your account and store.</li>
                                <li><strong>User Dashboard:</strong> Manage personal account details.</li>
                                <li><strong>Help Center:</strong> Access support resources.</li>
                                <li><strong>Documentation:</strong> Explore detailed guides.</li>
                            </ul>
                            <h2>Navigation Tips</h2>
                            <ul>
                                <li>Use the sidebar to switch between sections quickly.</li>
                                <li>Check notifications for updates on orders or inquiries.</li>
                                <li>Bookmark frequently used sections for faster access.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Visit the "Account Setup" topic to configure your account, or explore "Creating Products" in Product Management.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Getting Started',
                'title' => 'Seller Guidelines',
                'slug' => 'seller-guidelines',
                'content' => '<p class="lead">Understand Digitora’s seller guidelines to ensure compliance and success.</p>
                            <h2>Key Policies</h2>
                            <ul>
                                <li>Only digital products are allowed; physical goods are not permitted.</li>
                                <li>All products must comply with intellectual property laws.</li>
                                <li>Product descriptions must be accurate and not misleading.</li>
                                <li>Respond to customer inquiries within 48 hours.</li>
                            </ul>
                            <h2>Platform Fees</h2>
                            <p>A 5% platform fee is deducted after payment processing fees on each sale. There are no listing or monthly fees.</p>
                            <h2>Consequences of Non-Compliance</h2>
                            <ul>
                                <li>Product removal for guideline violations.</li>
                                <li>Account suspension for repeated issues.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Review the "Account Setup" topic to get started, or refer to "Creating Products" in Product Management.</p>',
                'last_updated' => '2025-05-04',
            ],
            // Product Management
            [
                'section_title' => 'Product Management',
                'title' => 'Creating Products',
                'slug' => 'creating-products',
                'content' => '<p class="lead">This guide walks you through creating and publishing digital products on Digitora.</p>
                            <h2>Overview</h2>
                            <p>The process includes:</p>
                            <ol>
                                <li>Preparing your digital files.</li>
                                <li>Creating a product listing.</li>
                                <li>Setting pricing.</li>
                                <li>Adding visuals.</li>
                                <li>Publishing your product.</li>
                            </ol>
                            <h2>Step 1: Preparing Your Digital Files</h2>
                            <p>Ensure your files are ready:</p>
                            <ul>
                                <li>Verify files are in their final version.</li>
                                <li>Organize files logically for customer use.</li>
                                <li>Ensure files meet system-defined format and size limits (maximum 100MB).</li>
                                <li>Include a README file with usage instructions if applicable.</li>
                            </ul>
                            <div class="tip"><strong>Tip:</strong> For products with multiple files, create a ZIP archive for easier downloading.</div>
                            <h2>Step 2: Creating a Product Listing</h2>
                            <p>Follow these steps:</p>
                            <ol>
                                <li>Go to Products in your dashboard.</li>
                                <li>Click "Add Product".</li>
                                <li>Enter a descriptive product name.</li>
                                <li>Write a detailed description of what the product includes and its benefits.</li>
                                <li>Select an appropriate category.</li>
                            </ol>
                            <div class="note"><strong>Note:</strong> A well-chosen name and category improve search visibility.</div>
                            <h2>Step 3: Setting Pricing</h2>
                            <p>Set your pricing strategy:</p>
                            <ol>
                                <li>Enter the regular price.</li>
                                <li>Optionally enable a discount price for promotions.</li>
                                <li>Consider offering bundles for related products.</li>
                            </ol>
                            <div class="warning"><strong>Important:</strong> Digitora deducts a 5% platform fee after payment processing fees. Factor this into your pricing.</div>
                            <h2>Step 4: Adding Product Images</h2>
                            <p>Add visuals to attract buyers:</p>
                            <ul>
                                <li>Upload a main image (recommended size: 1200x800px).</li>
                                <li>Include additional screenshots or previews.</li>
                                <li>Optionally provide a demo file for customers to preview.</li>
                            </ul>
                            <div class="tip"><strong>Tip:</strong> For design assets, show both the raw file and its application in use.</div>
                            <h2>Step 5: Uploading Your Files</h2>
                            <p>Upload the product files:</p>
                            <ol>
                                <li>In the "Product Files" section, click "Choose Files" or drag and drop.</li>
                                <li>Wait for the upload to complete.</li>
                                <li>Verify all files uploaded correctly.</li>
                            </ol>
                            <div class="note"><strong>Note:</strong> These files are what customers receive upon purchase. Ensure they are complete.</div>
                            <h2>Step 6: Publishing Your Product</h2>
                            <p>Review and publish:</p>
                            <ol>
                                <li>Check all details for accuracy.</li>
                                <li>Choose to publish immediately or save as a draft.</li>
                                <li>Click "Publish Product" to make it live.</li>
                            </ol>
                            <div class="success"><strong>Success:</strong> Your product is now available in the marketplace!</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Provide detailed information about the product.</li>
                                <li>Use high-quality images to showcase your product.</li>
                                <li>Price competitively by researching similar products.</li>
                                <li>Update products regularly to keep them relevant.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Explore "Product Categories" to optimize your listings, or visit "Promoting Your Products" in the Help Center.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Product Management',
                'title' => 'Product Categories',
                'slug' => 'product-categories',
                'content' => '<p class="lead">Choose the right categories to improve your product’s visibility on Digitora.</p>
                            <h2>Why Categories Matter</h2>
                            <p>Categories help customers find your products through search and filters. Selecting the correct category:</p>
                            <ul>
                                <li>Improves discoverability.</li>
                                <li>Ensures your product reaches the right audience.</li>
                                <li>Enhances the customer shopping experience.</li>
                            </ul>
                            <h2>Selecting a Category</h2>
                            <p>When creating a product:</p>
                            <ol>
                                <li>Choose a primary category that best describes your product (e.g., Graphics, Education).</li>
                                <li>Optionally add subcategories for more specificity.</li>
                                <li>Avoid using overly broad or unrelated categories.</li>
                            </ol>
                            <div class="tip"><strong>Tip:</strong> Review top-selling products in your category to understand customer preferences.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Be specific to match customer expectations.</li>
                                <li>Update categories if your product evolves.</li>
                                <li>Use tags for additional discoverability.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Refer to "Creating Products" for listing instructions, or explore "Product Analytics" to track performance.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Product Management',
                'title' => 'Pricing Strategies',
                'slug' => 'pricing-strategies',
                'content' => '<p class="lead">Learn how to price your digital products effectively on Digitora.</p>
                            <h2>Factors to Consider</h2>
                            <p>When setting prices, consider:</p>
                            <ul>
                                <li><strong>Market Rates:</strong> Research similar products to ensure competitive pricing.</li>
                                <li><strong>Platform Fees:</strong> A 5% fee is deducted after payment processing fees.</li>
                                <li><strong>Value:</strong> Reflect the time, effort, and value your product provides.</li>
                            </ul>
                            <h2>Pricing Options</h2>
                            <p>Digitora offers flexible pricing settings:</p>
                            <ol>
                                <li>Set a regular price for your product.</li>
                                <li>Enable a discount price for limited-time promotions.</li>
                                <li>Create bundles to offer multiple products at a reduced rate.</li>
                            </ol>
                            <div class="warning"><strong>Important:</strong> Ensure your pricing covers the platform fee while remaining attractive to buyers.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Start with a competitive price to attract initial buyers.</li>
                                <li>Use discounts strategically during sales events.</li>
                                <li>Monitor analytics to adjust pricing based on demand.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Visit "Creating Products" to set up your listings, or explore "Product Analytics" to monitor sales performance.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Product Management',
                'title' => 'Product Analytics',
                'slug' => 'product-analytics',
                'content' => '<p class="lead">Track your product performance to optimize sales on Digitora.</p>
                            <h2>Accessing Analytics</h2>
                            <p>Navigate to Analytics in your dashboard to view:</p>
                            <ul>
                                <li>Total sales and revenue per product.</li>
                                <li>Customer demographics and purchase trends.</li>
                                <li>Conversion rates and product views.</li>
                            </ul>
                            <h2>Using Analytics</h2>
                            <p>Leverage data to improve your strategy:</p>
                            <ol>
                                <li>Identify top-performing products to focus marketing efforts.</li>
                                <li>Analyze customer demographics to target specific audiences.</li>
                                <li>Track conversion rates to optimize product listings.</li>
                            </ol>
                            <div class="tip"><strong>Tip:</strong> Regularly review analytics to spot trends and adjust your approach.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Compare product performance to identify underperforming listings.</li>
                                <li>Use data to inform pricing adjustments.</li>
                                <li>Monitor seasonal trends to plan promotions.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Explore "Pricing Strategies" to optimize your pricing, or visit "Analytics & Reporting" for advanced insights.</p>',
                'last_updated' => '2025-05-04',
            ],
            // Order Management
            [
                'section_title' => 'Order Management',
                'title' => 'Processing Orders',
                'slug' => 'processing-orders',
                'content' => '<p class="lead">Learn how to process and manage customer orders on Digitora.</p>
                            <h2>Order Workflow</h2>
                            <p>Orders follow this process:</p>
                            <ol>
                                <li>Customer purchases a product.</li>
                                <li>Order appears in your Orders section.</li>
                                <li>Customer receives automatic access to digital files.</li>
                                <li>Monitor for inquiries or disputes.</li>
                            </ol>
                            <h2>Managing Orders</h2>
                            <p>Go to Orders in your dashboard to:</p>
                            <ul>
                                <li>View order details (e.g., customer name, product, date).</li>
                                <li>Respond to customer inquiries via the messaging system.</li>
                                <li>Track order status and download history.</li>
                            </ul>
                            <div class="note"><strong>Note:</strong> Digital products are delivered automatically upon purchase.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Respond to inquiries within 48 hours.</li>
                                <li>Monitor for suspicious activity (e.g., multiple downloads).</li>
                                <li>Keep records of customer interactions for reference.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Refer to "Handling Customer Inquiries" in the Help Center, or explore "Managing Disputes" for additional guidance.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Order Management',
                'title' => 'Managing Disputes',
                'slug' => 'managing-disputes',
                'content' => '<p class="lead">Handle customer disputes professionally to maintain trust on Digitora.</p>
                            <h2>Dispute Process</h2>
                            <p>If a customer raises an issue:</p>
                            <ol>
                                <li>Review the order details in the Orders section.</li>
                                <li>Respond to the customer via the messaging system.</li>
                                <li>Escalate to support if the issue cannot be resolved.</li>
                            </ol>
                            <h2>Common Dispute Scenarios</h2>
                            <ul>
                                <li>File not accessible: Verify the uploaded files and assist the customer.</li>
                                <li>Misleading description: Clarify details and update the listing if needed.</li>
                                <li>Refund requests: Note that refunds are generally not offered for digital products once downloaded.</li>
                            </ul>
                            <div class="warning"><strong>Important:</strong> Respond promptly to maintain customer satisfaction and avoid escalation.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Communicate clearly and professionally.</li>
                                <li>Document all interactions for reference.</li>
                                <li>Update listings to prevent future disputes.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Explore "Processing Orders" for order management, or visit "Need More Help?" to contact support.</p>',
                'last_updated' => '2025-05-04',
            ],
            // Payments
            [
                'section_title' => 'Payments',
                'title' => 'Payment Schedules',
                'slug' => 'payment-schedules',
                'content' => '<p class="lead">Understand how and when you receive payments on Digitora.</p>
                            <h2>Payout Process</h2>
                            <p>Digitora processes payouts on the 15th of each month for the previous month’s revenue:</p>
                            <ul>
                                <li>A 5% platform fee is deducted after payment processing fees.</li>
                                <li>Minimum payout threshold is Rp 500,000.</li>
                                <li>Funds are transferred to your configured payment method.</li>
                            </ul>
                            <h2>Setting Up Payments</h2>
                            <p>Ensure your payment method is set up:</p>
                            <ol>
                                <li>Go to Settings > Billing.</li>
                                <li>Select a payment method (e.g., PayPal, bank transfer).</li>
                                <li>Enter and verify your payment details.</li>
                            </ol>
                            <div class="warning"><strong>Important:</strong> Payouts are delayed if the minimum threshold is not met.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Verify your payment details regularly.</li>
                                <li>Monitor your earnings to ensure timely payouts.</li>
                                <li>Contact support if there are payout issues.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Visit "Tracking Earnings" for more details, or explore "Account Settings" to update your payment method.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Payments',
                'title' => 'Tracking Earnings',
                'slug' => 'tracking-earnings',
                'content' => '<p class="lead">Monitor your earnings and payout history on Digitora.</p>
                            <h2>Accessing Earnings</h2>
                            <p>Go to Payments in your dashboard to view:</p>
                            <ul>
                                <li>Total revenue and deductions (including the 5% platform fee).</li>
                                <li>Payout history and upcoming payouts.</li>
                                <li>Breakdown of earnings by product.</li>
                            </ul>
                            <h2>Understanding Deductions</h2>
                            <p>Each sale incurs:</p>
                            <ul>
                                <li>Payment processing fees (varies by method).</li>
                                <li>A 5% platform fee applied after processing fees.</li>
                            </ul>
                            <div class="note"><strong>Note:</strong> Payouts occur on the 15th of each month if the Rp 500,000 threshold is met.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Track earnings regularly to plan finances.</li>
                                <li>Analyze product performance to maximize revenue.</li>
                                <li>Ensure your payment method is up to date.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Refer to "Payment Schedules" for payout details, or explore "Analytics & Reporting" for deeper insights.</p>',
                'last_updated' => '2025-05-04',
            ],
            // Analytics & Reporting
            [
                'section_title' => 'Analytics & Reporting',
                'title' => 'Understanding Analytics',
                'slug' => 'understanding-analytics',
                'content' => '<p class="lead">Use analytics to track and improve your sales performance on Digitora.</p>
                            <h2>Accessing Analytics</h2>
                            <p>Navigate to Analytics in your dashboard to view:</p>
                            <ul>
                                <li>Sales and revenue trends over time.</li>
                                <li>Customer demographics (e.g., location, purchase history).</li>
                                <li>Product performance metrics (e.g., views, conversions).</li>
                            </ul>
                            <h2>Using Analytics</h2>
                            <p>Analyze data to make informed decisions:</p>
                            <ol>
                                <li>Identify top-performing products to focus marketing efforts.</li>
                                <li>Use demographic data to target specific audiences.</li>
                                <li>Track conversion rates to optimize listings.</li>
                            </ol>
                            <div class="tip"><strong>Tip:</strong> Export reports for detailed analysis or record-keeping.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Review analytics weekly to spot trends.</li>
                                <li>Compare product performance to prioritize updates.</li>
                                <li>Use insights to plan promotions or new products.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Explore "Product Analytics" in Product Management, or visit "Generating Reports" for advanced reporting options.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Analytics & Reporting',
                'title' => 'Generating Reports',
                'slug' => 'generating-reports',
                'content' => '<p class="lead">Generate detailed reports to analyze your Digitora store performance.</p>
                            <h2>Creating Reports</h2>
                            <p>In the Analytics section:</p>
                            <ol>
                                <li>Select the report type (e.g., sales, customer, product).</li>
                                <li>Choose a date range for the report.</li>
                                <li>Click "Generate Report" to view or export the data.</li>
                            </ol>
                            <h2>Report Types</h2>
                            <ul>
                                <li><strong>Sales Report:</strong> Revenue, fees, and net earnings.</li>
                                <li><strong>Customer Report:</strong> Demographics and purchase patterns.</li>
                                <li><strong>Product Report:</strong> Performance metrics for each product.</li>
                            </ul>
                            <div class="note"><strong>Note:</strong> Reports can be exported as CSV files for external analysis.</div>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Generate monthly reports to track progress.</li>
                                <li>Use reports to identify growth opportunities.</li>
                                <li>Share reports with your team for collaborative planning.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Refer to "Understanding Analytics" for data insights, or explore "Pricing Strategies" in Product Management.</p>',
                'last_updated' => '2025-05-04',
            ],
            // Account Settings
            [
                'section_title' => 'Account Settings',
                'title' => 'Managing Your Profile',
                'slug' => 'managing-your-profile',
                'content' => '<p class="lead">Keep your Digitora profile up to date to enhance credibility.</p>
                            <h2>Updating Your Profile</h2>
                            <p>Go to Settings > Profile to update:</p>
                            <ul>
                                <li>Full Name: Your legal name.</li>
                                <li>Profile Picture: A clear image (300x300px recommended).</li>
                                <li>Bio: A 50-100 word description of your business.</li>
                            </ul>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Use a professional photo to build trust.</li>
                                <li>Keep your bio concise and relevant.</li>
                                <li>Update your profile regularly to reflect changes.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Explore "Store Customization" to set up your store, or visit "Securing Your Account" in the Help Center.</p>',
                'last_updated' => '2025-05-04',
            ],
            [
                'section_title' => 'Account Settings',
                'title' => 'Store Customization',
                'slug' => 'store-customization',
                'content' => '<p class="lead">Customize your Digitora store to reflect your brand identity.</p>
                            <h2>Customizing Your Store</h2>
                            <p>Navigate to Settings > Store Customization to update:</p>
                            <ul>
                                <li>Store Name: A unique name for your store.</li>
                                <li>Store Logo: A high-resolution logo (500x500px, PNG or JPG).</li>
                                <li>Store Description: A 100-150 word overview of your offerings.</li>
                                <li>Store Category: The primary category for your products.</li>
                            </ul>
                            <h2>Best Practices</h2>
                            <ul>
                                <li>Choose a memorable store name.</li>
                                <li>Use a high-quality logo for branding.</li>
                                <li>Write a clear description to attract customers.</li>
                            </ul>
                            <h2>Next Steps</h2>
                            <p>Refer to "Managing Your Profile" for profile updates, or explore "Creating Products" in Product Management.</p>',
                'last_updated' => '2025-05-04',
            ],
        ];

        $topics = [];
        foreach ($topicsData as $data) {
            $section = $sections[$data['section_title']];
            $topics[] = DocumentationTopic::create([
                'section_id' => $section->id,
                'title' => $data['title'],
                'slug' => $data['slug'],
                'content' => $data['content'],
                'last_updated' => $data['last_updated'],
            ]);
        }

        // Seed Related Topics
        // Getting Started
        $topics[0]->relatedTopics()->attach([$topics[1]->id, $topics[2]->id, $topics[3]->id]);
        $topics[1]->relatedTopics()->attach([$topics[0]->id, $topics[2]->id, $topics[4]->id]);
        $topics[2]->relatedTopics()->attach([$topics[0]->id, $topics[1]->id, $topics[4]->id]);
        $topics[3]->relatedTopics()->attach([$topics[0]->id, $topics[1]->id, $topics[4]->id]);
        // Product Management
        $topics[4]->relatedTopics()->attach([$topics[5]->id, $topics[6]->id, $topics[7]->id]);
        $topics[5]->relatedTopics()->attach([$topics[4]->id, $topics[6]->id, $topics[7]->id]);
        $topics[6]->relatedTopics()->attach([$topics[4]->id, $topics[5]->id, $topics[7]->id]);
        $topics[7]->relatedTopics()->attach([$topics[4]->id, $topics[5]->id, $topics[6]->id]);
        // Order Management
        $topics[8]->relatedTopics()->attach([$topics[9]->id, $topics[4]->id]);
        $topics[9]->relatedTopics()->attach([$topics[8]->id, $topics[4]->id]);
        // Payments
        $topics[10]->relatedTopics()->attach([$topics[11]->id, $topics[14]->id]);
        $topics[11]->relatedTopics()->attach([$topics[10]->id, $topics[12]->id]);
        // Analytics & Reporting
        $topics[12]->relatedTopics()->attach([$topics[13]->id, $topics[7]->id]);
        $topics[13]->relatedTopics()->attach([$topics[12]->id, $topics[7]->id]);
        // Account Settings
        $topics[14]->relatedTopics()->attach([$topics[15]->id, $topics[1]->id]);
        $topics[15]->relatedTopics()->attach([$topics[14]->id, $topics[1]->id]);

        // Seed Video Tutorials
        $videoTutorialsData = [
            [
                'title' => 'Getting Started with Digitora',
                'description' => 'A walkthrough of the seller platform for new users.',
                'url' => '#',
                'duration' => '10:23',
                'thumbnail' => null,
            ],
            [
                'title' => 'Creating Your First Product',
                'description' => 'Step-by-step guide to listing your digital product.',
                'url' => '#',
                'duration' => '8:45',
                'thumbnail' => null,
            ],
            [
                'title' => 'Understanding Analytics',
                'description' => 'Learn how to track and improve your sales performance.',
                'url' => '#',
                'duration' => '12:17',
                'thumbnail' => null,
            ],
            [
                'title' => 'Managing Customer Orders',
                'description' => 'Guide to processing and handling customer orders.',
                'url' => '#',
                'duration' => '6:30',
                'thumbnail' => null,
            ],
            [
                'title' => 'Setting Up Payment Methods',
                'description' => 'Configure your payment settings for seamless payouts.',
                'url' => '#',
                'duration' => '5:15',
                'thumbnail' => null,
            ],
        ];

        foreach ($videoTutorialsData as $data) {
            VideoTutorial::create($data);
        }
    }
}