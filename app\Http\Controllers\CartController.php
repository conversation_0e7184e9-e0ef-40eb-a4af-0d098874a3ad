<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Course;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Midtrans\Transaction;
use Illuminate\Support\Facades\DB;

class CartController extends Controller
{
    /**
     * Display the cart.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $cart = $this->getCart();

        // Eager load both product and course relationships for cart items
        $cart->load(['items.product', 'items.course']);

        $cartItemCount = $cart->items->count();

        // For authenticated users, use the user dashboard layout
        if (Auth::check()) {
            return view('users.cart.index', compact('cart', 'cartItemCount'));
        }

        // For guests, use the regular cart view
        return view('cart.index', compact('cart'));
    }

    /**
     * Add a product to the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addToCart(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            // We don't need to validate quantity anymore since we always set it to 1
        ]);

        // We'll allow guests to add items to cart
        // The cart will be associated with their session

        $product = Product::findOrFail($request->product_id);
        $cart = $this->getCart();

        // Check if the product is already in the cart
        $cartItem = CartItem::where('cart_id', $cart->id)
            ->where('product_id', $product->id)
            ->first();

        if ($cartItem) {
            // If product already exists in cart, keep quantity at 1 (don't increment)
            // We'll just return a message that the product is already in the cart
            return redirect()->back()->with('info', 'This product is already in your cart.');
        } else {
            // Calculate the price (use discount price if available)
            $price = $product->discount_price > 0 ? $product->discount_price : $product->price;

            // Ensure price is an integer for Midtrans compatibility
            $price = (int)$price;

            // Make sure price is at least 1000 IDR
            if ($price < 1000) {
                $price = 1000;
            }

            // Add new cart item with quantity fixed at 1
            CartItem::create([
                'cart_id' => $cart->id,
                'product_id' => $product->id,
                'quantity' => 1, // Always set quantity to 1
                'price' => $price,
            ]);
        }

        return redirect()->back()->with('success', 'Product added to cart successfully!');
    }

    /**
     * Add a course to the cart.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addCourseToCart(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
        ]);

        $course = Course::findOrFail($request->course_id);
        $cart = $this->getCart();

        // Check if the course is already in the cart
        $cartItem = CartItem::where('cart_id', $cart->id)
            ->where('course_id', $course->id)
            ->first();

        if ($cartItem) {
            return redirect()->back()->with('info', 'This course is already in your cart.');
        } else {
            // Calculate the price (use discount price if available)
            $price = $course->discount_price > 0 ? $course->discount_price : $course->price;

            // Ensure price is an integer for Midtrans compatibility
            $price = (int)$price;

            // Make sure price is at least 1000 IDR
            if ($price < 1000) {
                $price = 1000;
            }

            // Add new cart item for course
            CartItem::create([
                'cart_id' => $cart->id,
                'course_id' => $course->id,
                'quantity' => 1, // Always set quantity to 1 for courses
                'price' => $price,
            ]);
        }

        return redirect()->back()->with('success', 'Course added to cart successfully!');
    }

    /**
     * Update the quantity of a cart item.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateCartItem(Request $request, $id)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1',
        ]);

        $cartItem = CartItem::findOrFail($id);
        $cart = $this->getCart();

        // Ensure the cart item belongs to the user's cart
        if ($cartItem->cart_id !== $cart->id) {
            abort(403);
        }

        // Always set quantity to 1, regardless of the requested quantity
        $cartItem->quantity = 1;
        $cartItem->save();

        return redirect()->route('cart.index')->with('success', 'Cart updated successfully!');
    }

    /**
     * Remove a cart item.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function removeCartItem($id)
    {
        $cartItem = CartItem::findOrFail($id);
        $cart = $this->getCart();

        // Ensure the cart item belongs to the user's cart
        if ($cartItem->cart_id !== $cart->id) {
            abort(403);
        }

        $cartItem->delete();

        return redirect()->route('cart.index')->with('success', 'Item removed from cart!');
    }

    /**
     * Clear the cart.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function clearCart()
    {
        $cart = $this->getCart();

        // Delete all cart items
        CartItem::where('cart_id', $cart->id)->delete();

        return redirect()->route('cart.index')->with('success', 'Cart cleared successfully!');
    }

    /**
     * Get the current user's cart or create a new one.
     *
     * @return \App\Models\Cart
     */
    protected function getCart()
    {
        if (Auth::check()) {
            // Get or create cart for logged in user
            $cart = Cart::firstOrCreate(
                ['user_id' => Auth::id()],
                ['session_id' => Str::random(40)]
            );
        } else {
            // Get or create cart for guest user
            $sessionId = session()->get('cart_session_id');

            if (!$sessionId) {
                $sessionId = Str::random(40);
                session()->put('cart_session_id', $sessionId);
            }

            // Create or get a guest user
            $guestUser = User::firstOrCreate(
                ['email' => 'guest_' . $sessionId . '@example.com'],
                [
                    'id' => (string) Str::uuid(),
                    'name' => 'Guest User',
                    'password' => Hash::make(Str::random(16)), // Generate a secure random password
                ]
            );

            // Assign user role to guest user if they don't have any roles
            if (!$guestUser->activeRoles()->exists()) {
                $guestUser->assignRole('user');
            }

            $cart = Cart::firstOrCreate(
                ['session_id' => $sessionId],
                ['user_id' => $guestUser->id]
            );
        }

        return $cart;
    }

    /**
     * Proceed to checkout.
     *
     * @return \Illuminate\View\View
     */
    public function checkout()
    {
        $cart = $this->getCart();

        // Eager load both product and course relationships for cart items
        $cart->load(['items.product', 'items.course']);

        $cartItemCount = $cart->items->count();

        // Ensure cart is not empty
        if ($cart->items->isEmpty()) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty!');
        }

        // For authenticated users, use the user dashboard layout
        if (Auth::check()) {
            return view('users.cart.checkout', compact('cart', 'cartItemCount'));
        }

        return view('cart.checkout', compact('cart'));
    }

    /**
     * Process the checkout payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    // public function processPayment(Request $request)
    // {
    //     // Require login for payment processing
    //     if (!Auth::check()) {
    //         // Store checkout data in session
    //         session()->put('checkout_data', $request->all());
    //         return redirect()->route('login')->with('message', 'Please login to complete your purchase.');
    //     }

    //     // Process payment logic here

    //     return redirect()->route('user.purchases')->with('success', 'Your purchase was successful!');
    // }

    public function checkTransactions(Request $request)
    {
        $productIds = json_decode($request->productIds, true);
        $products = Product::find($productIds);
        // if (!$product)
        //     return response()->json(['message' => 'Produk tidak ditemukan!'], 404);

        $orders = Order::where('buyer_id', Auth::id())
            ->whereIn('product_id', $productIds)->first();

        if (!$orders)
            return response()->json(["status" => "new-order"], 200);

        $successOrders = Order::where('buyer_id', Auth::id())
            ->whereIn('product_id', $productIds)->where('status', '=', 'success')->first();

        if ($successOrders)
            return response()->json(['message' => 'Anda sudah melakukan pembayaran pada produk', "status" => "success"], 200);
        else {
            $pendingOrder = Order::where('buyer_id', Auth::id())
                ->whereIn('product_id', $productIds)->where('status', '=', 'pending')->latest()->first();
            if ($pendingOrder)
                return response()->json(['message' => 'Anda mempunyai pembayaran dengan status pending! Silakan melanjutkan pembayaran sebelumnya! Klik tombol buat transaksi baru untuk menghapus transaksi sebelumnya!', "status" => "pending", "orderId" => $pendingOrder->order_id, "snap_token" => $pendingOrder->snap_token], 200);
            else
                return response()->json(["status" => "new-order"], 200);
        }
    }

    public function cancelTransactions(Request $request)
    {
        \Midtrans\Config::$serverKey = config('services.midtrans.serverKey');
        \Midtrans\Config::$isProduction = config('services.midtrans.isProduction');
        \Midtrans\Config::$isSanitized = config('services.midtrans.isSanitized');
        \Midtrans\Config::$is3ds = config('services.midtrans.is3ds');

        $orderId = $request->orderId;
        try {
            $response = Transaction::cancel($orderId);
        } catch (\Throwable $th) {
            Order::where('order_id', '=', $orderId)->update([
                'status' => 'canceled'
            ]);
        }
    }

    public function processPayment(Request $request)
    {
        // Initialize response array
        $response = [];

        // dapatin data midtrans
        \Midtrans\Config::$serverKey = config('services.midtrans.serverKey');
        \Midtrans\Config::$isProduction = config('services.midtrans.isProduction');
        \Midtrans\Config::$isSanitized = config('services.midtrans.isSanitized');
        \Midtrans\Config::$is3ds = config('services.midtrans.is3ds');


        // bikin transaksi untuk create invoice
        DB::transaction(function () use ($request, &$response) {
            $grossAmount = 0;

            $productIds = json_decode($request->productIds, true);
            $buyer = Auth::user();

            $orderId = Str::random(20) . time();

            $itemDetails = [];

            foreach ($productIds as $productId) {
                $product = Product::find($productId);

                $amount = $product->price; // jika tidak ada diskon, maka harga normal

                $discountPrice = $product->discount_price;

                if ($discountPrice != null && $discountPrice != 0)
                    $amount = (int) ($discountPrice); // jika ada diskon, maka harga diskon

                $grossAmount += $amount;

                // bikin invoice
                $order = Order::create([
                    'order_id' => $orderId,
                    'buyer_id' => $buyer->id,
                    'product_id' => $product->id,
                    'amount' => floatval($amount),
                    'status' => 'pending',
                ]);


                array_push($itemDetails, array(
                    'id' => Str::random(5),
                    'price' => $product->price,
                    'quantity' => 1,
                    'name' => $product->name,
                ));

                // jika ada diskon, tambahkan item diskon
                if ($discountPrice != null && $discountPrice != 0) {
                    array_push($itemDetails, array(
                        'id' => Str::random(3),
                        'price' => -(int) ($product->price - $discountPrice),
                        'quantity' => 1,
                        'name' => 'Discount for ' . $product->name
                    ));
                }
            }

            // payload untuk midtrans
            $payload = [
                'transaction_details' => [
                    'order_id' => $orderId,
                    'gross_amount' => $grossAmount,
                ],
                'customer_details' => [
                    'first_name' => $buyer->name,
                    'email' => $buyer->email,
                ],
                'item_details' => $itemDetails
            ];

            // buat snap token, dan simpan invoice
            $snapToken = \Midtrans\Snap::getSnapToken($payload);

            $orders = Order::where('order_id', '=', $orderId)->get();

            foreach ($orders as $order) {
                $order->status = 'pending';

                $order->snap_token = $snapToken;
                $order->save();
            }


            $response['snap_token'] = $snapToken;
        });

        return response()->json($response);
    }
}
