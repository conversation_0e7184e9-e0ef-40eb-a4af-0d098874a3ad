<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Database\Seeder;

class AddProductOtherCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder adds "Other" categories at all levels of the product categorization system.
     */
    public function run(): void
    {
        $this->command->info('Adding "Other" categories at all levels...');

        // Step 1: Add "Other" category
        $this->addOtherCategory();

        // Step 2: Add "Other" subcategory to each main category
        $this->addOtherSubcategoriesToMainCategories();

        // Step 3: Add "Other" detailed category to each subcategory
        $this->addOtherDetailedCategoriesToAllSubcategories();

        // Step 4: Verify all subcategories have an "Other" detailed category
        $this->verifyAllSubcategoriesHaveOtherDetailedCategory();

        $this->command->info('Successfully added "Other" categories at all levels!');
    }

    // add other in category table
    private function addOtherCategory(): void
    {
        // Check if "Other" category already exists
        $existingOther = ProductCategory::where('name', 'Other')->first();

        if ($existingOther) {
            $this->command->info("  - 'Other' category already exists");
            return;
        }

        // Create the "Other" category
        ProductCategory::create([
            'name' => 'Other',
            'slug' => 'other',
            'description' => 'Other items',
            'is_active' => true,
            'sort_order' => 999,
        ]);

        $this->command->info("  - Added 'Other' category");
    }


    /**
     * Add "Other" subcategory to each main category.
     */
    private function addOtherSubcategoriesToMainCategories(): void
    {
        $mainCategories = ProductCategory::all();
        $this->command->info("Found {$mainCategories->count()} main categories");

        foreach ($mainCategories as $mainCategory) {
            $this->command->info("Processing main category: {$mainCategory->name}");

            // Check if "Other" subcategory already exists
            $existingOther = ProductSubcategory::where('category_id', $mainCategory->id)
                ->where('name', 'Other')
                ->first();

            if ($existingOther) {
                $this->command->info("  - 'Other' subcategory already exists for category: {$mainCategory->name}");
                continue;
            }

            // Get the highest sort order for this category
            $maxSortOrder = ProductSubcategory::where('category_id', $mainCategory->id)
                ->max('sort_order');

            // Create a unique slug
            $slug = 'other-' . $mainCategory->id;
            $counter = 1;
            $originalSlug = $slug;

            while (ProductSubcategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the "Other" subcategory
            $otherSubcategory = ProductSubcategory::create([
                'category_id' => $mainCategory->id,
                'name' => 'Other',
                'slug' => $slug,
                'description' => 'Other items in this category',
                'legacy_code' => 'other_' . $mainCategory->name,
                'sort_order' => ($maxSortOrder !== null) ? $maxSortOrder + 1 : 0,
                'is_active' => true,
            ]);

            $this->command->info("  - Added 'Other' subcategory to category: {$mainCategory->name}");

            // Immediately add an "Other" detailed category to this "Other" subcategory
            $this->addOtherDetailedCategoryToSubcategory($otherSubcategory);
        }
    }

    /**
     * Add "Other" detailed category to all subcategories.
     */
    private function addOtherDetailedCategoriesToAllSubcategories(): void
    {
        // Get all subcategories that are not named "Other"
        $regularSubcategories = ProductSubcategory::where('name', '!=', 'Other')->get();
        $this->command->info("Found {$regularSubcategories->count()} regular subcategories");

        foreach ($regularSubcategories as $subcategory) {
            $this->addOtherDetailedCategoryToSubcategory($subcategory);
        }
    }

    /**
     * Add "Other" detailed category to a specific subcategory.
     */
    private function addOtherDetailedCategoryToSubcategory(ProductSubcategory $subcategory): void
    {
        $categoryName = $subcategory->category ? $subcategory->category->name : 'Unknown';

        // Check if "Other" detailed category already exists
        $existingOther = ProductDetailedCategory::where('subcategory_id', $subcategory->id)
            ->where('name', 'Other')
            ->first();

        if ($existingOther) {
            $this->command->info("    - 'Other' detailed category already exists for subcategory: {$subcategory->name} (under {$categoryName})");
            return;
        }

        // Get the highest sort order for this subcategory
        $maxSortOrder = ProductDetailedCategory::where('subcategory_id', $subcategory->id)
            ->max('sort_order');

        // Create a unique slug
        $slug = 'other-' . $subcategory->id;
        $counter = 1;
        $originalSlug = $slug;

        while (ProductDetailedCategory::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Create the "Other" detailed category
        ProductDetailedCategory::create([
            'subcategory_id' => $subcategory->id,
            'name' => 'Other',
            'slug' => $slug,
            'description' => 'Other items in this subcategory',
            'sort_order' => ($maxSortOrder !== null) ? $maxSortOrder + 1 : 0,
            'is_active' => true,
        ]);

        $this->command->info("    - Added 'Other' detailed category to subcategory: {$subcategory->name} (under {$categoryName})");
    }

    /**
     * Verify that all subcategories have an "Other" detailed category.
     */
    private function verifyAllSubcategoriesHaveOtherDetailedCategory(): void
    {
        $this->command->info("Verifying all subcategories have an 'Other' detailed category...");

        $allSubcategories = ProductSubcategory::all();
        $missingCount = 0;

        foreach ($allSubcategories as $subcategory) {
            $hasOtherDetailedCategory = ProductDetailedCategory::where('subcategory_id', $subcategory->id)
                ->where('name', 'Other')
                ->exists();

            if (!$hasOtherDetailedCategory) {
                $categoryName = $subcategory->category ? $subcategory->category->name : 'Unknown';
                $this->command->warn("Missing 'Other' detailed category for subcategory: {$subcategory->name} (under {$categoryName})");
                $this->addOtherDetailedCategoryToSubcategory($subcategory);
                $missingCount++;
            }
        }

        if ($missingCount === 0) {
            $this->command->info("All subcategories have an 'Other' detailed category!");
        } else {
            $this->command->info("Added {$missingCount} missing 'Other' detailed categories.");
        }
    }
}
