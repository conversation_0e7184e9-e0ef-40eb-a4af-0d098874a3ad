<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('seller_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->foreignUuid('category_id')->nullable()->references('id')->on('product_categories')->onUpdate('cascade')->onDelete('set null');
            $table->foreignUuid('subcategory_id')->nullable()->references('id')->on('product_subcategories')->onUpdate('cascade')->onDelete('set null');
            $table->foreignUuid('detailed_category_id')->nullable()->references('id')->on('product_detailed_categories')->onUpdate('cascade')->onDelete('set null');
            $table->integer('price'); // Price in IDR (integer for Midtrans compatibility)
            $table->integer('discount_price')->nullable(); // Discounted price in IDR
            $table->enum('difficulty_level', ['beginner', 'intermediate', 'advanced'])->default('beginner');
            $table->enum('status', ['draft', 'active', 'inactive'])->default('draft');
            $table->string('thumbnail')->nullable(); // Main course thumbnail
            $table->json('images')->nullable(); // Additional course images
            $table->text('what_you_will_learn')->nullable(); // JSON array of learning outcomes
            $table->text('requirements')->nullable(); // JSON array of requirements
            $table->text('target_audience')->nullable(); // JSON array of target audience
            $table->integer('estimated_duration')->nullable(); // Duration in minutes
            $table->decimal('average_rating', 3, 2)->default(0.00);
            $table->integer('reviews_count')->default(0);
            $table->integer('students_count')->default(0); // Number of enrolled students
            $table->boolean('is_featured')->default(false);
            $table->timestamp('published_at')->nullable();
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['seller_id', 'status']);
            $table->index(['category_id', 'status']);
            $table->index(['status', 'is_featured']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
