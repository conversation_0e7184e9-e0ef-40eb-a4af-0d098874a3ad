<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserMembership extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'membership_tier_id',
        'started_at',
        'expires_at',
        'status',
        'amount_paid',
        'payment_method',
        'transaction_id',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
        'amount_paid' => 'integer',
    ];

    /**
     * Get the user that owns this membership.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the membership tier.
     */
    public function membershipTier()
    {
        return $this->belongsTo(MembershipTier::class);
    }

    /**
     * Check if membership is currently active.
     */
    public function isActive()
    {
        if ($this->status !== 'active') {
            return false;
        }

        // If no expiry date, it's lifetime/free tier
        if (!$this->expires_at) {
            return true;
        }

        return $this->expires_at->isFuture();
    }

    /**
     * Check if membership is expired.
     */
    public function isExpired()
    {
        if ($this->status === 'expired') {
            return true;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return true;
        }

        return false;
    }

    /**
     * Get days remaining until expiry.
     */
    public function getDaysRemainingAttribute()
    {
        if (!$this->expires_at) {
            return null; // Lifetime membership
        }

        return max(0, Carbon::now()->diffInDays($this->expires_at, false));
    }

    /**
     * Scope to get active memberships only.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Automatically expire membership if past expiry date.
     */
    public function checkAndUpdateExpiry()
    {
        if ($this->isExpired() && $this->status === 'active') {
            $this->update(['status' => 'expired']);
        }
    }
}
