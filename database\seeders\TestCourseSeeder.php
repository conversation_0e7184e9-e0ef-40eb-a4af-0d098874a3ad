<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Course;
use App\Models\User;
use App\Models\ProductDetailedCategory;

class TestCourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user as seller
        $seller = User::first();
        if (!$seller) {
            $this->command->error('No users found. Please create a user first.');
            return;
        }

        // Get first category
        $category = ProductDetailedCategory::first();
        if (!$category) {
            $this->command->error('No categories found. Please run category seeders first.');
            return;
        }

        // Create test course
        $course = Course::create([
            'seller_id' => $seller->id,
            'title' => 'Complete WhatsApp Bot Development Course',
            'slug' => 'complete-whatsapp-bot-development-course',
            'description' => 'Learn to build powerful WhatsApp bots for business automation',
            'short_description' => 'Master WhatsApp bot development from scratch',
            'detailed_category_id' => $category->id,
            'price' => 299000,
            'difficulty_level' => 'beginner',
            'status' => 'active',
            'what_you_will_learn' => [
                'Build WhatsApp bots from scratch',
                'Integrate with business systems',
                'Automate customer service',
                'Deploy bots to production'
            ],
            'requirements' => [
                'Basic programming knowledge',
                'Computer with internet connection'
            ],
            'target_audience' => [
                'Developers',
                'Business owners',
                'Entrepreneurs'
            ],
            'estimated_duration' => 480, // 8 hours
        ]);

        $this->command->info("Test course created with ID: {$course->id}");
        $this->command->info("View at: /seller/courses/{$course->id}");
    }
}
