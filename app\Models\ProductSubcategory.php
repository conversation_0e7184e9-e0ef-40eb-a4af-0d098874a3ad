<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductSubcategory extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'description',
        'icon',
        'legacy_code',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the category that owns the subcategory.
     */
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the detailed categories for this subcategory.
     */
    public function detailedCategories()
    {
        return $this->hasMany(ProductDetailedCategory::class, 'subcategory_id')
            ->orderBy('sort_order')
            ->orderBy('name');
    }

    /**
     * Get active detailed categories.
     */
    public function activeDetailedCategories()
    {
        return $this->hasMany(ProductDetailedCategory::class, 'subcategory_id')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name');
    }

    /**
     * Get all products in this subcategory.
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'subcategory_id');
    }

    /**
     * Scope a query to only include active subcategories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the full path of the subcategory (breadcrumb).
     */
    public function getPathAttribute()
    {
        return $this->category->name . ' > ' . $this->name;
    }

    /**
     * Get the full slug path of the subcategory.
     */
    public function getFullSlugPathAttribute()
    {
        return $this->category->slug . '/' . $this->slug;
    }

    /**
     * Get the legacy category code that corresponds to this subcategory.
     */
    public function getLegacyCategoryAttribute()
    {
        $legacyMapping = [
            'templates' => 'template',
            'spreadsheets' => 'spreadsheet',
            'dashboards' => 'dashboard',
            'planners' => 'planner',
            'worksheets' => 'worksheet',
            'graphics' => 'graphic',
            'fonts' => 'font',
            'illustrations' => 'illustration',
            'ui-kits' => 'ui_kit',
            'code' => 'code',
            'plugins' => 'plugin',
            'api-templates' => 'api_template',
            'course-materials' => 'course_material',
            'e-books' => 'ebook',
        ];

        return $legacyMapping[$this->slug] ?? null;
    }
}
