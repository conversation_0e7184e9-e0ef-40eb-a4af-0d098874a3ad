<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDocumentationTopicsTable extends Migration
{
    public function up()
    {
        Schema::create('documentation_topics', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('section_id')->references('id')->on('documentation_sections')->onUpdate('cascade')->onDelete('cascade');
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('content');
            $table->date('last_updated');
            $table->integer('helpful_yes')->default(0);
            $table->integer('helpful_no')->default(0);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('documentation_topics');
    }
}
