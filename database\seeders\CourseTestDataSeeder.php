<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Course;
use App\Models\Order;
use App\Models\SellerApplication;
use App\Models\Category;
use App\Models\Subcategory;
use App\Models\DetailedCategory;
use App\Models\CourseSection;
use App\Models\CourseCurriculumItem;

class CourseTestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test categories if they don't exist
        $category = Category::firstOrCreate([
            'name' => 'Technology',
            'slug' => 'technology'
        ]);

        $subcategory = Subcategory::firstOrCreate([
            'category_id' => $category->id,
            'name' => 'Programming',
            'slug' => 'programming'
        ]);

        $detailedCategory = DetailedCategory::firstOrCreate([
            'subcategory_id' => $subcategory->id,
            'name' => 'Web Development',
            'slug' => 'web-development'
        ]);

        // Create test seller user
        $seller = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'id' => (string) Str::uuid(),
            'name' => 'Test Course Seller',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        // Create seller application
        $sellerApplication = SellerApplication::firstOrCreate([
            'user_id' => $seller->id
        ], [
            'store_name' => 'Digital Learning Hub',
            'store_name_slug' => 'digital-learning-hub',
            'store_description' => 'Premium online courses for digital skills',
            'phone' => '+62812345678',
            'address' => 'Jakarta, Indonesia',
            'status' => 'approved',
            'approved_at' => now(),
        ]);

        // Create test buyer users
        $buyer1 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'id' => (string) Str::uuid(),
            'name' => 'Test Buyer One',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        $buyer2 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'id' => (string) Str::uuid(),
            'name' => 'Test Buyer Two',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        $buyer3 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'id' => (string) Str::uuid(),
            'name' => 'Test Buyer Three',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        // Create test courses
        $course1 = Course::firstOrCreate([
            'slug' => 'complete-web-development-bootcamp'
        ], [
            'id' => (string) Str::uuid(),
            'seller_id' => $seller->id,
            'title' => 'Complete Web Development Bootcamp',
            'short_description' => 'Learn HTML, CSS, JavaScript, React, Node.js and more in this comprehensive bootcamp.',
            'description' => 'This comprehensive web development bootcamp will take you from beginner to advanced level. You\'ll learn modern web technologies including HTML5, CSS3, JavaScript ES6+, React, Node.js, Express, MongoDB, and deployment strategies.',
            'price' => 299000,
            'discount_price' => 199000,
            'category_id' => $category->id,
            'subcategory_id' => $subcategory->id,
            'detailed_category_id' => $detailedCategory->id,
            'difficulty_level' => 'beginner',
            'estimated_duration' => 2400, // 40 hours
            'status' => 'active',
            'thumbnail' => null,
        ]);

        $course2 = Course::firstOrCreate([
            'slug' => 'advanced-react-masterclass'
        ], [
            'id' => (string) Str::uuid(),
            'seller_id' => $seller->id,
            'title' => 'Advanced React Masterclass',
            'short_description' => 'Master advanced React concepts including hooks, context, performance optimization, and testing.',
            'description' => 'Take your React skills to the next level with this advanced masterclass. Learn advanced patterns, performance optimization, testing strategies, and modern React features.',
            'price' => 199000,
            'discount_price' => 149000,
            'category_id' => $category->id,
            'subcategory_id' => $subcategory->id,
            'detailed_category_id' => $detailedCategory->id,
            'difficulty_level' => 'advanced',
            'estimated_duration' => 1800, // 30 hours
            'status' => 'active',
            'thumbnail' => null,
        ]);

        $course3 = Course::firstOrCreate([
            'slug' => 'python-data-science-fundamentals'
        ], [
            'id' => (string) Str::uuid(),
            'seller_id' => $seller->id,
            'title' => 'Python Data Science Fundamentals',
            'short_description' => 'Learn Python for data science with pandas, numpy, matplotlib, and machine learning basics.',
            'description' => 'Start your data science journey with Python. Learn essential libraries like pandas, numpy, matplotlib, and get introduced to machine learning concepts.',
            'price' => 249000,
            'discount_price' => 0,
            'category_id' => $category->id,
            'subcategory_id' => $subcategory->id,
            'detailed_category_id' => $detailedCategory->id,
            'difficulty_level' => 'intermediate',
            'estimated_duration' => 2100, // 35 hours
            'status' => 'active',
            'thumbnail' => null,
        ]);

        // Create course sections and curriculum items for course1
        $this->createCourseSections($course1);
        $this->createCourseSections($course2);
        $this->createCourseSections($course3);

        // Create successful orders (purchases)
        $this->createSuccessfulOrder($buyer1, $course1, 199000);
        $this->createSuccessfulOrder($buyer1, $course2, 149000);
        $this->createSuccessfulOrder($buyer2, $course1, 199000);
        $this->createSuccessfulOrder($buyer3, $course3, 249000);

        $this->command->info('Course test data seeded successfully!');
        $this->command->info('');
        $this->command->info('Test Accounts Created:');
        $this->command->info('======================');
        $this->command->info('Seller Account:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
        $this->command->info('');
        $this->command->info('Buyer Accounts (with course purchases):');
        $this->command->info('Email: <EMAIL> | Password: password123');
        $this->command->info('  - Purchased: Complete Web Development Bootcamp');
        $this->command->info('  - Purchased: Advanced React Masterclass');
        $this->command->info('');
        $this->command->info('Email: <EMAIL> | Password: password123');
        $this->command->info('  - Purchased: Complete Web Development Bootcamp');
        $this->command->info('');
        $this->command->info('Email: <EMAIL> | Password: password123');
        $this->command->info('  - Purchased: Python Data Science Fundamentals');
    }

    private function createCourseSections($course)
    {
        $sections = [
            [
                'title' => 'Getting Started',
                'description' => 'Introduction and setup',
                'items' => [
                    ['title' => 'Welcome to the Course', 'type' => 'lecture', 'is_preview' => true],
                    ['title' => 'Course Overview', 'type' => 'lecture', 'is_preview' => true],
                    ['title' => 'Setting Up Your Environment', 'type' => 'lecture', 'is_preview' => false],
                ]
            ],
            [
                'title' => 'Core Concepts',
                'description' => 'Fundamental concepts and principles',
                'items' => [
                    ['title' => 'Understanding the Basics', 'type' => 'lecture', 'is_preview' => false],
                    ['title' => 'Hands-on Exercise', 'type' => 'exercise', 'is_preview' => false],
                    ['title' => 'Additional Resources', 'type' => 'pdf', 'is_preview' => false],
                ]
            ],
            [
                'title' => 'Advanced Topics',
                'description' => 'Advanced concepts and real-world applications',
                'items' => [
                    ['title' => 'Advanced Techniques', 'type' => 'lecture', 'is_preview' => false],
                    ['title' => 'Project Work', 'type' => 'exercise', 'is_preview' => false],
                    ['title' => 'Final Assessment', 'type' => 'quiz', 'is_preview' => false],
                ]
            ]
        ];

        foreach ($sections as $index => $sectionData) {
            $section = CourseSection::create([
                'id' => (string) Str::uuid(),
                'course_id' => $course->id,
                'title' => $sectionData['title'],
                'description' => $sectionData['description'],
                'sort_order' => $index + 1,
                'is_active' => true,
            ]);

            foreach ($sectionData['items'] as $itemIndex => $itemData) {
                CourseCurriculumItem::create([
                    'id' => (string) Str::uuid(),
                    'course_id' => $course->id,
                    'section_id' => $section->id,
                    'title' => $itemData['title'],
                    'type' => $itemData['type'],
                    'content' => 'Sample content for ' . $itemData['title'],
                    'video_url' => $itemData['type'] === 'lecture' ? 'https://www.youtube.com/watch?v=dQw4w9WgXcQ' : null,
                    'sort_order' => $itemIndex + 1,
                    'is_preview' => $itemData['is_preview'],
                    'is_active' => true,
                ]);
            }
        }
    }

    private function createSuccessfulOrder($buyer, $course, $amount)
    {
        Order::create([
            'id' => (string) Str::uuid(),
            'order_id' => 'TEST-' . strtoupper(Str::random(10)),
            'buyer_id' => $buyer->id,
            'seller_id' => $course->seller_id,
            'course_id' => $course->id,
            'amount' => $amount,
            'status' => 'success',
            'payment_method' => 'test_payment',
            'snap_token' => null,
            'created_at' => now()->subDays(rand(1, 30)),
            'updated_at' => now()->subDays(rand(1, 30)),
        ]);
    }
}
