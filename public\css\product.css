/* Custom styles for product management */

/* Status Labels (Active/Draft) */
.status-label {
    transition: all 0.3s ease;
    font-weight: 500;
}

.status-label.border-green-100 {
    border-color: #d4f4e2;
    background-color: #e6f9ed;
    color: #2f855a;
}

.status-label.border-orange-100 {
    border-color: #feebc8;
    background-color: #fffaf0;
    color: #c05621;
}

/* Existing File/Image Items */
.existing-file-item, .existing-image {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.existing-file-item[style*="display: none"],
.existing-image[style*="display: none"] {
    opacity: 0;
    transform: translateY(-10px);
}

/* Upload Areas */
.upload-area {
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #a5b4fc;
    background-color: #f8fafc;
}

/* Choose Buttons */
.choose-image, .choose-files {
    transition: all 0.3s ease;
}

.choose-image:hover, .choose-files:hover {
    background-color: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Form Inputs */
input, select, textarea {
    transition: all 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Buttons */
button, a {
    transition: all 0.3s ease;
}

button:hover, a:hover {
    transform: translateY(-1px);
}

/* Cards */
.rounded-xl {
    border-radius: 1rem;
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Product Card Layout */
.product-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.product-card {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.product-card-link {
    display: block;
    text-decoration: none;
    color: inherit;
}

.product-card-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.product-card-content {
    padding: 1.25rem;
    position: relative;
}

.product-card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.product-card-meta span {
    font-size: 0.875rem;
    color: #6b7280;
}

.product-card-price {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
}

/* Dropdown Menu for Actions */
.product-card-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

.dropdown-toggle {
    background: #e7f2ed;
    border: #000000;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 0.375rem;
    transition: background-color 0.3s ease;
}

.dropdown-toggle:hover {
    background-color: #55575c;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 10;
    min-width: 150px;
    overflow: hidden;
}

.dropdown-menu a,
.dropdown-menu button {
    display: block;
    width: 100%;
    text-align: left;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #374151;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.dropdown-menu a:hover,
.dropdown-menu button:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

.dropdown-menu button.delete {
    color: #dc2626;
}

.dropdown-menu button.delete:hover {
    background-color: #fee2e2;
    color: #b91c1c;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    .product-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .product-card {
        border-radius: 0.5rem;
    }

    .product-card-image {
        height: 120px;
    }

    .product-card-content {
        padding: 1rem;
    }

    .product-card-title {
        font-size: 1.125rem;
    }

    .product-card-meta span {
        font-size: 0.75rem;
    }

    .product-card-price {
        font-size: 1rem;
    }

    .dropdown-toggle {
        padding: 0.375rem;
    }

    .dropdown-menu {
        min-width: 120px;
    }

    .dropdown-menu a,
    .dropdown-menu button {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
}