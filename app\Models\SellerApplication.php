<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class SellerApplication extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'id_type',
        'id_number',
        'id_document',
        'bank_name',
        'account_number',
        'account_holder_name',
        'payment_method',
        'store_name',
        'store_name_slug',
        'store_description',
        'store_logo',
        'status',
    ];

    // Encrypt id_number when setting
    public function setIdNumberAttribute($value)
    {
        $this->attributes['id_number'] = Crypt::encryptString($value);
    }

    // Decrypt id_number when retrieving
    public function getIdNumberAttribute($value)
    {
        return Crypt::decryptString($value);
    }

    // Encrypt bank_name when setting
    public function setBankNameAttribute($value)
    {
        $this->attributes['bank_name'] = Crypt::encryptString($value);
    }

    // Decrypt bank_name when retrieving
    public function getBankNameAttribute($value)
    {
        return Crypt::decryptString($value);
    }

    // Encrypt account_number when setting
    public function setAccountNumberAttribute($value)
    {
        $this->attributes['account_number'] = Crypt::encryptString($value);
    }

    // Decrypt account_number when retrieving
    public function getAccountNumberAttribute($value)
    {
        return Crypt::decryptString($value);
    }

    // Encrypt account_holder_name when setting
    public function setAccountHolderNameAttribute($value)
    {
        $this->attributes['account_holder_name'] = Crypt::encryptString($value);
    }

    // Decrypt account_holder_name when retrieving
    public function getAccountHolderNameAttribute($value)
    {
        return Crypt::decryptString($value);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
