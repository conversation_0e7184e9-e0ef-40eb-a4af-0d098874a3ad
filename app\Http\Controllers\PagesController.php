<?php

namespace App\Http\Controllers;

use App\Models\Faq;
use App\Models\HelpCategory;
use App\Models\HelpArticle;
use App\Models\Product;
use App\Models\User;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Cart;

class PagesController extends Controller
{
    /**
     * Display the browse products page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function browseProducts(Request $request)
    {
        $query = Product::where('status', 'active')
            ->whereHas('seller', function($q) {
                $q->whereHas('sellerApplication', function($q2) {
                    $q2->where('status', 'approved');
                });
            })
            ->with([
                'seller',
                'seller.sellerApplication',
                'productCategory',
                'productSubcategory',
                'productDetailedCategory'
            ]);

        // Search functionality
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filters
        $selectedCategory = null;
        $selectedSubcategory = null;

        // Main category filter
        if ($category = $request->input('category')) {
            $selectedCategory = \App\Models\ProductCategory::where('slug', $category)->first();
            if ($selectedCategory) {
                $query->where('category_id', $selectedCategory->id);
            } else {
                // Try legacy category as fallback
                $query->where('category', $category);
            }
        }

        // Subcategory filter
        if ($subcategory = $request->input('subcategory')) {
            $selectedSubcategory = \App\Models\ProductSubcategory::where('slug', $subcategory)->first();
            if ($selectedSubcategory) {
                $query->where('subcategory_id', $selectedSubcategory->id);
            }
        }

        // Detailed category filter
        if ($detailedCategory = $request->input('detailed_category')) {
            $selectedDetailedCategory = \App\Models\ProductDetailedCategory::where('slug', $detailedCategory)->first();
            if ($selectedDetailedCategory) {
                $query->where('detailed_category_id', $selectedDetailedCategory->id);
            }
        }

        // Sort functionality
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Paginate results - always use 10 products per page
        $products = $query->paginate(10);

        // Get all categories for the filter dropdown
        $categories = \App\Models\ProductCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // Get subcategories based on selected category or all subcategories
        $subcategories = \App\Models\ProductSubcategory::where('is_active', true);
        if ($selectedCategory) {
            $subcategories->where('category_id', $selectedCategory->id);
        }
        $subcategories = $subcategories->orderBy('sort_order')->orderBy('name')->get();

        // Get detailed categories based on selected subcategory or all detailed categories
        $detailedCategories = \App\Models\ProductDetailedCategory::where('is_active', true);
        if ($selectedSubcategory) {
            $detailedCategories->where('subcategory_id', $selectedSubcategory->id);
        }
        $detailedCategories = $detailedCategories->orderBy('sort_order')->orderBy('name')->get();

        // Get cart item count for the user dashboard
        $cartItemCount = 0;
        $purchasedProductIds = [];

        if (Auth::check()) {
            // Get cart items count
            $cart = Cart::where('user_id', Auth::id())->first();
            if ($cart) {
                $cartItemCount = $cart->items->count();
            }

            // Get purchased products
            $purchasedProductIds = Order::where('buyer_id', Auth::id())
                ->where('status', 'success')
                ->pluck('product_id')
                ->toArray();
        } else {
            // For guest users, get cart from session
            $sessionId = session()->get('cart_session_id');
            if ($sessionId) {
                $cart = Cart::where('session_id', $sessionId)->first();
                if ($cart) {
                    $cartItemCount = $cart->items->count();
                }
            }
        }

        return view('users.browse', compact(
            'products',
            'categories',
            'subcategories',
            'detailedCategories',
            'cartItemCount',
            'purchasedProductIds'
        ));
    }

    /**
     * Display the success stories page.
     *
     * @return \Illuminate\View\View
     */
    public function successStories()
    {
        // Get some successful sellers to showcase
        $successfulSellers = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function($q) {
                $q->where('status', 'approved');
            })
            ->withCount(['products' => function($q) {
                $q->where('status', 'active');
            }])
            ->orderByDesc('products_count')
            ->take(6)
            ->get();

        return view('pages.success-stories', compact('successfulSellers'));
    }

    /**
     * Display the about us page.
     *
     * @return \Illuminate\View\View
     */
    public function aboutUs()
    {
        // Get some stats for the about page
        $productCount = Product::where('status', 'active')->count();
        $sellerCount = User::where('is_seller', true)->count();
        $userCount = User::count();

        return view('pages.about', compact('productCount', 'sellerCount', 'userCount'));
    }

    /**
     * Display the blog page.
     *
     * @return \Illuminate\View\View
     */
    public function blog()
    {
        // In a real application, you would fetch blog posts from a database
        // For now, we'll use dummy data
        $blogPosts = [
            [
                'title' => 'How to Create and Sell Digital Products',
                'excerpt' => 'Learn the step-by-step process of creating and selling digital products on Digitora.',
                'author' => 'Digitora Team',
                'date' => now()->subDays(5),
                'image' => 'https://via.placeholder.com/800x400?text=Digital+Products',
                'slug' => 'how-to-create-and-sell-digital-products',
            ],
            [
                'title' => 'Top 10 Digital Products to Sell in 2024',
                'excerpt' => 'Discover the most profitable digital products to sell this year and how to create them.',
                'author' => 'Marketing Team',
                'date' => now()->subDays(10),
                'image' => 'https://via.placeholder.com/800x400?text=Top+Digital+Products',
                'slug' => 'top-10-digital-products-to-sell',
            ],
            [
                'title' => 'Success Story: From $0 to $10,000 in Digital Sales',
                'excerpt' => 'Read how one of our sellers achieved remarkable success selling digital templates.',
                'author' => 'Success Team',
                'date' => now()->subDays(15),
                'image' => 'https://via.placeholder.com/800x400?text=Success+Story',
                'slug' => 'success-story-digital-sales',
            ],
        ];

        return view('pages.blog', compact('blogPosts'));
    }

    /**
     * Display the careers page.
     *
     * @return \Illuminate\View\View
     */
    public function careers()
    {
        // In a real application, you would fetch job openings from a database
        // For now, we'll use dummy data
        $jobOpenings = [
            [
                'title' => 'Senior Laravel Developer',
                'department' => 'Engineering',
                'location' => 'Remote',
                'type' => 'Full-time',
            ],
            [
                'title' => 'UI/UX Designer',
                'department' => 'Design',
                'location' => 'Jakarta, Indonesia',
                'type' => 'Full-time',
            ],
            [
                'title' => 'Digital Marketing Specialist',
                'department' => 'Marketing',
                'location' => 'Remote',
                'type' => 'Full-time',
            ],
        ];

        return view('pages.careers', compact('jobOpenings'));
    }

    /**
     * Display the contact us page.
     *
     * @return \Illuminate\View\View
     */
    public function contact()
    {
        return view('pages.contact');
    }

    /**
     * Process the contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // In a real application, you would send an email or save to database
        // For now, we'll just redirect with a success message

        return redirect()->route('contact')->with('success', 'Your message has been sent successfully! We will get back to you soon.');
    }

    /**
     * Display the help center page.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function helpCenter(Request $request)
    {
        $search = $request->query('search');
        $faqs = Faq::all();
        $articlesPerCategory = 4; // Configurable limit for non-search case

        if ($search) {
            $categories = HelpCategory::with(['articles' => function ($query) use ($search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
            }])->get();
        } else {
            $categories = HelpCategory::with(['articles' => function ($query) use ($articlesPerCategory) {
                $query->take($articlesPerCategory);
            }])->get();
        }

        return view('pages.help-center', compact('categories', 'faqs', 'search'));
    }

    /**
     * Display a specific help article.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function helpArticle($slug)
    {
        $article = HelpArticle::where('slug', $slug)->with('category', 'relatedArticles')->firstOrFail();

        return view('pages.help-article', compact('article'));
    }

    /**
     * Display the FAQ page.
     *
     * @return \Illuminate\View\View
     */
    public function faq()
    {
        $faqs = Faq::all();

        return view('pages.faq', compact('faqs'));
    }
}
