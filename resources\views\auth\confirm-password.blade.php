<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Password - Digitora</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/digitora-logo.png') }}">
    <link href="{{ asset('css/auth.css') }}" rel="stylesheet">
</head>

<body class="auth-page">
    <div class="auth-bg">
        <div class="container mx-auto px-4 py-4">
            <a href="{{ route('home') }}" class="back-link">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span>Back to Home</span>
            </a>
        </div>

        <div class="flex-1 flex items-center justify-center px-4 py-8">
            <div class="auth-card">
                <div class="p-6 text-center">
                    <div class="logo-container">
                        <div class="logo">
                            <span>D</span>
                        </div>
                    </div>
                    <h2 class="text-2xl font-bold mb-2">Confirm Password</h2>
                    <p class="text-gray-600 text-sm">This is a secure area of the application. Please confirm your
                        password before continuing.</p>
                </div>

                <!-- Error Messages -->
                @if ($errors->any())
                    <div class="px-6">
                        <div class="alert alert-error">
                            @foreach ($errors->all() as $error)
                                <p>{{ $error }}</p>
                            @endforeach
                        </div>
                    </div>
                @endif

                <div class="p-6">
                    <form method="POST" action="{{ route('password.confirm') }}" class="auth-form">
                        @csrf

                        <!-- Password -->
                        <div class="form-group">
                            <label for="password">Password</label>
                            <div class="password-field">
                                <input id="password" name="password" type="password" placeholder="••••••••" required
                                    autocomplete="current-password">
                                <button type="button" class="password-toggle">
                                    <svg class="eye-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                    <svg class="eye-off-icon hidden" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.977 9.977 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.542 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-full py-3">
                            Confirm Password
                        </button>
                    </form>
                </div>

                <div class="auth-footer">
                    <p>
                        <a href="{{ route('home') }}">Cancel and return to home</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ asset(js_path() . '/auth.js') }}"></script>
</body>

</html>
