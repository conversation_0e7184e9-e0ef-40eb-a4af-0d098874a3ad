@extends('layouts.browse')

@section('title', 'Purchase ' . $course->title)

@push('styles')
<style>
.purchase-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.purchase-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.purchase-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.purchase-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.purchase-subtitle {
    opacity: 0.9;
    margin: 0;
}

.purchase-content {
    padding: 2rem;
}

.course-info {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.course-thumbnail {
    width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 0.375rem;
    flex-shrink: 0;
}

.course-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.5rem 0;
}

.course-details p {
    color: #6b7280;
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
}

.price-section {
    background: #f9fafb;
    padding: 1.5rem;
    border-radius: 0.375rem;
    margin-bottom: 2rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.price-row:last-child {
    margin-bottom: 0;
    padding-top: 0.5rem;
    border-top: 1px solid #e5e7eb;
    font-weight: 600;
}

.original-price {
    text-decoration: line-through;
    color: #9ca3af;
}

.discount-price {
    color: #059669;
    font-weight: 600;
}

.total-price {
    font-size: 1.25rem;
    color: #111827;
}

.purchase-actions {
    display: flex;
    gap: 1rem;
    flex-direction: column;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
    color: white;
    text-decoration: none;
}

.pending-order {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.pending-order h4 {
    color: #92400e;
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
}

.pending-order p {
    color: #92400e;
    margin: 0;
    font-size: 0.875rem;
}

@media (max-width: 640px) {
    .purchase-container {
        margin: 1rem auto;
        padding: 0 0.5rem;
    }
    
    .purchase-content {
        padding: 1.5rem;
    }
    
    .course-info {
        flex-direction: column;
    }
    
    .course-thumbnail {
        width: 100%;
        height: 120px;
    }
}
</style>
@endpush

@section('content')
<div class="purchase-container">
    <div class="purchase-card">
        <div class="purchase-header">
            <h1 class="purchase-title">Complete Your Purchase</h1>
            <p class="purchase-subtitle">You're about to purchase this course</p>
        </div>
        
        <div class="purchase-content">
            @if($pendingOrder)
                <div class="pending-order">
                    <h4>Pending Payment Found</h4>
                    <p>You have a pending payment for this course. You can continue with the previous payment or create a new one.</p>
                </div>
            @endif

            <div class="course-info">
                @if($course->thumbnail)
                    <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="course-thumbnail">
                @else
                    <div class="course-thumbnail" style="background: #f3f4f6; display: flex; align-items: center; justify-content: center;">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                @endif
                
                <div class="course-details">
                    <h3>{{ $course->title }}</h3>
                    <p>by {{ $course->seller->name }}</p>
                    <p>{{ $course->difficulty_level }} Level</p>
                    @if($course->estimated_duration)
                        <p>{{ $course->estimated_duration }} minutes</p>
                    @endif
                </div>
            </div>

            <div class="price-section">
                @if($course->discount_price > 0 && $course->discount_price < $course->price)
                    <div class="price-row">
                        <span>Original Price:</span>
                        <span class="original-price">Rp {{ number_format($course->price, 0, ',', '.') }}</span>
                    </div>
                    <div class="price-row">
                        <span>Discount:</span>
                        <span class="discount-price">-Rp {{ number_format($course->price - $course->discount_price, 0, ',', '.') }}</span>
                    </div>
                    <div class="price-row">
                        <span>Total:</span>
                        <span class="total-price">Rp {{ number_format($course->discount_price, 0, ',', '.') }}</span>
                    </div>
                @else
                    <div class="price-row">
                        <span>Total:</span>
                        <span class="total-price">Rp {{ number_format($course->price, 0, ',', '.') }}</span>
                    </div>
                @endif
            </div>

            <div class="purchase-actions">
                @if($pendingOrder)
                    <button onclick="continuePayment('{{ $pendingOrder->snap_token }}')" class="btn btn-primary">
                        Continue Previous Payment
                    </button>
                    <button onclick="createNewPayment()" class="btn btn-secondary">
                        Create New Payment
                    </button>
                @else
                    <button onclick="processPayment()" class="btn btn-primary">
                        Pay Now
                    </button>
                @endif
                
                <a href="{{ route('browse.courses.show', $course) }}" class="btn btn-secondary">
                    Back to Course
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://app.sandbox.midtrans.com/snap/snap.js" data-client-key="{{ config('services.midtrans.clientKey') }}"></script>
<script>
function processPayment() {
    // Show loading state
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Processing...';
    button.disabled = true;

    fetch('{{ route("course.purchase", $course) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error: ' + data.error);
            button.textContent = originalText;
            button.disabled = false;
            return;
        }

        // Open Midtrans payment popup
        snap.pay(data.snap_token, {
            onSuccess: function(result) {
                window.location.href = '{{ route("browse.courses.access", $course) }}';
            },
            onPending: function(result) {
                alert('Payment is pending. Please complete your payment.');
                window.location.reload();
            },
            onError: function(result) {
                alert('Payment failed. Please try again.');
                button.textContent = originalText;
                button.disabled = false;
            },
            onClose: function() {
                button.textContent = originalText;
                button.disabled = false;
            }
        });
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred. Please try again.');
        button.textContent = originalText;
        button.disabled = false;
    });
}

function continuePayment(snapToken) {
    snap.pay(snapToken, {
        onSuccess: function(result) {
            window.location.href = '{{ route("browse.courses.access", $course) }}';
        },
        onPending: function(result) {
            alert('Payment is pending. Please complete your payment.');
            window.location.reload();
        },
        onError: function(result) {
            alert('Payment failed. Please try again.');
        }
    });
}

function createNewPayment() {
    if (confirm('This will cancel your previous payment. Are you sure?')) {
        processPayment();
    }
}
</script>
@endpush
