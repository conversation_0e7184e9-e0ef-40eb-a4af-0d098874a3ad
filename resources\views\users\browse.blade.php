@extends('layouts.user-dashboard')

@push('scripts')
<script>
    $(document).ready(function() {
        // Handle category change
        $('#category').change(function() {
            var selectedCategory = $(this).val();

            // Filter subcategories
            $('#subcategory option').each(function() {
                if ($(this).val() === '' || $(this).data('category') === selectedCategory) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });

            // Reset subcategory selection
            $('#subcategory').val('');

            // Reset detailed category selection
            $('#detailed_category').val('');

            // Trigger subcategory change to update detailed categories
            $('#subcategory').trigger('change');
        });

        // Handle subcategory change
        $('#subcategory').change(function() {
            var selectedSubcategory = $(this).val();

            // Filter detailed categories
            $('#detailed_category option').each(function() {
                if ($(this).val() === '' || $(this).data('subcategory') === selectedSubcategory) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });

            // Reset detailed category selection if subcategory changed
            $('#detailed_category').val('');
        });

        // Initial trigger to set up the cascading dropdowns
        $('#category').trigger('change');
    });
</script>
@endpush

@section('content')
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Browse Products</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Discover digital products from our marketplace</p>
        </div>
    </div>

    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
        <!-- Search and Filter Form -->
        <form action="{{ route('user.browse.products') }}" method="GET" class="mb-6">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <div class="relative rounded-md shadow-sm">
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                               class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-4 pr-10 py-2 sm:text-sm border-gray-300 rounded-md"
                               placeholder="Search products...">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select id="category" name="category" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->slug }}" {{ request('category') == $category->slug ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="subcategory" class="block text-sm font-medium text-gray-700 mb-1">Subcategory</label>
                    <select id="subcategory" name="subcategory" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="">All Subcategories</option>
                        @foreach($subcategories ?? [] as $subcategory)
                            <option value="{{ $subcategory->slug }}"
                                {{ request('subcategory') == $subcategory->slug ? 'selected' : '' }}
                                data-category="{{ $subcategory->category->slug }}">
                                {{ $subcategory->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="detailed_category" class="block text-sm font-medium text-gray-700 mb-1">Detailed Category</label>
                    <select id="detailed_category" name="detailed_category" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="">All Detailed Categories</option>
                        @foreach($detailedCategories ?? [] as $detailedCategory)
                            <option value="{{ $detailedCategory->slug }}"
                                {{ request('detailed_category') == $detailedCategory->slug ? 'selected' : '' }}
                                data-subcategory="{{ $detailedCategory->subcategory->slug }}">
                                {{ $detailedCategory->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select id="sort" name="sort" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                        <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                    </select>
                </div>
            </div>

            <div class="mt-4 flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Apply Filters
                </button>
                @if(request('search') || request('category') || request('subcategory') || request('detailed_category') || (request('sort') && request('sort') != 'newest'))
                    <a href="{{ route('user.browse.products') }}" class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Clear Filters
                    </a>
                @endif
            </div>
        </form>

        <!-- Products Grid -->
        @if($products->count() > 0)
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($products as $product)
                    <div class="group relative bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                        @php
                            $storeSlug = null;
                            if ($product->seller && $product->seller->sellerApplication) {
                                $storeSlug = $product->seller->sellerApplication->store_name_slug;
                            }
                        @endphp

                        @if($storeSlug)
                            <a href="{{ route('store.product', ['storeNameSlug' => $storeSlug, 'product' => $product->slug]) }}" class="block">
                        @else
                            <div class="block">
                        @endif
                            <div class="aspect-w-3 aspect-h-2 bg-gray-200 overflow-hidden">
                                <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                     alt="{{ $product->name }}"
                                     class="w-full h-full object-center object-cover group-hover:opacity-90 transition-opacity">
                                @if($product->discount_price)
                                    <div class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                                        {{ $product->getDiscountPercentageAttribute() }}% OFF
                                    </div>
                                @endif
                            </div>
                        @if($storeSlug)
                            </a>
                        @else
                            </div>
                        @endif
                        <div class="p-4">
                            <h3 class="text-sm font-medium text-gray-900 mb-1">
                                @if($storeSlug)
                                    <a href="{{ route('store.product', ['storeNameSlug' => $storeSlug, 'product' => $product->slug]) }}" class="hover:text-indigo-600">
                                        {{ $product->name }}
                                    </a>
                                @else
                                    {{ $product->name }}
                                @endif
                            </h3>
                            <p class="text-xs text-gray-500 mb-2">{{ $product->getDetailedCategoryNameAttribute() ?? $product->getSubcategoryNameAttribute() ?? $product->getCategoryNameAttribute() }}</p>
                            <div class="flex justify-between items-center">
                                <div>
                                    @if($product->discount_price)
                                        <span class="text-gray-500 text-xs line-through">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                                        <span class="text-indigo-600 font-medium">Rp {{ number_format($product->discount_price, 0, ',', '.') }}</span>
                                    @else
                                        <span class="text-indigo-600 font-medium">Rp {{ number_format($product->price, 0, ',', '.') }}</span>
                                    @endif
                                </div>
                                <div class="flex items-center">
                                    @if($product->average_rating)
                                        <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        <span class="text-xs text-gray-500 ml-1">{{ number_format($product->average_rating, 1) }}</span>
                                    @else
                                        <span class="text-xs text-gray-500">No ratings</span>
                                    @endif
                                </div>
                            </div>
                            <div class="mt-4">
                                @if(isset($purchasedProductIds) && in_array($product->id, $purchasedProductIds))
                                    <a href="{{ route('user.purchases') }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        View Purchase
                                    </a>
                                @else
                                    <form action="{{ route('cart.add') }}" method="POST">
                                        @csrf
                                        <input type="hidden" name="product_id" value="{{ $product->id }}">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                            <svg class="h-4 w-4 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                            </svg>
                                            Add to Cart
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8 text-center">
                <div class="pagination-container">
                    {{ $products->withQueryString()->links('vendor.pagination.custom-tailwind') }}
                </div>
            </div>
        @else
            <div class="text-center py-12">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter to find what you're looking for.</p>
                <div class="mt-6">
                    <a href="{{ route('user.browse') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Clear Filters
                    </a>
                </div>
            </div>
        @endif

        <!-- Browse More Categories -->
        <div class="mt-16 text-center">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Browse by Subcategory</h2>
            <div class="flex flex-wrap justify-center gap-3">
                @foreach($subcategories as $subcategory)
                    <a href="{{ route('user.browse', ['subcategory' => $subcategory->slug]) }}"
                        class="px-5 py-2 rounded-full {{ request('subcategory') == $subcategory->slug ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }} transition-colors duration-300 font-medium text-sm">
                        {{ $subcategory->name }}
                    </a>
                @endforeach
            </div>
        </div>

        <!-- Call to Action -->
        <div class="mt-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl p-8 text-center text-white">
            <h2 class="text-2xl font-bold mb-4">Ready to Start Selling Your Digital Products?</h2>
            <p class="mb-6 max-w-2xl mx-auto">Join thousands of creators who are earning by selling their digital products on Digitora.</p>
            <a href="{{ route('seller.apply') }}" class="inline-block bg-white text-indigo-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
                Become a Seller Today
            </a>
        </div>
    </div>
</div>
@endsection
