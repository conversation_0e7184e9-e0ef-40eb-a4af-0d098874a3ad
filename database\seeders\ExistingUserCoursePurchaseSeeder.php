<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\Order;
use Illuminate\Support\Str;

class ExistingUserCoursePurchaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder adds course purchases to existing UserSeeder accounts.
     */
    public function run(): void
    {
        $this->command->info('Adding course purchases to existing user accounts...');

        // Get existing users from UserSeeder
        $customerA = User::where('email', '<EMAIL>')->first();
        $customerB = User::where('email', '<EMAIL>')->first();
        $customerC = User::where('email', '<EMAIL>')->first();
        $customerD = User::where('email', '<EMAIL>')->first();

        if (!$customerA || !$customerB || !$customerC || !$customerD) {
            $this->command->error('Some customer accounts not found. Please run UserSeeder first.');
            return;
        }

        // Get available courses
        $whatsappCourse = Course::where('title', 'LIKE', '%WhatsApp Bot%')->first();
        $webDevCourse = Course::where('title', 'LIKE', '%Web Development%')->first();
        $aiCourse = Course::where('title', 'LIKE', '%AI Automation%')->first();
        $marketingCourse = Course::where('title', 'LIKE', '%Digital Marketing%')->first();

        if (!$whatsappCourse || !$webDevCourse) {
            $this->command->error('Required courses not found. Please run CourseSeeder first.');
            return;
        }

        // Customer A: Has purchased WhatsApp Bot and Web Development courses
        $this->createSuccessfulOrder($customerA, $whatsappCourse, 'Customer A purchased WhatsApp Bot course');
        $this->createSuccessfulOrder($customerA, $webDevCourse, 'Customer A purchased Web Development course');

        // Customer B: Has purchased only WhatsApp Bot course
        $this->createSuccessfulOrder($customerB, $whatsappCourse, 'Customer B purchased WhatsApp Bot course');

        // Customer C: Has purchased AI Automation course (if available)
        if ($aiCourse) {
            $this->createSuccessfulOrder($customerC, $aiCourse, 'Customer C purchased AI Automation course');
        } else {
            // Fallback to Web Development if AI course not available
            $this->createSuccessfulOrder($customerC, $webDevCourse, 'Customer C purchased Web Development course');
        }

        // Customer D: No purchases (for testing access denied flow)
        $this->command->info('Customer D has no purchases (for testing access denied flow)');

        $this->command->info('');
        $this->command->info('🎉 Course purchases added successfully!');
        $this->command->info('');
        $this->command->info('📋 UPDATED TEST ACCOUNT CREDENTIALS:');
        $this->command->info('=====================================');
        $this->command->info('');
        
        $this->displayUserPurchases($customerA);
        $this->displayUserPurchases($customerB);
        $this->displayUserPurchases($customerC);
        $this->displayUserPurchases($customerD);

        $this->command->info('🔗 TESTING URLS:');
        $this->command->info('================');
        $this->command->info('Login Page: http://digitora.test/login');
        $this->command->info('Course Browse: http://digitora.test/browse/courses');
        $this->command->info('');

        // Display course access URLs
        $courses = Course::whereIn('title', [
            'Complete WhatsApp Bot Development Course',
            'Complete Web Development Bootcamp',
            'AI Automation for Small Business',
            'Digital Marketing Mastery'
        ])->get();

        foreach ($courses as $course) {
            $this->command->info("📚 {$course->title}");
            $this->command->info("   Detail: http://digitora.test/browse/courses/{$course->slug}");
            $this->command->info("   Access: http://digitora.test/browse/courses/{$course->slug}/access");
            $this->command->info('');
        }

        $this->command->info('💡 TESTING INSTRUCTIONS:');
        $this->command->info('=========================');
        $this->command->info('1. Login with any customer account above');
        $this->command->info('2. Visit course access URLs to test the UX');
        $this->command->info('3. Accounts with purchases will show access granted');
        $this->command->info('4. Customer D (no purchases) will show access denied');
        $this->command->info('5. Test the countdown timers and redirects');
    }

    /**
     * Create a successful order for testing
     */
    private function createSuccessfulOrder($user, $course, $logMessage)
    {
        // Check if order already exists
        $existingOrder = Order::where('buyer_id', $user->id)
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if ($existingOrder) {
            $this->command->info("✓ Order already exists: {$logMessage}");
            return;
        }

        $amount = $course->discount_price > 0 ? $course->discount_price : $course->price;
        
        Order::create([
            'id' => (string) Str::uuid(),
            'order_id' => 'TEST-' . strtoupper(Str::random(8)) . '-' . date('Ymd'),
            'buyer_id' => $user->id,
            'seller_id' => $course->seller_id,
            'course_id' => $course->id,
            'amount' => $amount,
            'status' => 'success',
            'payment_method' => 'test_simulation',
            'snap_token' => null,
            'created_at' => now()->subDays(rand(1, 14)),
            'updated_at' => now()->subDays(rand(1, 14)),
        ]);

        $this->command->info("✓ Created: {$logMessage}");
    }

    /**
     * Display user purchases information
     */
    private function displayUserPurchases($user)
    {
        $this->command->info("👤 {$user->name}");
        $this->command->info("   Email: {$user->email}");
        
        // Extract password from UserSeeder pattern
        $passwordMap = [
            '<EMAIL>' => 'Customer@2024#A!',
            '<EMAIL>' => 'Customer@2024#B!',
            '<EMAIL>' => 'Customer@2024#C!',
            '<EMAIL>' => 'Customer@2024#D!',
        ];
        
        $password = $passwordMap[$user->email] ?? 'Unknown';
        $this->command->info("   Password: {$password}");

        $orders = Order::where('buyer_id', $user->id)
            ->where('status', 'success')
            ->with('course')
            ->get();

        if ($orders->count() > 0) {
            $this->command->info("   Purchased Courses:");
            foreach ($orders as $order) {
                if ($order->course) {
                    $this->command->info("   - {$order->course->title}");
                }
            }
        } else {
            $this->command->info("   Purchased Courses: None (for testing access denied)");
        }
        $this->command->info('');
    }
}
