<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    protected $casts = [
        'amount' => 'integer', // Changed from decimal:2 to integer for Midtrans compatibility
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'buyer_id');
    }

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Check if this order is for a course
     */
    public function isCourseOrder()
    {
        return !is_null($this->course_id);
    }

    /**
     * Check if this order is for a product
     */
    public function isProductOrder()
    {
        return !is_null($this->product_id);
    }

    /**
     * Get the item name (product or course title)
     */
    public function getItemNameAttribute()
    {
        if ($this->isCourseOrder() && $this->course) {
            return $this->course->title;
        } elseif ($this->isProductOrder() && $this->product) {
            return $this->product->name;
        }
        return 'Unknown Item';
    }

    /**
     * Get the item type (Product or Course)
     */
    public function getItemTypeAttribute()
    {
        if ($this->isCourseOrder()) {
            return 'Course';
        } elseif ($this->isProductOrder()) {
            return 'Product';
        }
        return 'Unknown';
    }

    /**
     * Get the purchasable item (either product or course)
     */
    public function getPurchasableItem()
    {
        if ($this->isCourseOrder()) {
            return $this->course;
        }

        if ($this->isProductOrder()) {
            return $this->product;
        }

        return null;
    }

    /**
     * Calculate seller revenue after all fees
     *
     * @return array
     */
    public function calculateSellerRevenue(): array
    {
        $feeService = new \App\Services\FeeCalculationService();
        return $feeService->calculateSellerRevenue($this);
    }

    /**
     * Get the net amount seller receives after all fees
     *
     * @return float
     */
    public function getSellerNetAmount(): float
    {
        $revenue = $this->calculateSellerRevenue();
        return $revenue['net_amount'];
    }

    /**
     * Get formatted seller net amount for display
     *
     * @return string
     */
    public function getFormattedSellerNetAmount(): string
    {
        $feeService = new \App\Services\FeeCalculationService();
        return $feeService->formatCurrency($this->getSellerNetAmount());
    }

    /**
     * Get the total fees deducted from this order
     *
     * @return float
     */
    public function getTotalFees(): float
    {
        $revenue = $this->calculateSellerRevenue();
        return $revenue['total_fees'];
    }

    /**
     * Get formatted total fees for display
     *
     * @return string
     */
    public function getFormattedTotalFees(): string
    {
        $feeService = new \App\Services\FeeCalculationService();
        return $feeService->formatCurrency($this->getTotalFees());
    }

    public function setStatusPending()
    {
        $this->attributes['status'] = 'pending';
        self::save();
    }

    public function setStatusSuccess()
    {
        $this->attributes['status'] = 'success';
        self::save();
    }

    public function setStatusFailed()
    {
        $this->attributes['status'] = 'failed';
        self::save();
    }

    public function setStatusExpired()
    {
        $this->attributes['status'] = 'expired';
        self::save();
    }

    public function setStatusCanceled()
    {
        $this->attributes['status'] = 'canceled';
        self::save();
    }
}
