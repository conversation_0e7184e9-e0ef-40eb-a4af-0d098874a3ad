@extends('layouts.browse')

@section('title', 'Browse Stores - Digitora')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-r from-purple-600 to-pink-700 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
            <h1 class="text-4xl font-bold mb-4">Discover Amazing Stores</h1>
            <p class="text-xl text-purple-100 mb-8">Explore stores from verified Indonesian entrepreneurs</p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form method="GET" action="{{ route('browse.stores') }}" class="flex">
                    <input type="text" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search stores..." 
                           class="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <button type="submit" 
                            class="bg-purple-800 hover:bg-purple-900 px-6 py-3 rounded-r-lg font-medium transition-colors">
                        Search
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Results -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
                
                <form method="GET" action="{{ route('browse.stores') }}" id="filter-form">
                    <input type="hidden" name="search" value="{{ request('search') }}">
                    
                    <!-- Category Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="">All Categories</option>
                            @foreach($detailedCategories as $category)
                                <option value="{{ $category->slug }}" {{ request('category') == $category->slug ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <!-- Sort Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                            <option value="products_count" {{ request('sort') == 'products_count' ? 'selected' : '' }}>Most Products</option>
                            <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Store Name</option>
                        </select>
                    </div>
                </form>
                
                @if(request()->hasAny(['search', 'category', 'sort']))
                    <a href="{{ route('browse.stores') }}" 
                       class="inline-flex items-center text-sm text-purple-600 hover:text-purple-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear Filters
                    </a>
                @endif
            </div>
        </div>
        
        <!-- Store Grid -->
        <div class="flex-1">
            <!-- Results Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">
                    @if(request('search'))
                        Search Results for "{{ request('search') }}"
                    @else
                        All Stores
                    @endif
                </h2>
                <p class="text-gray-600">{{ $stores->total() }} stores found</p>
            </div>
            
            @if(count($formattedStores) > 0)
                <!-- Store Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    @foreach($formattedStores as $store)
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                            <!-- Store Header -->
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <!-- Store Logo -->
                                    <div class="w-16 h-16 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0 mr-4">
                                        @if($store['logo'])
                                            <img src="{{ $store['logo'] }}" alt="{{ $store['name'] }}" class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-pink-600">
                                                <span class="text-white font-bold text-lg">{{ substr($store['name'], 0, 1) }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <!-- Store Info -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-semibold text-gray-900 truncate">
                                            <a href="{{ route('store.show', $store['slug']) }}" class="hover:text-purple-600">
                                                {{ $store['name'] }}
                                            </a>
                                        </h3>
                                        @if($store['main_category'])
                                            <p class="text-sm text-purple-600 font-medium">{{ $store['main_category'] }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Store Content -->
                            <div class="p-6">
                                <!-- Description -->
                                @if($store['description'])
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $store['description'] }}</p>
                                @endif
                                
                                <!-- Store Stats -->
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <span>{{ $store['product_count'] }} products</span>
                                    @if($selectedDetailedCategory && $store['category_product_count'] > 0)
                                        <span>{{ $store['category_product_count'] }} in {{ $selectedDetailedCategory->name }}</span>
                                    @endif
                                </div>
                                
                                <!-- Store Owner -->
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 rounded-full overflow-hidden bg-gray-200 mr-3">
                                        @if($store['user_avatar'])
                                            <img src="{{ $store['user_avatar'] }}" alt="{{ $store['user_name'] }}" class="w-full h-full object-cover">
                                        @else
                                            <div class="w-full h-full flex items-center justify-center bg-gray-300">
                                                <span class="text-xs font-medium text-gray-600">{{ substr($store['user_name'], 0, 1) }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-700">{{ $store['user_name'] }}</span>
                                        <div class="text-xs text-gray-500">Store Owner</div>
                                    </div>
                                </div>
                                
                                <!-- Action Button -->
                                <div class="flex space-x-2">
                                    <a href="{{ route('store.show', $store['slug']) }}" 
                                       class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors">
                                        Visit Store
                                    </a>
                                    <a href="{{ route('store.all-products', $store['slug']) }}" 
                                       class="flex-1 border border-purple-600 text-purple-600 hover:bg-purple-50 px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors">
                                        View Products
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $stores->appends(request()->query())->links() }}
                </div>
            @else
                <!-- No Results -->
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No stores found</h3>
                    <p class="text-gray-600 mb-8">
                        We couldn't find any stores matching your criteria. Try adjusting your filters or browse all available stores.
                    </p>
                    <div class="mt-8">
                        <a href="{{ route('browse.stores') }}"
                           class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-base font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 transition-colors">
                            Browse All Stores
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
@endsection
