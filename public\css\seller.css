/* Seller Apply Form Styles */
.seller-apply-form {
    max-width: 700px;
    margin: 0 auto;
}


/* Fix color difference and adjust spacing between navbar and content */
.seller-page.min-h-screen.bg-gradient-to-br {
    padding-top: 1.5rem; /* Reduced padding to fix excessive space */
    padding-bottom: 2rem; /* Reduced bottom padding to fix excessive space */
    margin-top: 0; /* Remove any margin that might cause color gap */
    background-image: linear-gradient(to bottom right, #eef2ff, #faf5ff, #ffffff);
    min-height: calc(100vh - 60px); /* Adjust based on navbar height */
}

/* Container spacing */
.seller-page .container.max-w-2xl {
    margin-top: 1rem; /* Reduced margin to fix excessive space */
    margin-bottom: 1.5rem; /* Reduced bottom margin to fix excessive space */
    transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Back button spacing */
.seller-page .back-link {
    margin-bottom: 1.5rem; /* Reduced space below back button */
    display: inline-flex; /* Changed to inline-flex for better alignment */
    margin-top: 0.5rem; /* Add a small margin at the top */
    align-items: center; /* Ensure vertical alignment */
    gap: 0.5rem; /* Add consistent gap between icon and text */
}

/* Fix SVG alignment in back link */
.seller-page .back-link svg {
    display: block; /* Ensure proper rendering */
    flex-shrink: 0; /* Prevent SVG from shrinking */
    position: relative; /* Allow for fine-tuning positioning */
    top: 0.5px; /* Slight adjustment to perfectly align with text */
}

.seller-page .back-link span {
    line-height: 1; /* Adjust line height for better alignment */
}

/* Header spacing */
.seller-page .header-section {
    margin-bottom: 2rem; /* Reduced space below header */
    margin-top: 0.5rem; /* Small margin at the top */
}

/* Progress bar spacing */
.seller-page .progress-section {
    margin-bottom: 2.5rem; /* Reduced space below progress bar */
    margin-top: 1rem; /* Add space above progress bar */
}

.seller-apply-form input,
.seller-apply-form select,
.seller-apply-form textarea {
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    border-radius: 10px;
    display: block; /* Ensure inputs are visible */
    padding: 0.85rem 1rem;
    font-size: 0.9rem;
    width: 100%;
    border: 1px solid #e5e7eb;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.seller-apply-form input:hover,
.seller-apply-form select:hover,
.seller-apply-form textarea:hover {
    border-color: #d1d5db;
    background-color: white;
}

.seller-apply-form input:focus,
.seller-apply-form select:focus,
.seller-apply-form textarea:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15);
    outline: none;
    background-color: white;
    transform: translateY(-2px);
}

/* Add a shake animation for validation errors */
@keyframes sellerShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.seller-apply-form .shake-animation {
    animation: sellerShake 0.4s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
}

.seller-apply-form .file\:bg-indigo-50 {
    background-color: #eef2ff;
}

.seller-apply-form .file\:text-indigo-700 {
    color: #4f46e5;
}

.seller-apply-form .hover\:file\:bg-indigo-100:hover {
    background-color: #e0e7ff;
}

/* Button styles */
.seller-page button[type="submit"],
.seller-page button#continue-button,
.seller-page button#back-button {
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    cursor: pointer;
    border-radius: 10px;
    font-weight: 600;
    padding: 0.85rem 1.75rem;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.01em;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.seller-page button[type="submit"]::after,
.seller-page button#continue-button::after,
.seller-page button#back-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.7);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.seller-page button[type="submit"]:focus:not(:active)::after,
.seller-page button#continue-button:focus:not(:active)::after,
.seller-page button#back-button:focus:not(:active)::after {
    animation: sellerRipple 1.2s cubic-bezier(0, 0.5, 0.25, 1);
}

@keyframes sellerRipple {
    0% {
        transform: scale(0, 0);
        opacity: 0.7;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: scale(50, 50);
    }
}

.seller-page button[type="submit"].bg-indigo-600,
.seller-page button#continue-button.bg-indigo-600 {
    background: linear-gradient(135deg, #4f46e5, #6366f1);
    color: white;
}

.seller-page button[type="submit"].bg-indigo-600:hover,
.seller-page button#continue-button.bg-indigo-600:hover {
    background: linear-gradient(135deg, #4338ca, #4f46e5);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(99, 102, 241, 0.25);
}

.seller-page button[type="submit"].bg-indigo-600:active,
.seller-page button#continue-button.bg-indigo-600:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(99, 102, 241, 0.2);
}

.seller-page button#back-button {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    color: #4b5563;
}

.seller-page button#back-button:hover {
    background-color: #f9fafb;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    color: #374151;
    border-color: #d1d5db;
}

.seller-page button#back-button:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Progress steps */
.progress-steps {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

/* Enhanced progress steps */
.flex.justify-between.max-w-md {
    position: relative;
    z-index: 1;
}

.flex.justify-between.max-w-md::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    right: 10%;
    height: 2px;
    background: #e5e7eb;
    z-index: -1;
    transform: translateY(-50%);
}

/* Progress bar styling */
.seller-page .relative.mt-4.max-w-md .absolute.h-1.bg-indigo-600 {
    transition: width 1.8s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
    box-shadow: 0 0 12px rgba(99, 102, 241, 0.4);
    height: 4px; /* Slightly thicker progress bar */
    border-radius: 4px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed); /* Gradient background */
}

/* Step circles */
.seller-page .flex.justify-between.max-w-md .rounded-full {
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    position: relative;
    z-index: 2;
}

.seller-page .flex.justify-between.max-w-md .rounded-full.bg-indigo-600 {
    transform: scale(1.15);
    box-shadow: 0 0 0 6px rgba(99, 102, 241, 0.25), 0 4px 10px rgba(99, 102, 241, 0.2);
}

.seller-page .flex.justify-between.max-w-md .rounded-full:not(.bg-indigo-600) {
    background-color: white;
}

/* Step text */
.seller-page .flex.justify-between.max-w-md .text-xs {
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    font-weight: 500;
    margin-top: 0.75rem;
    font-size: 0.8rem;
}

.seller-page .flex.justify-between.max-w-md .text-indigo-600 {
    font-weight: 600;
    transform: translateY(-2px);
}

/* Success page styles */
.rounded-full.bg-green-100 {
    box-shadow: 0 0 0 8px rgba(74, 222, 128, 0.1);
}

.max-w-md {
    max-width: 28rem;
}

.max-w-md a {
    font-weight: 500;
    transition: all 0.3s ease;
}

.max-w-md a:hover {
    transform: translateY(-2px);
}

/* Enhanced typography and spacing */
.seller-apply-form h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
}

.seller-apply-form p {
    color: #6b7280;
}

.seller-apply-form label {
    font-weight: 600;
    color: #374151;
}

.seller-apply-form input,
.seller-apply-form select,
.seller-apply-form textarea {
    border: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: #374151;
}

.seller-apply-form input::placeholder,
.seller-apply-form textarea::placeholder {
    color: #9ca3af;
}

/* Card styling */
.seller-page .bg-white {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08), 0 5px 10px rgba(0, 0, 0, 0.04);
    border-radius: 16px;
    transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
    animation: sellerFadeIn 0.7s cubic-bezier(0.16, 1, 0.3, 1);
    border: 1px solid rgba(229, 231, 235, 0.5);
    backdrop-filter: blur(8px);
    overflow: hidden;
}

.seller-page .bg-white:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 8px 15px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

@keyframes sellerFadeIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Add a subtle pulse animation to the active step */
@keyframes sellerPulse {
    0% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(99, 102, 241, 0); }
    100% { box-shadow: 0 0 0 0 rgba(99, 102, 241, 0); }
}

.seller-page .flex.justify-between.max-w-md .rounded-full.border-indigo-600:not(.bg-indigo-600) {
    animation: sellerPulse 2s infinite;
}

/* Form sections */
.p-6 {
    transition: all 0.3s ease;
}

/* Form field animations */
.seller-apply-form .mb-4,
.seller-apply-form .mb-6 {
    transition: all 0.3s ease;
}

.seller-apply-form .mb-4:hover,
.seller-apply-form .mb-6:hover {
    transform: translateX(3px);
}

/* Terms section */
.seller-apply-form + div {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.seller-apply-form + div a {
    color: #4f46e5;
    text-decoration: underline;
}

.seller-apply-form + div a:hover {
    color: #4338ca;
}

/* Non-editable field styling */
.seller-apply-form input[readonly] {
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #6b7280;
    box-shadow: none;
    cursor: not-allowed;
}

.seller-apply-form input[readonly]:hover,
.seller-apply-form input[readonly]:focus {
    border-color: #d1d5db;
    box-shadow: none;
    transform: none;
    background-color: #f3f4f6;
}

/* Lock icon styling */
.seller-apply-form .relative .absolute svg {
    color: #9ca3af;
    transition: all 0.3s ease;
}

.seller-apply-form .relative:hover .absolute svg {
    color: #6b7280;
}

/* Pending page specific styles */
.seller-pending-page {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
    margin-top: 0;
    margin-bottom: 0;
}

/* Pending page card */
.seller-pending-page .max-w-md {
    margin-top: 0;
    margin-bottom: 0;
}







