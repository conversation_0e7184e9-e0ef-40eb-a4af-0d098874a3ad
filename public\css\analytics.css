/* Analytics specific styles */

/* Chart containers */
.chart-container {
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border-radius: 0.75rem;
    overflow: hidden;
}

.chart-container:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Time range buttons */
.time-range-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background-color: #f9fafb;
    color: #4b5563;
    border: 1px solid #e5e7eb;
}

.time-range-btn:hover {
    background-color: #f3f4f6;
    transform: translateY(-1px);
}

.time-range-btn.active {
    background-color: #e0e7ff;
    color: #4f46e5;
    border-color: #c7d2fe;
    font-weight: 600;
}

/* Stat cards for analytics */
.analytics-stat-card {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease-out forwards;
}

.analytics-stat-card:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.analytics-stat-card h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.analytics-stat-card .value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
}

.analytics-stat-card:hover .value {
    transform: scale(1.05);
}

.analytics-stat-card p {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Category badges */
.category-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.category-badge.digital {
    background-color: #dbeafe;
    color: #1e40af;
}

.category-badge.physical {
    background-color: #d1fae5;
    color: #065f46;
}

.category-badge.service {
    background-color: #ede9fe;
    color: #5b21b6;
}

.category-badge.other {
    background-color: #f3f4f6;
    color: #374151;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .time-range-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }

    .analytics-stat-card {
        padding: 1.25rem;
    }

    .analytics-stat-card .value {
        font-size: 1.25rem;
    }
}
