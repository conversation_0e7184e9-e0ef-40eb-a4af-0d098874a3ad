@extends('layouts.browse')

@section('title', 'Course Access Denied')

@push('styles')
<style>
.access-denied-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.access-denied-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    max-width: 600px;
    width: 100%;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.access-denied-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.access-denied-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.access-denied-message {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.course-info {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #667eea;
}

.course-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.course-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.countdown-container {
    background: #edf2f7;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.countdown-text {
    color: #4a5568;
    margin-bottom: 1rem;
}

.countdown-timer {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    font-family: 'Courier New', monospace;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: white;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

@media (max-width: 640px) {
    .access-denied-card {
        padding: 2rem;
        margin: 1rem;
    }
    
    .access-denied-title {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
</style>
@endpush

@section('content')
<div class="access-denied-container">
    <div class="access-denied-card">
        <!-- Icon -->
        <div class="access-denied-icon">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10M12 3C7.03 3 3 7.03 3 12s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9z"></path>
            </svg>
        </div>

        <!-- Title -->
        <h1 class="access-denied-title">Course Access Required</h1>

        <!-- Message -->
        <p class="access-denied-message">
            You need to purchase this course to access its content. Once you complete your purchase, you'll have unlimited access to all course materials, including videos, documents, and downloadable resources.
        </p>

        <!-- Course Info -->
        @if(isset($course))
        <div class="course-info">
            <div class="course-title">{{ $course->title }}</div>
            <div class="course-price">
                @if($course->discount_price > 0)
                    <span>Rp {{ number_format($course->discount_price, 0, ',', '.') }}</span>
                    <span style="text-decoration: line-through; color: #a0aec0; font-size: 1rem; margin-left: 0.5rem;">
                        Rp {{ number_format($course->price, 0, ',', '.') }}
                    </span>
                @else
                    Rp {{ number_format($course->price, 0, ',', '.') }}
                @endif
            </div>
        </div>
        @endif

        <!-- Countdown -->
        <div class="countdown-container">
            <div class="countdown-text">Redirecting you back to the course page in:</div>
            <div class="countdown-timer" id="countdown">10</div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            @if(isset($course))
                <a href="{{ route('browse.courses.show', $course) }}" class="btn btn-primary">
                    View Course Details
                </a>
            @endif
            <a href="{{ route('browse.courses') }}" class="btn btn-secondary">
                Browse All Courses
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let countdown = 10;
    const countdownElement = document.getElementById('countdown');
    const redirectUrl = @if(isset($course)) "{{ route('browse.courses.show', $course) }}" @else "{{ route('browse.courses') }}" @endif;
    
    const timer = setInterval(function() {
        countdown--;
        countdownElement.textContent = countdown;
        
        if (countdown <= 0) {
            clearInterval(timer);
            window.location.href = redirectUrl;
        }
    }, 1000);
    
    // Allow users to cancel the redirect by clicking anywhere
    document.addEventListener('click', function() {
        clearInterval(timer);
        countdownElement.textContent = 'Redirect cancelled';
        countdownElement.style.color = '#a0aec0';
    });
});
</script>
@endsection
