<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name')->unique(); // e.g., 'Manage Products', 'View Analytics', 'Approve Sellers'
            $table->string('slug')->unique(); // e.g., 'manage-products', 'view-analytics', 'approve-sellers'
            $table->text('description')->nullable();
            $table->string('category')->nullable(); // e.g., 'product', 'user', 'admin'
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permissions');
    }
};
