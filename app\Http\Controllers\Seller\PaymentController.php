<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Payment;
use App\Models\SellerApplication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $status = $request->get('status', 'all');
        
        $query = Payment::where('seller_id', Auth::id())
            ->with('order');
            
        if ($status !== 'all') {
            $query->where('status', $status);
        }
        
        $payments = $query->orderBy('created_at', 'desc')
            ->paginate(10);
            
        // Calculate actual earnings using the fee service
        $feeService = new \App\Services\FeeCalculationService();

        // Get all successful orders for this seller
        $successfulOrders = \App\Models\Order::where('seller_id', Auth::id())
            ->where('status', 'success')
            ->get();

        $revenueData = $feeService->calculateTotalSellerRevenue($successfulOrders);

        $totalEarnings = $revenueData['total_net_amount'];
        $pendingPayments = $revenueData['total_net_amount']; // For now, treating all as pending
        $meetsMinimumPayout = $revenueData['meets_minimum_payout'];
        $minimumThreshold = $feeService::MINIMUM_PAYOUT_THRESHOLD;
            
        $nextPayoutDate = Carbon::now()->startOfMonth()->addMonth()->format('F d, Y');
        
        return view('seller.payments.index', compact('payments', 'status', 'totalEarnings', 'pendingPayments', 'nextPayoutDate', 'meetsMinimumPayout', 'minimumThreshold'));
    }


    /**
     * Display the specified resource.
     */
    public function show(Payment $payment)
    {
        // Ensure the payment belongs to the seller
        if ($payment->seller_id !== Auth::id()) {
            abort(403);
        }
        
        $payment->load(['order', 'order.product', 'order.user']);
        
        return view('seller.payments.detail', compact('payment'));
    }
    
    /**
     * Download the payment receipt.
     */
    public function downloadReceipt(Payment $payment)
    {
        // Ensure the payment belongs to the seller
        if ($payment->seller_id !== Auth::id()) {
            abort(403);
        }
        
        // In a real application, you would generate a PDF receipt here
        // For this example, we'll just return a message
        // TODO: Implement PDF receipt generation
        return back()->with('info', 'Receipt download functionality would be implemented here.');
    }
    
    /**
     * Update payment method.
     */
    public function updatePaymentMethod(Request $request)
    {
        $user = Auth::user();
        if (!$user instanceof \App\Models\User) {
            abort(500, 'Authenticated user is not valid.');
        }

        $sellerApplication = SellerApplication::where('user_id', $user->id)
            ->where('status', 'approved')
            ->first();

        if (!$sellerApplication) {
            return redirect()->route('seller.payments.index')
                ->with('error', 'No approved seller application found.');
        }

        $rules = [
            'payment_method' => 'required|in:bank_transfer,paypal,stripe',
        ];

        if ($request->payment_method === 'bank_transfer') {
            $rules = array_merge($rules, [
                'bank_name' => 'required|string|max:255',
                'account_number' => 'required|string|max:255',
                'account_holder_name' => 'required|string|max:255',
            ]);
        } elseif ($request->payment_method === 'paypal') {
            $rules = array_merge($rules, [
                'paypal_email' => 'required|email|max:255',
            ]);
        } elseif ($request->payment_method === 'stripe') {
            $rules = array_merge($rules, [
                'stripe_account_id' => 'required|string|max:255',
            ]);
        }

        $validated = $request->validate($rules);

        $sellerApplication->payment_method = $validated['payment_method'];

        if ($validated['payment_method'] === 'bank_transfer') {
            $sellerApplication->bank_name = $validated['bank_name'];
            $sellerApplication->account_number = $validated['account_number'];
            $sellerApplication->account_holder_name = $validated['account_holder_name'];
        } else {
            // Clear bank details if switching to PayPal or Stripe
            $sellerApplication->bank_name = null;
            $sellerApplication->account_number = null;
            $sellerApplication->account_holder_name = null;
        }

        // TODO: Store PayPal email or Stripe account ID if needed (e.g., in a new column or JSON field)
        $sellerApplication->save();
        
        return redirect()->route('seller.payments.index')
            ->with('success', 'Payment method updated successfully!');
    }
}