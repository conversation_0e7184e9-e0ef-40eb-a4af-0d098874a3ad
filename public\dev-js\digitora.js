document.addEventListener('DOMContentLoaded', () => {
    // Navigation Menu Toggle
    const navMenuButton = document.getElementById('nav-menu-button');
    const navMenu = document.getElementById('nav-menu');

    if (navMenuButton && navMenu) {
        navMenuButton.addEventListener('click', () => {
            navMenu.classList.toggle('hidden');
            navMenu.classList.toggle('open');
            navMenuButton.setAttribute('aria-expanded', navMenu.classList.contains('open'));
        });
    }

    // Category Filter for Featured Products
    const filterButtons = document.querySelectorAll('.category-filter');
    const productsGrid = document.querySelector('.products-grid');

    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active state from all buttons
                filterButtons.forEach(btn => {
                    btn.classList.remove('bg-purple-100', 'text-purple-700');
                    btn.classList.add('bg-gray-100', 'text-gray-700');
                });

                // Add active state to clicked button
                button.classList.remove('bg-gray-100', 'text-gray-700');
                button.classList.add('bg-purple-100', 'text-purple-700');

                // Add animation to products grid
                if (productsGrid) {
                    productsGrid.classList.add('opacity-50');
                    setTimeout(() => {
                        productsGrid.classList.remove('opacity-50');
                        // Placeholder for filtering logic
                        const filter = button.dataset.filter;
                        console.log(`Filtering by: ${filter}`);
                    }, 300);
                }
            });
        });
    }

    // Intersection Observer for Animations
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    if (animatedElements.length > 0 && 'IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.15 });

        animatedElements.forEach(element => observer.observe(element));
    }

    // Add Shadow to Header on Scroll
    const header = document.querySelector('header');
    if (header) {
        window.addEventListener('scroll', () => {
            if (window.scrollY > 20) {
                header.classList.add('shadow-md');
                header.classList.remove('shadow-sm');
            } else {
                header.classList.remove('shadow-md');
                header.classList.add('shadow-sm');
            }
        });
    }

    // Smooth Scroll for Anchor Links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Search Suggestion (Placeholder for Future Implementation)
    const searchInputs = document.querySelectorAll('input[type="search"]');
    searchInputs.forEach(input => {
        input.addEventListener('input', () => {
            const query = input.value.trim();
            if (query.length > 2) {
                console.log(`Searching for: ${query}`);
                // You can implement an AJAX call to fetch suggestions here
            }
        });
    });

    // Category card animations
    const categoryCards = document.querySelectorAll('.category-card');
    if (categoryCards.length > 0) {
        categoryCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.classList.add('shadow-md');
            });

            card.addEventListener('mouseleave', () => {
                card.classList.remove('shadow-md');
            });
        });
    }
});