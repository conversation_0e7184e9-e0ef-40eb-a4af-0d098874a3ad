<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateHelpArticleRelatedTable extends Migration
{
    public function up()
    {
        Schema::create('help_article_related', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('article_id')->references('id')->on('help_articles')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignUuid('related_article_id')->references('id')->on('help_articles')->onUpdate('cascade')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('help_article_related');
    }
}
