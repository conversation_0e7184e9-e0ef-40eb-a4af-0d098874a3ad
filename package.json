{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "obfuscate": "javascript-obfuscator public/dev-js --output public/js --compact true --control-flow-flattening false --dead-code-injection false --debug-protection false --debug-protection-interval 0 --disable-console-output true --identifier-names-generator hexadecimal --log false --numbers-to-expressions false --rename-globals false --self-defending true --simplify true --split-strings false --string-array true --string-array-calls-transform false --string-array-encoding none --string-array-index-shift true --string-array-rotate true --string-array-shuffle true --string-array-wrappers-count 1 --string-array-wrappers-chained-calls true --string-array-wrappers-parameters-max-count 2 --string-array-wrappers-type variable --string-array-threshold 0.75 --unicode-escape-sequence false", "obfuscate-commit": "npm run obfuscate && git add public/js/*.js && (git diff --cached --quiet || git commit -m \"Auto-obfuscate JavaScript files\")"}, "devDependencies": {"@tailwindcss/forms": "^0.5.2", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.21", "axios": "^1.6.4", "javascript-obfuscator": "^4.1.1", "laravel-vite-plugin": "^1.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.0.0"}, "dependencies": {"chart.js": "^4.4.9"}}