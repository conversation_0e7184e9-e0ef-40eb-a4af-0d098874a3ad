<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class CourseCurriculumItem extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'course_id',
        'section_id',
        'title',
        'description',
        'type',
        'content',
        'file_path',
        'metadata',
        'sort_order',
        'estimated_duration',
        'is_active',
        'is_preview',
    ];

    protected $casts = [
        'metadata' => 'array',
        'sort_order' => 'integer',
        'estimated_duration' => 'integer',
        'is_active' => 'boolean',
        'is_preview' => 'boolean',
    ];

    // Content type constants (Udemy-style)
    const TYPE_LECTURE = 'lecture';
    const TYPE_VIDEO = 'video';
    const TYPE_PDF = 'pdf';
    const TYPE_DOCUMENT = 'document';

    /**
     * Get the course that owns the curriculum item
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the section that owns the curriculum item
     */
    public function section()
    {
        return $this->belongsTo(CourseSection::class, 'section_id');
    }

    /**
     * Scope to get only active curriculum items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get curriculum items ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope to get preview curriculum items
     */
    public function scopePreview($query)
    {
        return $query->where('is_preview', true);
    }

    /**
     * Scope to filter by content type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the file URL if the curriculum item has a file
     */
    public function getFileUrlAttribute()
    {
        if ($this->file_path && Storage::disk('public')->exists($this->file_path)) {
            return Storage::disk('public')->url($this->file_path);
        }
        return null;
    }

    /**
     * Check if the curriculum item is a video
     */
    public function getIsVideoAttribute()
    {
        return $this->type === self::TYPE_VIDEO;
    }

    /**
     * Check if the curriculum item is a lecture
     */
    public function getIsLectureAttribute()
    {
        return $this->type === self::TYPE_LECTURE;
    }

    /**
     * Check if the curriculum item has a downloadable file
     */
    public function getIsDownloadableAttribute()
    {
        return in_array($this->type, [self::TYPE_PDF, self::TYPE_DOCUMENT]) && !empty($this->file_path);
    }

    /**
     * Get the icon for the curriculum item type
     */
    public function getTypeIconAttribute()
    {
        return match($this->type) {
            self::TYPE_LECTURE => 'fas fa-play-circle',
            self::TYPE_VIDEO => 'fas fa-video',
            self::TYPE_PDF => 'fas fa-file-pdf',
            self::TYPE_DOCUMENT => 'fas fa-file-alt',
            default => 'fas fa-file'
        };
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            if (!$item->sort_order) {
                $maxOrder = self::where('section_id', $item->section_id)->max('sort_order') ?? 0;
                $item->sort_order = $maxOrder + 1;
            }
        });

        static::deleting(function ($item) {
            // Delete associated file if exists
            if ($item->file_path && Storage::disk('public')->exists($item->file_path)) {
                Storage::disk('public')->delete($item->file_path);
            }
        });
    }
}
