<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductDetailedCategory extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'subcategory_id',
        'name',
        'slug',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the subcategory that owns the detailed category.
     */
    public function subcategory()
    {
        return $this->belongsTo(ProductSubcategory::class, 'subcategory_id');
    }

    /**
     * Get the category through the subcategory.
     */
    public function category()
    {
        return $this->hasOneThrough(
            ProductCategory::class,
            ProductSubcategory::class,
            'id', // Foreign key on ProductSubcategory table
            'id', // Foreign key on ProductCategory table
            'subcategory_id', // Local key on ProductDetailedCategory table
            'category_id' // Local key on ProductSubcategory table
        );
    }

    /**
     * Get all products in this detailed category.
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'detailed_category_id');
    }

    /**
     * Get all courses in this detailed category.
     */
    public function courses()
    {
        return $this->hasMany(Course::class, 'detailed_category_id');
    }

    /**
     * Scope a query to only include active detailed categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the full path of the detailed category (breadcrumb).
     */
    public function getPathAttribute()
    {
        return $this->subcategory->category->name . ' > ' . $this->subcategory->name . ' > ' . $this->name;
    }

    /**
     * Get the full slug path of the detailed category.
     */
    public function getFullSlugPathAttribute()
    {
        return $this->subcategory->category->slug . '/' . $this->subcategory->slug . '/' . $this->slug;
    }
}
