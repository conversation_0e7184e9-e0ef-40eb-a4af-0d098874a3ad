<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CourseSectionController extends Controller
{
    /**
     * Store a newly created section in storage.
     */
    public function store(Request $request, Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $validated['course_id'] = $course->id;

        // If no sort_order provided, set it to the next available position
        if (!isset($validated['sort_order'])) {
            $maxOrder = $course->sections()->max('sort_order') ?? 0;
            $validated['sort_order'] = $maxOrder + 1;
        }

        $section = CourseSection::create($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Section created successfully!',
                'section' => $section->fresh()->load('curriculumItems'),
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Section created successfully!');
    }

    /**
     * Update the specified section in storage.
     */
    public function update(Request $request, Course $course, CourseSection $section)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id() || $section->course_id !== $course->id) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'nullable|boolean',
        ]);

        $section->update($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Section updated successfully!',
                'section' => $section->fresh()->load('curriculumItems'),
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Section updated successfully!');
    }

    /**
     * Remove the specified section from storage.
     */
    public function destroy(Course $course, CourseSection $section)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id() || $section->course_id !== $course->id) {
            abort(403);
        }

        $section->delete();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Section deleted successfully!',
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Section deleted successfully!');
    }

    /**
     * Reorder sections for the course.
     */
    public function reorder(Request $request, Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        $validated = $request->validate([
            'sections' => 'required|array',
            'sections.*.id' => 'required|exists:course_sections,id',
            'sections.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['sections'] as $sectionData) {
            CourseSection::where('id', $sectionData['id'])
                ->where('course_id', $course->id)
                ->update(['sort_order' => $sectionData['sort_order']]);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Sections reordered successfully!',
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Sections reordered successfully!');
    }

    /**
     * Toggle the active status of a section.
     */
    public function toggleStatus(Request $request, Course $course, CourseSection $section)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id() || $section->course_id !== $course->id) {
            abort(403);
        }

        // Validate the status input
        $validated = $request->validate([
            'status' => 'required|in:0,1'
        ]);

        $isActive = (bool) $validated['status'];
        
        $section->update([
            'is_active' => $isActive
        ]);

        $status = $isActive ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Section {$status} successfully!",
            'status' => $isActive ? 'active' : 'inactive',
            'section' => $section->fresh()->load('curriculumItems'),
        ]);
    }
}
