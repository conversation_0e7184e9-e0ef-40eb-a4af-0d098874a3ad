<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CartItem extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = ['cart_id', 'product_id', 'course_id', 'quantity', 'price'];

    protected $casts = [
        'price' => 'integer', // Cast price as integer for Midtrans compatibility
        'quantity' => 'integer',
    ];

    /**
     * Get the cart that owns the item.
     */
    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }

    /**
     * Get the product for this cart item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the course for this cart item.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Check if this cart item is for a course
     */
    public function isCourseItem()
    {
        return !is_null($this->course_id);
    }

    /**
     * Check if this cart item is for a product
     */
    public function isProductItem()
    {
        return !is_null($this->product_id);
    }

    /**
     * Get the purchasable item (either product or course)
     */
    public function getPurchasableItem()
    {
        if ($this->isCourseItem()) {
            return $this->course;
        }

        if ($this->isProductItem()) {
            return $this->product;
        }

        return null;
    }

    /**
     * Get the item name
     */
    public function getItemName()
    {
        $item = $this->getPurchasableItem();
        return $item ? ($item->title ?? $item->name) : 'Unknown Item';
    }

    /**
     * Get the subtotal for this cart item.
     */
    public function getSubtotalAttribute()
    {
        // Ensure we return an integer for Midtrans compatibility
        return (int)$this->price * (int)$this->quantity;
    }
}
