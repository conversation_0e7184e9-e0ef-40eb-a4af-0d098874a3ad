/* AI Chat Styles - Professional Chat Widget Design */
.ai-chat-container {
    position: fixed;
    bottom: 24px;
    right: 24px;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.ai-chat-button {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #9333ea 0%, #6366f1 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(147, 51, 234, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.ai-chat-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.ai-chat-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(147, 51, 234, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ai-chat-button:hover::before {
    opacity: 1;
}

.ai-chat-button:active {
    transform: translateY(0);
}

.ai-chat-window {
    position: absolute;
    bottom: 72px;
    right: 0;
    width: 320px;
    height: 400px;
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(16px) scale(0.96);
    pointer-events: none;
    backdrop-filter: blur(8px);
}

.ai-chat-window.active {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

.ai-chat-header {
    background: linear-gradient(135deg, #9333ea 0%, #6366f1 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-chat-header h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.ai-chat-header-actions {
    display: flex;
    gap: 8px;
}

.ai-chat-header-button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
    transition: all 0.2s ease;
    border-radius: 4px;
    width: 28px;
    height: 28px;
}

.ai-chat-header-button:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

.ai-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #fafafa;
}

.ai-chat-messages::-webkit-scrollbar {
    width: 4px;
}

.ai-chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
}

.ai-chat-message {
    max-width: 85%;
    padding: 10px 14px;
    border-radius: 16px;
    line-height: 1.4;
    font-size: 13px;
    position: relative;
    word-wrap: break-word;
}

.ai-chat-message.user {
    background: linear-gradient(135deg, #9333ea 0%, #6366f1 100%);
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
    box-shadow: 0 1px 2px rgba(147, 51, 234, 0.2);
}

.ai-chat-message.ai {
    background-color: #ffffff;
    color: #374151;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Formatting styles for AI messages */
.ai-chat-message.ai strong {
    font-weight: 600;
    color: #1f2937;
}

.ai-chat-message.ai em {
    font-style: italic;
    color: #6b7280;
}

.ai-chat-message.ai br {
    line-height: 1.6;
}

.ai-chat-input-container {
    padding: 16px 20px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    display: flex;
    gap: 8px;
    background: #ffffff;
}

.ai-chat-input {
    flex: 1;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 13px;
    outline: none;
    transition: all 0.2s ease;
    background: #fafafa;
    resize: none;
    font-family: inherit;
}

.ai-chat-input:focus {
    border-color: #9333ea;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
}

.ai-chat-input::placeholder {
    color: #9ca3af;
}

.ai-chat-send-button {
    background: linear-gradient(135deg, #9333ea 0%, #6366f1 100%);
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    min-width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(147, 51, 234, 0.2);
}

.ai-chat-send-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(147, 51, 234, 0.3);
}

.ai-chat-send-button:active {
    transform: translateY(0);
}

.ai-chat-send-button:disabled {
    background: #e5e7eb;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.ai-chat-login-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    text-align: center;
    z-index: 10;
    border-radius: 16px;
}

.ai-chat-login-message {
    margin-bottom: 20px;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
}

.ai-chat-login-button {
    background: linear-gradient(135deg, #9333ea 0%, #6366f1 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(147, 51, 234, 0.2);
}

.ai-chat-login-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3);
}

.ai-chat-typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 10px 14px;
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 16px;
    border-bottom-left-radius: 4px;
    align-self: flex-start;
    margin-top: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ai-chat-typing-dot {
    width: 6px;
    height: 6px;
    background-color: #9333ea;
    border-radius: 50%;
    animation: typing-animation 1.4s infinite ease-in-out;
}

.ai-chat-typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.ai-chat-typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.ai-chat-typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing-animation {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-3px);
        opacity: 1;
    }
}

/* Responsive Design for Mobile and Tablet Devices */
@media (max-width: 768px) {
    .ai-chat-container {
        bottom: 16px;
        right: 16px;
    }

    .ai-chat-button {
        width: 52px;
        height: 52px;
    }

    .ai-chat-button svg {
        width: 20px;
        height: 20px;
    }

    .ai-chat-window {
        position: fixed;
        bottom: 80px;
        right: 16px;
        left: 16px;
        width: auto;
        max-width: 320px;
        height: 360px;
        max-height: calc(100vh - 120px);
        margin-left: auto;
    }

    .ai-chat-header {
        padding: 14px 16px;
    }

    .ai-chat-header h3 {
        font-size: 14px;
    }

    .ai-chat-header-button {
        width: 24px;
        height: 24px;
    }

    .ai-chat-messages {
        padding: 12px 16px;
        gap: 10px;
    }

    .ai-chat-message {
        max-width: 88%;
        padding: 9px 12px;
        font-size: 13px;
    }

    .ai-chat-input-container {
        padding: 12px 16px 16px;
        gap: 8px;
    }

    .ai-chat-input {
        padding: 9px 14px;
        font-size: 13px;
    }

    .ai-chat-send-button {
        width: 34px;
        height: 34px;
        min-width: 34px;
    }

    .ai-chat-send-button svg {
        width: 16px;
        height: 16px;
    }

    .ai-chat-login-overlay {
        padding: 20px;
    }

    .ai-chat-login-message {
        font-size: 13px;
        margin-bottom: 16px;
    }

    .ai-chat-login-button {
        padding: 10px 20px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .ai-chat-container {
        bottom: 12px;
        right: 12px;
    }

    .ai-chat-button {
        width: 48px;
        height: 48px;
    }

    .ai-chat-button svg {
        width: 18px;
        height: 18px;
    }

    .ai-chat-window {
        position: fixed;
        bottom: 72px;
        right: 12px;
        left: 12px;
        width: auto;
        max-width: none;
        height: 320px;
        max-height: calc(100vh - 100px);
        margin: 0;
    }

    .ai-chat-header {
        padding: 12px 14px;
    }

    .ai-chat-header h3 {
        font-size: 13px;
    }

    .ai-chat-header-button {
        width: 22px;
        height: 22px;
    }

    .ai-chat-messages {
        padding: 10px 14px;
        gap: 8px;
    }

    .ai-chat-message {
        max-width: 92%;
        padding: 8px 11px;
        font-size: 12px;
    }

    .ai-chat-input-container {
        padding: 10px 14px 14px;
        gap: 6px;
    }

    .ai-chat-input {
        padding: 8px 12px;
        font-size: 12px;
    }

    .ai-chat-send-button {
        width: 32px;
        height: 32px;
        min-width: 32px;
    }

    .ai-chat-send-button svg {
        width: 14px;
        height: 14px;
    }

    .ai-chat-login-overlay {
        padding: 16px;
    }

    .ai-chat-login-message {
        font-size: 12px;
        margin-bottom: 14px;
    }

    .ai-chat-login-button {
        padding: 9px 18px;
        font-size: 12px;
    }
}

/* Ensure chat doesn't interfere with page content */
@media (max-width: 768px) {
    body {
        padding-bottom: env(safe-area-inset-bottom, 0);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .ai-chat-button {
        box-shadow: 0 4px 16px rgba(147, 51, 234, 0.25), 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .ai-chat-window {
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.06);
    }
}
