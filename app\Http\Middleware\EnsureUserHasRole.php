<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserHasRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $role  The role slug to check for
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!$request->user() || !$request->user()->hasRole($role)) {
            // If it's an AJAX request or expects JSON, return JSON error
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'error' => "You do not have access to this resource. Required role: {$role}",
                    'redirect' => route('home')
                ], 403);
            }

            return redirect()->route('home')->with('error', "You do not have access to this resource. Required role: {$role}");
        }

        return $next($request);
    }
}
