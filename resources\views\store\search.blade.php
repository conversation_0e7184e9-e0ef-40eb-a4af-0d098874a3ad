@extends('store.layout')

@section('content')
<div class="py-8 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumbs -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('store.show', $seller->store_slug) }}" class="text-gray-500 hover:text-indigo-600 text-sm">
                        Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-500 ml-1 text-sm">Search Results</span>
                    </div>
                </li>
            </ol>
        </nav>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Sidebar -->
            <div class="md:col-span-1">
                <div class="bg-white rounded-lg border p-4 sticky top-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Categories</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="{{ route('store.show', $seller->store_slug) }}" class="text-gray-500 hover:text-indigo-600 text-sm">
                                All Products
                            </a>
                        </li>
                        @if(isset($hasNewCategories) && $hasNewCategories)
                            @foreach($categories as $catSlug => $catName)
                            <li>
                                <a href="{{ route('store.category', [$seller->store_slug, $catSlug]) }}" class="text-gray-500 hover:text-indigo-600 text-sm">
                                    {{ $catName }} ({{ $categoryProductCounts[$catSlug] }})
                                </a>
                            </li>
                            @endforeach
                        @else
                            @foreach($categories as $cat)
                            <li>
                                <a href="{{ route('store.category', [$seller->store_slug, $cat]) }}" class="text-gray-500 hover:text-indigo-600 text-sm">
                                    {{ $cat }} ({{ $categoryProductCounts[$cat] }})
                                </a>
                            </li>
                            @endforeach
                        @endif
                    </ul>

                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Price Range</h3>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input id="price-all" name="price" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" checked>
                                <label for="price-all" class="ml-3 text-sm text-gray-600">All Prices</label>
                            </div>
                            <div class="flex items-center">
                                <input id="price-1" name="price" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="price-1" class="ml-3 text-sm text-gray-600">Under Rp 100.000</label>
                            </div>
                            <div class="flex items-center">
                                <input id="price-2" name="price" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="price-2" class="ml-3 text-sm text-gray-600">Rp 100.000 - Rp 250.000</label>
                            </div>
                            <div class="flex items-center">
                                <input id="price-3" name="price" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="price-3" class="ml-3 text-sm text-gray-600">Rp 250.000 - Rp 500.000</label>
                            </div>
                            <div class="flex items-center">
                                <input id="price-4" name="price" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="price-4" class="ml-3 text-sm text-gray-600">Over Rp 500.000</label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Rating</h3>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input id="rating-all" name="rating" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500" checked>
                                <label for="rating-all" class="ml-3 text-sm text-gray-600">All Ratings</label>
                            </div>
                            <div class="flex items-center">
                                <input id="rating-4" name="rating" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="rating-4" class="ml-3 flex items-center text-sm text-gray-600">
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 4; $i++)
                                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endfor
                                        <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    </div>
                                    <span class="ml-1">& Up</span>
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="rating-3" name="rating" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <label for="rating-3" class="ml-3 flex items-center text-sm text-gray-600">
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 3; $i++)
                                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endfor
                                        @for($i = 1; $i <= 2; $i++)
                                            <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endfor
                                    </div>
                                    <span class="ml-1">& Up</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products -->
            <div class="md:col-span-3">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">Search Results for "{{ $query }}"</h1>
                    <p class="text-gray-500 mt-1">{{ $products->total() }} products found</p>
                </div>

                <!-- Filters -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 mr-2">Sort by:</span>
                        <select class="rounded-md border-gray-300 py-1 pl-2 pr-8 text-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option>Newest</option>
                            <option>Price: Low to High</option>
                            <option>Price: High to Low</option>
                            <option>Most Popular</option>
                        </select>
                    </div>
                </div>

                <!-- Products Grid -->
                @if($products->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($products as $product)
                    <div class="group relative card-hover bg-white rounded-xl overflow-hidden shadow-md border border-gray-100 animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="aspect-w-4 aspect-h-3 overflow-hidden bg-gray-100">
                            <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                    alt="{{ $product->name }}"
                                    class="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-110">
                            </a>
                            @if($product->discount_price)
                                <div class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                                    SALE {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% OFF
                                </div>
                            @endif
                        </div>
                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900 line-clamp-1">
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}" class="hover:text-indigo-600 transition-colors duration-300">
                                    {{ $product->name }}
                                </a>
                            </h3>
                            <p class="mt-1 text-sm text-gray-500">
                                @if(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                    {{ $product->productSubcategory->name }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                    {{ $product->productCategory->name }}
                                @else
                                    {{ ucfirst($product->category) }}
                                @endif
                            </p>
                            <div class="mt-3 flex justify-between items-center">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= ($product->average_rating ?? 5))
                                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @else
                                            <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 20 20" fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        @endif
                                    @endfor
                                    <span class="text-xs text-gray-500 ml-1">({{ $product->reviews_count ?? 0 }})</span>
                                </div>
                            </div>
                            <div class="mt-3">
                                @if($product->discount_price)
                                    <p class="text-lg font-bold text-indigo-600">
                                        Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                    <p class="text-sm text-gray-500 line-through">
                                        Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @else
                                    <p class="text-lg font-bold text-indigo-600">
                                        Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @endif
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-4 flex gap-2">
                                <!-- View Details Button (Left) -->
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}"
                                   class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                                    View Details
                                </a>

                                <!-- DigiAI Button (Right) -->
                                @if($product->hasActiveChatbot())
                                <button onclick="openProductChatFromStore('{{ $product->name }}', '{{ $seller->store_slug }}', '{{ $product->slug }}')"
                                        class="inline-flex justify-center items-center px-3 py-2 border border-blue-200 text-xs font-medium rounded-lg text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-300">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                                        <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                                    </svg>
                                    <span class="hidden sm:inline">Ask DigiAI</span>
                                    <span class="sm:hidden">AI</span>
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    <div class="pagination-container">
                        {{ $products->appends(['query' => $query])->links('vendor.pagination.custom-tailwind') }}
                    </div>
                </div>
                @else
                <div class="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M20 12H4" />
                    </svg>
                    <h3 class="mt-2 text-lg font-medium text-gray-900">No products found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter to find what you're looking for.</p>
                    <div class="mt-6">
                        <a href="{{ route('store.show', $seller->store_slug) }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700">
                            View All Products
                        </a>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
