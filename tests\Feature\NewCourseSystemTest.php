<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\CourseSubchapter;
use App\Models\CourseMaterial;
use App\Models\User;
use App\Models\Role;
use App\Models\UserRole;
use App\Models\SellerApplication;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class NewCourseSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $seller;
    protected $sellerApplication;
    protected $category;
    protected $subcategory;
    protected $detailedCategory;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        $sellerRole = Role::create([
            'name' => 'Seller',
            'slug' => 'seller',
            'description' => 'Seller role for testing'
        ]);

        // Create a seller user
        $this->seller = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test Seller'
        ]);

        // Assign seller role
        UserRole::create([
            'user_id' => $this->seller->id,
            'role_id' => $sellerRole->id
        ]);

        // Create seller application
        $this->sellerApplication = SellerApplication::create([
            'user_id' => $this->seller->id,
            'id_type' => 'ktp',
            'id_number' => encrypt('**********123456'),
            'bank_name' => encrypt('Test Bank'),
            'account_number' => encrypt('**********'),
            'account_holder_name' => encrypt('Test Seller'),
            'store_name' => 'Test Store',
            'store_name_slug' => 'test-store',
            'store_description' => 'Test store description',
            'status' => 'approved'
        ]);

        // Create category structure for courses
        $this->category = ProductCategory::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'description' => 'Test category for courses',
            'product_type' => 'course',
            'is_active' => true,
            'sort_order' => 1
        ]);

        $this->subcategory = ProductSubcategory::create([
            'category_id' => $this->category->id,
            'name' => 'Test Subcategory',
            'slug' => 'test-subcategory',
            'description' => 'Test subcategory',
            'legacy_code' => 'test_sub',
            'is_active' => true,
            'sort_order' => 1
        ]);

        $this->detailedCategory = ProductDetailedCategory::create([
            'subcategory_id' => $this->subcategory->id,
            'name' => 'Test Detailed Category',
            'slug' => 'test-detailed-category',
            'description' => 'Test detailed category',
            'is_active' => true,
            'sort_order' => 1
        ]);
    }

    /** @test */
    public function seller_can_create_a_course()
    {
        $this->actingAs($this->seller);

        $courseData = [
            'title' => 'Test Course',
            'description' => 'This is a test course description',
            'short_description' => 'Short description',
            'category_id' => $this->category->id,
            'subcategory_id' => $this->subcategory->id,
            'detailed_category_id' => $this->detailedCategory->id,
            'price' => 50000,
            'difficulty_level' => 'beginner',
            'status' => 'draft'
        ];

        $response = $this->post(route('seller.courses.store'), $courseData);

        $response->assertRedirect();
        $this->assertDatabaseHas('courses', [
            'title' => 'Test Course',
            'seller_id' => $this->seller->id,
            'status' => 'draft'
        ]);
    }

    /** @test */
    public function course_can_have_chapters()
    {
        $course = Course::create([
            'seller_id' => $this->seller->id,
            'title' => 'Test Course',
            'slug' => 'test-course',
            'description' => 'Test description',
            'category_id' => $this->category->id,
            'subcategory_id' => $this->subcategory->id,
            'detailed_category_id' => $this->detailedCategory->id,
            'price' => 50000,
            'difficulty_level' => 'beginner',
            'status' => 'draft'
        ]);

        $chapter = CourseChapter::create([
            'course_id' => $course->id,
            'title' => 'Test Chapter',
            'description' => 'Test chapter description',
            'sort_order' => 1
        ]);

        $this->assertDatabaseHas('course_chapters', [
            'course_id' => $course->id,
            'title' => 'Test Chapter'
        ]);

        $this->assertEquals(1, $course->chapters()->count());
    }

    /** @test */
    public function chapter_can_have_subchapters()
    {
        $course = Course::create([
            'seller_id' => $this->seller->id,
            'title' => 'Test Course',
            'slug' => 'test-course',
            'description' => 'Test description',
            'category_id' => $this->category->id,
            'subcategory_id' => $this->subcategory->id,
            'detailed_category_id' => $this->detailedCategory->id,
            'price' => 50000,
            'difficulty_level' => 'beginner',
            'status' => 'draft'
        ]);

        $chapter = CourseChapter::create([
            'course_id' => $course->id,
            'title' => 'Test Chapter',
            'sort_order' => 1
        ]);

        $subchapter = CourseSubchapter::create([
            'course_id' => $course->id,
            'chapter_id' => $chapter->id,
            'title' => 'Test Subchapter',
            'description' => 'Test subchapter description',
            'sort_order' => 1
        ]);

        $this->assertDatabaseHas('course_subchapters', [
            'course_id' => $course->id,
            'chapter_id' => $chapter->id,
            'title' => 'Test Subchapter'
        ]);

        $this->assertEquals(1, $chapter->subchapters()->count());
    }

    /** @test */
    public function subchapter_can_have_materials()
    {
        $course = Course::create([
            'seller_id' => $this->seller->id,
            'title' => 'Test Course',
            'slug' => 'test-course',
            'description' => 'Test description',
            'category_id' => $this->category->id,
            'subcategory_id' => $this->subcategory->id,
            'detailed_category_id' => $this->detailedCategory->id,
            'price' => 50000,
            'difficulty_level' => 'beginner',
            'status' => 'draft'
        ]);

        $chapter = CourseChapter::create([
            'course_id' => $course->id,
            'title' => 'Test Chapter',
            'sort_order' => 1
        ]);

        $subchapter = CourseSubchapter::create([
            'course_id' => $course->id,
            'chapter_id' => $chapter->id,
            'title' => 'Test Subchapter',
            'sort_order' => 1
        ]);

        // Test text material
        $textMaterial = CourseMaterial::create([
            'course_id' => $course->id,
            'chapter_id' => $chapter->id,
            'subchapter_id' => $subchapter->id,
            'title' => 'Test Text Material',
            'type' => 'text',
            'content' => 'This is test text content',
            'sort_order' => 1
        ]);

        // Test video material
        $videoMaterial = CourseMaterial::create([
            'course_id' => $course->id,
            'chapter_id' => $chapter->id,
            'subchapter_id' => $subchapter->id,
            'title' => 'Test Video Material',
            'type' => 'video',
            'content' => 'https://youtube.com/watch?v=test',
            'sort_order' => 2
        ]);

        $this->assertDatabaseHas('course_materials', [
            'subchapter_id' => $subchapter->id,
            'title' => 'Test Text Material',
            'type' => 'text'
        ]);

        $this->assertDatabaseHas('course_materials', [
            'subchapter_id' => $subchapter->id,
            'title' => 'Test Video Material',
            'type' => 'video'
        ]);

        $this->assertEquals(2, $subchapter->materials()->count());
    }

    /** @test */
    public function course_system_is_independent_from_products()
    {
        // Verify that courses and products use different tables
        $this->assertDatabaseMissing('products', [
            'content_type' => 'course'
        ]);

        // Create a course
        $course = Course::create([
            'seller_id' => $this->seller->id,
            'title' => 'Independent Course',
            'slug' => 'independent-course',
            'description' => 'This course is independent',
            'category_id' => $this->category->id,
            'subcategory_id' => $this->subcategory->id,
            'detailed_category_id' => $this->detailedCategory->id,
            'price' => 75000,
            'difficulty_level' => 'intermediate',
            'status' => 'active'
        ]);

        // Verify course exists in courses table
        $this->assertDatabaseHas('courses', [
            'title' => 'Independent Course',
            'seller_id' => $this->seller->id
        ]);

        // Verify no interference with products table
        $this->assertDatabaseMissing('products', [
            'name' => 'Independent Course'
        ]);
    }
}
