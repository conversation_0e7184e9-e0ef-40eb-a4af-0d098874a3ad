@extends('store.layout')

@section('content')
<div class="py-8 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumbs -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('store.show', $seller->store_slug) }}" class="text-gray-500 hover:text-indigo-600 text-sm font-medium transition duration-150 ease-in-out">
                        Home
                    </a>
                </li>

                @if(isset($hasNewCategories) && $hasNewCategories)
                    @php
                        // Try to determine if this is a category, subcategory, or detailed category
                        $categoryObj = \App\Models\ProductCategory::where('slug', $categorySlugForLinks ?? $category)->first();
                        $subcategoryObj = \App\Models\ProductSubcategory::where('slug', $categorySlugForLinks ?? $category)->first();
                        $detailedCategoryObj = \App\Models\ProductDetailedCategory::where('slug', $categorySlugForLinks ?? $category)->first();
                    @endphp

                    @if($detailedCategoryObj && $detailedCategoryObj->subcategory && $detailedCategoryObj->subcategory->category)
                        <!-- If it's a detailed category, show the full hierarchy -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="{{ route('store.category', [$seller->store_slug, $detailedCategoryObj->subcategory->category->slug]) }}" class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                    {{ $detailedCategoryObj->subcategory->category->name }}
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="{{ route('store.category', [$seller->store_slug, $detailedCategoryObj->subcategory->slug]) }}" class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                    {{ $detailedCategoryObj->subcategory->name }}
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium">{{ $detailedCategoryObj->name }}</span>
                            </div>
                        </li>
                    @elseif($subcategoryObj && $subcategoryObj->category)
                        <!-- If it's a subcategory, show category -> subcategory -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <a href="{{ route('store.category', [$seller->store_slug, $subcategoryObj->category->slug]) }}" class="text-gray-500 hover:text-indigo-600 ml-1 text-sm font-medium transition duration-150 ease-in-out">
                                    {{ $subcategoryObj->category->name }}
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium">{{ $subcategoryObj->name }}</span>
                            </div>
                        </li>
                    @elseif($categoryObj)
                        <!-- If it's a main category, just show the category -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium">{{ $categoryObj->name }}</span>
                            </div>
                        </li>
                    @else
                        <!-- Fallback if we can't determine the category type -->
                        <li>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="ml-1 text-gray-600 text-sm font-medium">{{ $category }}</span>
                            </div>
                        </li>
                    @endif
                @else
                    <!-- Legacy category system -->
                    <li>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-gray-600 text-sm font-medium">{{ $category }}</span>
                        </div>
                    </li>
                @endif
            </ol>
        </nav>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Sidebar -->
            <div class="md:col-span-1">
                <div class="bg-white rounded-lg border p-4 sticky top-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Categories</h3>

                    <!-- All Products Button -->
                    <a href="{{ route('store.all-products', $seller->store_slug) }}" class="block w-full mb-4 text-center px-4 py-2 border border-indigo-600 rounded-md text-sm font-medium {{ $category === 'all' ? 'bg-indigo-600 text-white' : 'bg-white text-indigo-600 hover:bg-indigo-50' }} transition-colors">
                        View All Products
                    </a>

                    <ul class="space-y-2 border-t pt-4">
                        @if(isset($hasNewCategories) && $hasNewCategories)
                            @foreach($categories as $catSlug => $catName)
                            <li>
                                <a href="{{ route('store.category', [$seller->store_slug, $catSlug]) }}" class="text-gray-500 hover:text-indigo-600 text-sm {{ $catName == $category ? 'font-medium text-indigo-600' : '' }}">
                                    {{ $catName }} <span class="text-gray-400">({{ $categoryProductCounts[$catSlug] }})</span>
                                </a>
                            </li>
                            @endforeach
                        @else
                            @foreach($categories as $cat)
                            <li>
                                <a href="{{ route('store.category', [$seller->store_slug, $cat]) }}" class="text-gray-500 hover:text-indigo-600 text-sm {{ $cat == $category ? 'font-medium text-indigo-600' : '' }}">
                                    {{ $cat }} <span class="text-gray-400">({{ $categoryProductCounts[$cat] }})</span>
                                </a>
                            </li>
                            @endforeach
                        @endif
                    </ul>

                    <form action="{{ $category === 'all' ? route('store.all-products', $seller->store_slug) : route('store.category', [$seller->store_slug, $category]) }}" method="GET" id="filter-form">
                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Price Range</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input id="price-all" name="price_range" value="all" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ !request('price_range') || request('price_range') == 'all' ? 'checked' : '' }}>
                                    <label for="price-all" class="ml-3 text-sm text-gray-600">All Prices</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-1" name="price_range" value="0-100000" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ request('price_range') == '0-100000' ? 'checked' : '' }}>
                                    <label for="price-1" class="ml-3 text-sm text-gray-600">Under Rp 100.000</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-2" name="price_range" value="100000-500000" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ request('price_range') == '100000-500000' ? 'checked' : '' }}>
                                    <label for="price-2" class="ml-3 text-sm text-gray-600">Rp 100.000 - Rp 500.000</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-3" name="price_range" value="500000-1000000" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ request('price_range') == '500000-1000000' ? 'checked' : '' }}>
                                    <label for="price-3" class="ml-3 text-sm text-gray-600">Rp 500.000 - Rp 1.000.000</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="price-4" name="price_range" value="1000000+" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ request('price_range') == '1000000+' ? 'checked' : '' }}>
                                    <label for="price-4" class="ml-3 text-sm text-gray-600">Over Rp 1.000.000</label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rating</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input id="rating-all" name="rating" value="all" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ !request('rating') || request('rating') == 'all' ? 'checked' : '' }}>
                                    <label for="rating-all" class="ml-3 text-sm text-gray-600">All Ratings</label>
                                </div>
                                <div class="flex items-center">
                                    <input id="rating-4" name="rating" value="4" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ request('rating') == '4' ? 'checked' : '' }}>
                                    <label for="rating-4" class="ml-3 flex items-center text-sm text-gray-600">
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 4; $i++)
                                                <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            @endfor
                                        </div>
                                        <span class="ml-1">& Up</span>
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input id="rating-3" name="rating" value="3" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                        {{ request('rating') == '3' ? 'checked' : '' }}>
                                    <label for="rating-3" class="ml-3 flex items-center text-sm text-gray-600">
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 3; $i++)
                                                <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            @endfor
                                            @for($i = 1; $i <= 2; $i++)
                                                <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            @endfor
                                        </div>
                                        <span class="ml-1">& Up</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <button type="submit" class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors duration-300">
                                Apply Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products -->
            <div class="md:col-span-3">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">{{ $category === 'all' ? 'All Products' : $category }}</h1>
                    <p class="text-gray-500 mt-1">{{ $products->total() }} products found</p>
                </div>

                <!-- Filters -->
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4 bg-white p-4 rounded-lg shadow-sm">
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 mr-2">Sort by:</span>
                        <select name="sort" form="filter-form" class="rounded-md border-gray-300 py-1 pl-2 pr-8 text-sm focus:border-indigo-500 focus:ring-indigo-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="newest" {{ request('sort') == 'newest' || !request('sort') ? 'selected' : '' }}>Newest</option>
                            <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                            <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                            <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                        </select>
                    </div>
                </div>

                <!-- Products Grid -->
                @if($products->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($products as $product)
                    <div class="group relative card-hover bg-white rounded-xl overflow-hidden shadow-md border border-gray-100 animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="aspect-w-4 aspect-h-3 overflow-hidden bg-gray-100">
                            <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                    alt="{{ $product->name }}"
                                    class="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-110">
                            </a>
                            @if($product->discount_price)
                            <div class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                                SALE {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% OFF
                            </div>
                            @endif
                        </div>
                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900">
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                    <span aria-hidden="true" class="absolute inset-0"></span>
                                    {{ $product->name }}
                                </a>
                            </h3>
                            <p class="mt-1 text-sm text-gray-500">
                                @if(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory)
                                    {{ $product->productDetailedCategory->name }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                    {{ $product->productSubcategory->name }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                    {{ $product->productCategory->name }}
                                @else
                                    {{ ucfirst($product->category) }}
                                @endif
                            </p>
                            <div class="mt-2 flex items-center justify-between">
                                <div>
                                    @if($product->discount_price)
                                    <p class="text-sm font-medium text-gray-900">Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                    <p class="text-sm text-gray-500 line-through">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                    @else
                                    <p class="text-sm font-medium text-gray-900">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                    @endif
                                </div>
                                <div class="flex items-center">
                                    <div class="flex items-center">
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= ($product->average_rating ?? 5))
                                            <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                            @else
                                            <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                            @endif
                                        @endfor
                                    </div>
                                    <span class="text-xs text-gray-500 ml-1">({{ $product->reviews_count ?? 0 }})</span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-4 flex gap-2 relative z-10">
                                <!-- View Details Button (Left) -->
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}"
                                   class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                                    View Details
                                </a>

                                <!-- DigiAI Button (Right) -->
                                @if($product->hasActiveChatbot())
                                <button onclick="openProductChatFromStore('{{ $product->name }}', '{{ $seller->store_slug }}', '{{ $product->slug }}')"
                                        class="relative z-10 inline-flex justify-center items-center px-3 py-2 border border-blue-200 text-xs font-medium rounded-lg text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-300">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                                        <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                                    </svg>
                                    <span class="hidden sm:inline">Ask DigiAI</span>
                                    <span class="sm:hidden">AI</span>
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    <div class="pagination-container">
                        {{ $products->links('vendor.pagination.custom-tailwind') }}
                    </div>
                </div>
                @else
                <div class="text-center py-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M20 12H4" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">{{ $category === 'all' ? 'No products found' : 'No products found in this category' }}</h3>
                    <p class="mt-1 text-sm text-gray-500">{{ $category === 'all' ? 'Try adjusting your filters or check back later' : 'Try adjusting your filters or check out other categories' }}</p>
                    @if($category !== 'all')
                    <p class="mt-1 text-xs text-gray-500">You can view all products using the "View All Products" button in the sidebar</p>
                    @endif
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
