<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\HelpCategory;
use App\Models\HelpArticle;
use App\Models\Faq;
use App\Models\SupportRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class HelpCenterController extends Controller
{
    /**
     * Display the help center page.
     */
    public function index(Request $request)
    {
        $search = $request->query('search');
        $faqs = Faq::all();
        $articlesPerCategory = 4; // Configurable limit for non-search case

        if ($search) {
            $categories = HelpCategory::with(['articles' => function ($query) use ($search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
            }])->get();
        } else {
            $categories = HelpCategory::with('articles')->get();
        }

        // Debug: Log the categories and their articles
        foreach ($categories as $category) {
            Log::info("Category: {$category->name}, Articles: " . $category->articles->pluck('title')->toJson());
        }

        return view('seller.help-center.index', compact('faqs', 'categories'));
    }

    /**
     * Display a specific help article.
     */
    public function article($slug)
    {
        $article = HelpArticle::with('category', 'relatedArticles')->where('slug', $slug)->firstOrFail();

        return view('seller.help-center.article', compact('article'));
    }

    /**
     * Record feedback on an article.
     */
    public function feedback(Request $request, $slug)
    {
        $article = HelpArticle::where('slug', $slug)->firstOrFail();
        $type = $request->input('type');

        if ($type === 'yes') {
            $article->increment('helpful_yes');
        } elseif ($type === 'no') {
            $article->increment('helpful_no');
        }

        return redirect()->route('seller.help-center.article', $slug)
            ->with('success', 'Thank you for your feedback!');
    }

    /**
     * Display the contact support form.
     */
    public function contactForm()
    {
        return view('seller.help-center.contact');
    }

    /**
     * Submit the contact support form.
     */
    public function submitContact(Request $request)
    {
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'priority' => 'required|in:low,medium,high',
            'attachment' => 'nullable|file|max:10240|mimes:jpg,png,pdf,doc,docx',
        ]);

        $data = [
            'user_id' => Auth::id(),
            'subject' => $validated['subject'],
            'message' => $validated['message'],
            'priority' => $validated['priority'],
        ];

        if ($request->hasFile('attachment')) {
            $userId = Auth::id();
            $path = $request->file('attachment')->store("support_attachments/user_{$userId}", 'public');
            $data['attachment'] = $path;
        }

        SupportRequest::create($data);

        return redirect()->route('seller.help-center.contact')
            ->with('success', 'Your support request has been submitted successfully. We\'ll get back to you within 24 hours.');
    }
}