<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('dev-js/course-udemy-style.css')); ?>?v=<?php echo e(time()); ?>">
<link rel="stylesheet" href="<?php echo e(asset('css/seller-course-show.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Course configuration for JavaScript
window.courseInterfaceConfig = {
    courseId: '<?php echo e($course->id); ?>',
    csrfToken: '<?php echo e(csrf_token()); ?>',
    courseData: <?php echo json_encode($course->load(['sections.curriculumItems']), 15, 512) ?>
};
</script>
<script src="<?php echo e(asset('dev-js/course-udemy-interface.js')); ?>" defer></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="udemy-course-container">
    <!-- Header -->
    <div class="course-header">
        <div class="course-header-content">
            <div class="course-header-info">
                <h1 class="course-title"><?php echo e($course->title); ?></h1>
                <p class="course-subtitle">Manage course content and structure</p>
            </div>
            <div class="course-header-actions">
                <a href="<?php echo e(route('seller.courses.edit', $course)); ?>" class="btn btn-outline">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Course
                </a>
                <a href="<?php echo e(route('seller.courses.index')); ?>" class="btn btn-secondary">
                    ← Back to Courses
                </a>
            </div>
        </div>
    </div>

    <!-- Course Overview Card -->
    <div class="course-overview-card">
        <div class="course-overview-content">
            <div class="course-thumbnail">
                <?php if($course->thumbnail): ?>
                    <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>" alt="<?php echo e($course->title); ?>" class="thumbnail-image">
                <?php else: ?>
                    <div class="thumbnail-placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; background: var(--gray-100); color: var(--gray-400);">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                <?php endif; ?>
            </div>
            <div class="course-details">
                <h3 class="course-overview-title"><?php echo e($course->title); ?></h3>
                <p class="course-overview-description"><?php echo e($course->short_description); ?></p>
                <div class="course-meta-stats">
                    <span class="meta-badge"><?php echo e($course->sections->count()); ?> sections</span>
                    <span class="meta-badge"><?php echo e($course->sections->sum(function($section) { return $section->curriculumItems->count(); })); ?> items</span>
                    <span class="meta-badge status-<?php echo e($course->status); ?>"><?php echo e(ucfirst($course->status)); ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Publishing Section -->
    <div class="course-header">
        <div class="course-header-content">
            <div class="course-header-info">
                <h2 class="course-title" style="font-size: 1.5rem !important;">Course Publishing</h2>
                <p class="course-subtitle">Control your course visibility and publication status</p>
            </div>
            <div class="course-header-actions">
                <div class="status-info" style="display: flex; align-items: center; gap: 0.75rem;">
                    <span class="status-badge status-<?php echo e($course->status); ?>">
                        <?php echo e(ucfirst($course->status)); ?>

                    </span>
                    <label class="switch switch-sm">
                        <input type="checkbox" <?php echo e($course->status === 'active' ? 'checked' : ''); ?> data-toggle-type="course" data-id="<?php echo e($course->id); ?>" data-course-id="<?php echo e($course->id); ?>">
                        <span class="slider"></span>
                    </label>
                    <?php if($course->published_at): ?>
                        <p class="publication-date">Published: <?php echo e($course->published_at->format('M d, Y')); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Curriculum -->
    <div class="curriculum-section">
        <div class="curriculum-header">
            <h2 class="curriculum-title">Course Curriculum</h2>
            <span class="curriculum-stats">
                <?php echo e($course->sections->count()); ?> sections • <?php echo e($course->sections->sum(function($section) { return $section->curriculumItems->count(); })); ?> items
            </span>
        </div>

        <!-- Curriculum Content -->
        <div class="curriculum-content">
            <?php if($course->sections->count() > 0): ?>
                <?php $__currentLoopData = $course->sections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionIndex => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="udemy-section" data-section-id="<?php echo e($section->id); ?>">
                        <!-- Section Header -->
                        <div class="section-header">
                            <div class="section-info">
                                <div class="section-number">Section <?php echo e($sectionIndex + 1); ?>:</div>
                                <h3 class="section-title"><?php echo e($section->title); ?></h3>
                                <?php if($section->description): ?>
                                    <p class="section-description"><?php echo e($section->description); ?></p>
                                <?php endif; ?>
                                <div class="section-status">
                                    <span class="status-badge status-<?php echo e($section->is_active ? 'active' : 'inactive'); ?>">
                                        <?php echo e($section->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                    <label class="switch switch-sm">
                                        <input type="checkbox" <?php echo e($section->is_active ? 'checked' : ''); ?> data-toggle-type="section" data-id="<?php echo e($section->id); ?>" data-course-id="<?php echo e($course->id); ?>">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="section-actions">
                                <button type="button" class="action-btn edit-btn" data-action="edit-section" data-section-id="<?php echo e($section->id); ?>" title="Edit section">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button type="button" class="action-btn delete-btn" data-action="delete-section" data-section-id="<?php echo e($section->id); ?>" title="Delete section">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- Curriculum Items -->
                        <div class="curriculum-items">
                            <?php if($section->curriculumItems->count() > 0): ?>
                                <?php $__currentLoopData = $section->curriculumItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itemIndex => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="curriculum-item" data-item-id="<?php echo e($item->id); ?>">
                                        <div class="item-content">
                                            <div class="item-icon item-type-<?php echo e($item->type); ?>">
                                                <?php if($item->type === 'lecture'): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                <?php elseif($item->type === 'video'): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                    </svg>
                                                <?php elseif($item->type === 'pdf'): ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                    </svg>
                                                <?php else: ?>
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                    </svg>
                                                <?php endif; ?>
                                            </div>
                                            <div class="item-details">
                                                <h4 class="item-title"><?php echo e($item->title); ?></h4>
                                                <div class="item-meta">
                                                    <span class="item-type"><?php echo e(ucfirst($item->type)); ?></span>
                                                    <?php if($item->estimated_duration): ?>
                                                        <span class="item-duration"><?php echo e($item->estimated_duration); ?> min</span>
                                                    <?php endif; ?>
                                                    <?php if($item->is_preview): ?>
                                                        <span class="item-preview">Preview</span>
                                                    <?php endif; ?>
                                                    <span class="item-status status-<?php echo e($item->is_active ? 'active' : 'inactive'); ?>">
                                                        <?php echo e($item->is_active ? 'Active' : 'Inactive'); ?>

                                                    </span>
                                                    <label class="switch switch-sm">
                                                        <input type="checkbox" <?php echo e($item->is_active ? 'checked' : ''); ?> data-toggle-type="curriculum-item" data-id="<?php echo e($item->id); ?>" data-course-id="<?php echo e($course->id); ?>" data-section-id="<?php echo e($section->id); ?>">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="item-actions">
                                            <button type="button" class="action-btn edit-btn" data-action="edit-curriculum-item" data-item-id="<?php echo e($item->id); ?>" title="Edit curriculum item">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                            </button>
                                            <button type="button" class="action-btn delete-btn" data-action="delete-curriculum-item" data-item-id="<?php echo e($item->id); ?>" title="Delete curriculum item">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>

                            <!-- Add Curriculum Item -->
                            <div class="add-curriculum-item-container">
                                <button class="add-curriculum-item-btn" data-action="add-curriculum-item" data-section-id="<?php echo e($section->id); ?>">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add Curriculum Item
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <!-- Empty State -->
                <div class="empty-state">
                    <div class="empty-state-content">
                        <svg class="empty-state-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="empty-state-title">Start Building Your Course</h3>
                        <p class="empty-state-description">Create sections and curriculum items to organize your course content.</p>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Add Section Button -->
            <div class="add-section-container">
                <button class="add-section-btn" data-action="add-section">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Section
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('seller.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/seller/courses/show.blade.php ENDPATH**/ ?>