<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDocumentationTopicRelatedTable extends Migration
{
    public function up()
    {
        Schema::create('documentation_topic_related', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('topic_id')->references('id')->on('documentation_topics')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignUuid('related_topic_id')->references('id')->on('documentation_topics')->onUpdate('cascade')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('documentation_topic_related');
    }
}
