<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Course;
use App\Models\User;
use App\Models\Cart;
use App\Models\ProductDetailedCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BrowseController extends Controller
{
    /**
     * Display a listing of courses with search and filtering.
     */
    public function courses(Request $request)
    {
        $query = Course::where('status', 'active')
            ->with(['seller.sellerApplication', 'category', 'subcategory', 'detailedCategory'])
            ->withCount(['sections', 'curriculumItems']);

        // Search functionality
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($category = $request->input('category')) {
            $query->where('detailed_category_id', $category);
        }

        // Difficulty filter
        if ($difficulty = $request->input('difficulty')) {
            $query->where('difficulty_level', $difficulty);
        }

        // Sort functionality
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Paginate results
        $courses = $query->paginate(12);

        // Get categories for filtering
        $categories = ProductDetailedCategory::where('is_active', true)
            ->whereHas('courses', function($q) {
                $q->where('status', 'active');
            })
            ->with(['subcategory.category'])
            ->orderBy('name')
            ->get();

        return view('browse.courses', compact('courses', 'categories'));
    }

    /**
     * Display a listing of products with search and filtering.
     */
    public function products(Request $request)
    {
        $query = Product::where('status', 'active')
            ->whereHas('seller', function($q) {
                $q->whereHas('sellerApplication', function($q2) {
                    $q2->where('status', 'approved');
                });
            })
            ->with([
                'seller',
                'seller.sellerApplication',
                'productCategory',
                'productSubcategory',
                'productDetailedCategory'
            ]);

        // Search functionality
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($category = $request->input('category')) {
            $query->where('detailed_category_id', $category);
        }

        // Sort functionality
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Paginate results
        $products = $query->paginate(12);

        // Get categories for filtering
        $categories = ProductDetailedCategory::where('is_active', true)
            ->whereHas('products', function($q) {
                $q->where('status', 'active');
            })
            ->with(['subcategory.category'])
            ->orderBy('name')
            ->get();

        return view('browse.products', compact('products', 'categories'));
    }

    /**
     * Display a listing of stores with search and filtering.
     */
    public function stores(Request $request)
    {
        // Get all active detailed categories
        $detailedCategories = ProductDetailedCategory::where('is_active', true)
            ->with(['subcategory.category'])
            ->orderBy('name')
            ->get();

        // Get selected detailed category if any
        $selectedCategorySlug = $request->input('category');
        $selectedDetailedCategory = null;

        if ($selectedCategorySlug) {
            $selectedDetailedCategory = ProductDetailedCategory::where('slug', $selectedCategorySlug)->first();
        }

        // Build the query for sellers with approved applications
        $query = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function ($query) {
                $query->where('status', 'approved');
            })
            ->with(['sellerApplication', 'currentMembership.membershipTier'])
            ->withCount(['products' => function($query) {
                $query->where('status', 'active');
            }]);

        // Search functionality
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->whereHas('sellerApplication', function($subQuery) use ($search) {
                    $subQuery->where('store_name', 'like', "%{$search}%")
                             ->orWhere('store_description', 'like', "%{$search}%");
                });
            });
        }

        // Category filter
        if ($selectedDetailedCategory) {
            $query->whereHas('products', function($q) use ($selectedDetailedCategory) {
                $q->where('status', 'active')
                  ->where('detailed_category_id', $selectedDetailedCategory->id);
            });
        }

        // Sort functionality
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'products_count':
                $query->orderBy('products_count', 'desc');
                break;
            case 'name':
                $query->whereHas('sellerApplication')->orderBy(
                    \App\Models\SellerApplication::select('store_name')
                        ->whereColumn('seller_applications.user_id', 'users.id')
                        ->limit(1)
                );
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Paginate results
        $stores = $query->paginate(12);

        // Format stores data
        $formattedStores = [];
        foreach ($stores as $seller) {
            $store = $seller->sellerApplication;
            
            // Get the main detailed category for this seller
            $mainDetailedCategory = null;
            if ($selectedDetailedCategory) {
                $detailedCategoryProductCount = $seller->products()
                    ->where('status', 'active')
                    ->where('detailed_category_id', $selectedDetailedCategory->id)
                    ->count();
            } else {
                $detailedCategoryProductCount = 0;
                $mainDetailedCategory = $seller->products()
                    ->where('status', 'active')
                    ->whereNotNull('detailed_category_id')
                    ->with('productDetailedCategory')
                    ->get()
                    ->groupBy('detailed_category_id')
                    ->sortByDesc(function($products) {
                        return $products->count();
                    })
                    ->first();
                
                if ($mainDetailedCategory && $mainDetailedCategory->isNotEmpty()) {
                    $mainDetailedCategory = $mainDetailedCategory->first()->productDetailedCategory->name ?? null;
                }
            }

            $formattedStores[] = [
                'name' => $store->store_name,
                'slug' => $store->store_name_slug,
                'description' => \Illuminate\Support\Str::limit($store->store_description, 100),
                'logo' => $store->store_logo ? asset('storage/' . $store->store_logo) : null,
                'product_count' => $seller->products_count,
                'category_product_count' => $detailedCategoryProductCount,
                'user_avatar' => $seller->avatar ? asset('storage/' . $seller->avatar) : null,
                'user_name' => $seller->name,
                'main_category' => $mainDetailedCategory
            ];
        }

        return view('browse.stores', compact(
            'detailedCategories',
            'selectedDetailedCategory',
            'formattedStores',
            'stores' // For pagination
        ));
    }
}
