<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseSection;
use App\Models\CourseCurriculumItem;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CourseController extends Controller
{
    /**
     * Display a listing of the seller's courses.
     */
    public function index(Request $request)
    {
        $query = Course::where('seller_id', Auth::id());

        if ($search = $request->input('search')) {
            $query->where('title', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%");
        }

        if ($status = $request->input('status')) {
            if ($status !== 'all') {
                $query->where('status', $status);
            }
        }

        if ($difficulty = $request->input('difficulty')) {
            if ($difficulty !== 'all') {
                $query->where('difficulty_level', $difficulty);
            }
        }

        $courses = $query->with(['category', 'subcategory', 'detailedCategory'])
            ->withCount(['sections', 'curriculumItems'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('seller.courses.index', compact('courses'));
    }

    /**
     * Show the form for creating a new course.
     */
    public function create()
    {
        // Get course-specific categories
        $categories = ProductCategory::active()
            ->forProductType('course')
            ->with(['activeSubcategories.activeDetailedCategories'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // Clear any existing auto-saved data when starting fresh
        // This ensures users always start with a clean slate
        session()->forget('course_auto_save');

        // Since we're starting fresh, no auto-save data should be available
        $autoSavedData = null;
        $hasAutoSave = false;

        return view('seller.courses.create', compact('categories', 'autoSavedData', 'hasAutoSave'));
    }

    /**
     * Store a newly created course in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:product_categories,id',
            'subcategory_id' => 'required|exists:product_subcategories,id',
            'detailed_category_id' => 'required|exists:product_detailed_categories,id',
            'price' => 'required|numeric|min:5000',
            'discount_price' => 'nullable|numeric|min:5000|lt:price',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'what_you_will_learn' => 'required|array|min:1',
            'what_you_will_learn.*' => 'required|string|max:255',
            'requirements' => 'required|array|min:1',
            'requirements.*' => 'required|string|max:255',
            'target_audience' => 'required|array|min:1',
            'target_audience.*' => 'required|string|max:255',
            'estimated_duration' => 'nullable|integer|min:1',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validated['seller_id'] = Auth::id();
        $validated['slug'] = Course::generateUniqueSlug($validated['title']);
        // Auto-set status to draft when creating course
        $validated['status'] = 'draft';

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $validated['thumbnail'] = $request->file('thumbnail')->store('courses/thumbnails', 'public');
        }

        // Handle additional images upload
        if ($request->hasFile('images')) {
            $images = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('courses/images', 'public');
                $images[] = [
                    'path' => $path,
                    'name' => $image->getClientOriginalName(),
                    'size' => $image->getSize(),
                ];
            }
            $validated['images'] = $images;
        }

        // Don't set published_at for draft courses
        // This will be set when the course is published from the course management page

        $course = Course::create($validated);

        // Clear any auto-saved data after successful course creation
        session()->forget('course_auto_save');

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Course created as draft! Add your content and publish when ready.');
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        $course->load([
            'sections.curriculumItems',
            'category',
            'subcategory',
            'detailedCategory'
        ]);

        return view('seller.courses.show', compact('course'));
    }

    /**
     * Show the form for editing the specified course.
     */
    public function edit(Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        // Get course-specific categories
        $categories = ProductCategory::active()
            ->forProductType('course')
            ->with(['activeSubcategories.activeDetailedCategories'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        return view('seller.courses.edit', compact('course', 'categories'));
    }

    /**
     * Update the specified course in storage.
     */
    public function update(Request $request, Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:product_categories,id',
            'subcategory_id' => 'required|exists:product_subcategories,id',
            'detailed_category_id' => 'required|exists:product_detailed_categories,id',
            'price' => 'required|numeric|min:5000',
            'discount_price' => 'nullable|numeric|min:5000|lt:price',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'what_you_will_learn' => 'required|array|min:1',
            'what_you_will_learn.*' => 'required|string|max:255',
            'requirements' => 'required|array|min:1',
            'requirements.*' => 'required|string|max:255',
            'target_audience' => 'required|array|min:1',
            'target_audience.*' => 'required|string|max:255',
            'estimated_duration' => 'nullable|integer|min:1',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:draft,active,inactive',
            'remove_thumbnail' => 'nullable|boolean',
            'remove_images' => 'nullable|array',
        ]);

        // Handle thumbnail removal
        if ($request->input('remove_thumbnail') && $course->thumbnail) {
            Storage::disk('public')->delete($course->thumbnail);
            $validated['thumbnail'] = null;
        }

        // Handle new thumbnail upload
        if ($request->hasFile('thumbnail')) {
            if ($course->thumbnail) {
                Storage::disk('public')->delete($course->thumbnail);
            }
            $validated['thumbnail'] = $request->file('thumbnail')->store('courses/thumbnails', 'public');
        }

        // Handle image removal
        if ($request->input('remove_images')) {
            $currentImages = $course->images ?? [];
            $removeIndices = $request->input('remove_images');
            
            foreach ($removeIndices as $index) {
                if (isset($currentImages[$index])) {
                    // Delete the file from storage - handle both array and string formats
                    if (is_array($currentImages[$index]) && isset($currentImages[$index]['path'])) {
                        Storage::disk('public')->delete($currentImages[$index]['path']);
                    } elseif (is_string($currentImages[$index])) {
                        // Handle legacy format where image is stored as string path
                        Storage::disk('public')->delete($currentImages[$index]);
                    }
                    unset($currentImages[$index]);
                }
            }
            
            $validated['images'] = array_values($currentImages); // Reindex array
        }

        // Handle new images upload
        if ($request->hasFile('images')) {
            $currentImages = $validated['images'] ?? $course->images ?? [];
            
            foreach ($request->file('images') as $image) {
                $path = $image->store('courses/images', 'public');
                $currentImages[] = [
                    'path' => $path,
                    'name' => $image->getClientOriginalName(),
                    'size' => $image->getSize(),
                ];
            }
            
            $validated['images'] = $currentImages;
        }

        // Set published_at if status is changing to active and not already set
        if ($validated['status'] === Course::STATUS_ACTIVE && !$course->published_at) {
            $validated['published_at'] = now();
        }

        $course->update($validated);

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Course updated successfully!');
    }

    /**
     * Auto-save course data to session
     */
    public function autoSave(Request $request)
    {
        try {
            $validated = $request->validate([
                'title' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'short_description' => 'nullable|string|max:500',
                'category_id' => 'nullable|exists:product_categories,id',
                'subcategory_id' => 'nullable|exists:product_subcategories,id',
                'detailed_category_id' => 'nullable|exists:product_detailed_categories,id',
                'price' => 'nullable|numeric|min:5000',
                'discount_price' => 'nullable|numeric|min:5000',
                'difficulty_level' => 'nullable|in:beginner,intermediate,advanced',
                'estimated_duration' => 'nullable|integer|min:1',
                'status' => 'nullable|in:draft,active',
            ]);

            // Save to session with timestamp
            $autoSaveData = [
                'data' => $validated,
                'timestamp' => now()->toISOString(),
                'seller_id' => Auth::id(),
            ];

            session(['course_auto_save' => $autoSaveData]);

            return response()->json([
                'success' => true,
                'message' => 'Course data auto-saved',
                'timestamp' => $autoSaveData['timestamp']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Auto-save failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear auto-saved course data from session
     */
    public function clearAutoSave()
    {
        session()->forget('course_auto_save');

        return response()->json([
            'success' => true,
            'message' => 'Auto-saved data cleared'
        ]);
    }

    /**
     * Save course as draft from session data
     */
    public function saveDraft(Request $request)
    {
        try {
            // Get auto-saved data from session
            $autoSaveData = session('course_auto_save');

            if (!$autoSaveData || !isset($autoSaveData['data'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'No auto-saved data found'
                ], 400);
            }

            $data = $autoSaveData['data'];

            // Ensure we have at least a title
            if (empty($data['title'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Course title is required'
                ], 400);
            }

            // Set required fields with defaults
            $data['seller_id'] = Auth::id();
            $data['slug'] = Course::generateUniqueSlug($data['title']);
            $data['status'] = 'draft';
            $data['description'] = $data['description'] ?? 'Draft course - description to be added';

            // Create the course as draft
            $course = Course::create($data);

            // Clear auto-save data
            session()->forget('course_auto_save');

            return response()->json([
                'success' => true,
                'message' => 'Course saved as draft successfully',
                'course_id' => $course->id,
                'redirect_url' => route('seller.courses.show', $course)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save draft: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified course from storage.
     */
    public function destroy(Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        $course->delete();

        return redirect()->route('seller.courses.index')
            ->with('success', 'Course deleted successfully!');
    }

    /**
     * Publish a course (change from draft to active)
     */
    public function publish(Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        // Only allow publishing if course is in draft status
        if ($course->status !== 'draft') {
            return redirect()->back()->with('error', 'Only draft courses can be published.');
        }

        $course->update([
            'status' => Course::STATUS_ACTIVE,
            'published_at' => now()
        ]);

        return redirect()->back()->with('success', 'Course published successfully! It is now visible to students.');
    }

    /**
     * Unpublish a course (change from active to draft)
     */
    public function unpublish(Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        // Only allow unpublishing if course is active
        if ($course->status !== Course::STATUS_ACTIVE) {
            return redirect()->back()->with('error', 'Only active courses can be unpublished.');
        }

        $course->update([
            'status' => 'draft'
        ]);

        return redirect()->back()->with('success', 'Course unpublished successfully! It is now hidden from students.');
    }

    /**
     * Toggle the publication status of the course.
     */
    public function toggleStatus(Request $request, Course $course)
    {
        // Ensure the course belongs to the authenticated seller
        if ($course->seller_id !== Auth::id()) {
            abort(403);
        }

        // Validate the status input
        $validated = $request->validate([
            'status' => 'required|in:0,1'
        ]);

        $isActive = (bool) $validated['status'];

        // Update status
        $newStatus = $isActive ? 'active' : 'draft';
        $course->status = $newStatus;

        if ($isActive && !$course->published_at) {
            $course->published_at = now();
        }

        $course->save();

        return response()->json([
            'success' => true,
            'message' => 'Course status updated successfully',
            'status' => $course->status,
            'published_at' => $course->published_at ? $course->published_at->format('M d, Y') : null
        ]);
    }
}
