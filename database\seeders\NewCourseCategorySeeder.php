<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Support\Str;

class NewCourseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define course-specific categories for the new independent course system
        $courseCategories = [
            [
                'name' => 'UMKM & Business',
                'icon' => 'briefcase',
                'description' => 'Business skills and UMKM development courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'WhatsApp Bot',
                        'description' => 'WhatsApp automation and bot development.',
                        'legacy_code' => 'whatsapp_bot',
                        'detailed_categories' => [
                            ['name' => 'WhatsApp Business API', 'description' => 'Learn to use WhatsApp Business API for automation.'],
                            ['name' => 'Chatbot Development', 'description' => 'Build intelligent chatbots for WhatsApp.'],
                            ['name' => 'Customer Service Automation', 'description' => 'Automate customer service with WhatsApp bots.'],
                            ['name' => 'Marketing Automation', 'description' => 'Use WhatsApp bots for marketing campaigns.'],
                        ]
                    ],
                    [
                        'name' => 'AI Automation',
                        'description' => 'Artificial Intelligence and automation for business.',
                        'legacy_code' => 'ai_automation',
                        'detailed_categories' => [
                            ['name' => 'Business Process Automation', 'description' => 'Automate business processes with AI.'],
                            ['name' => 'AI Tools for UMKM', 'description' => 'Practical AI tools for small businesses.'],
                            ['name' => 'Machine Learning Basics', 'description' => 'Introduction to machine learning for business.'],
                            ['name' => 'AI Content Creation', 'description' => 'Use AI for content creation and marketing.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other UMKM-related topics and specialized courses.',
                        'legacy_code' => 'umkm_other',
                        'detailed_categories' => [
                            ['name' => 'Digital Marketing', 'description' => 'Digital marketing strategies for UMKM.'],
                            ['name' => 'E-commerce', 'description' => 'Building and managing online stores.'],
                            ['name' => 'Financial Management', 'description' => 'Financial planning and management for small businesses.'],
                            ['name' => 'Business Strategy', 'description' => 'Strategic planning and business development.'],
                            ['name' => 'Other', 'description' => 'Miscellaneous UMKM topics and specialized courses.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Technology & Programming',
                'icon' => 'code',
                'description' => 'Programming, software development, and technology courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Web Development',
                        'description' => 'Frontend, backend, and full-stack web development.',
                        'legacy_code' => 'web_dev',
                        'detailed_categories' => [
                            ['name' => 'Frontend Development', 'description' => 'HTML, CSS, JavaScript, React, Vue.js courses.'],
                            ['name' => 'Backend Development', 'description' => 'Node.js, PHP, Python, Java backend courses.'],
                            ['name' => 'Full-Stack Development', 'description' => 'Complete web development stack courses.'],
                            ['name' => 'WordPress Development', 'description' => 'WordPress theme and plugin development.'],
                        ]
                    ],
                    [
                        'name' => 'Mobile Development',
                        'description' => 'iOS, Android, and cross-platform mobile development.',
                        'legacy_code' => 'mobile_dev',
                        'detailed_categories' => [
                            ['name' => 'Android Development', 'description' => 'Native Android app development.'],
                            ['name' => 'iOS Development', 'description' => 'Native iOS app development.'],
                            ['name' => 'React Native', 'description' => 'Cross-platform mobile development with React Native.'],
                            ['name' => 'Flutter', 'description' => 'Cross-platform mobile development with Flutter.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other programming and technology topics.',
                        'legacy_code' => 'tech_other',
                        'detailed_categories' => [
                            ['name' => 'Database Management', 'description' => 'SQL, NoSQL, and database design courses.'],
                            ['name' => 'DevOps & Cloud', 'description' => 'DevOps practices and cloud computing.'],
                            ['name' => 'Cybersecurity', 'description' => 'Information security and cybersecurity.'],
                            ['name' => 'Other', 'description' => 'Other technology and programming topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Design & Creative',
                'icon' => 'palette',
                'description' => 'Design, creative arts, and multimedia courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Graphic Design',
                        'description' => 'Visual design and graphic arts.',
                        'legacy_code' => 'graphic_design',
                        'detailed_categories' => [
                            ['name' => 'Logo Design', 'description' => 'Professional logo design techniques.'],
                            ['name' => 'Brand Identity', 'description' => 'Complete brand identity design.'],
                            ['name' => 'Print Design', 'description' => 'Design for print media and materials.'],
                            ['name' => 'Digital Design', 'description' => 'Design for digital platforms and media.'],
                        ]
                    ],
                    [
                        'name' => 'UI/UX Design',
                        'description' => 'User interface and user experience design.',
                        'legacy_code' => 'ui_ux_design',
                        'detailed_categories' => [
                            ['name' => 'UI Design', 'description' => 'User interface design principles and practices.'],
                            ['name' => 'UX Research', 'description' => 'User experience research and testing.'],
                            ['name' => 'Prototyping', 'description' => 'Design prototyping and wireframing.'],
                            ['name' => 'Design Systems', 'description' => 'Creating and maintaining design systems.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other design and creative topics.',
                        'legacy_code' => 'design_other',
                        'detailed_categories' => [
                            ['name' => 'Video Editing', 'description' => 'Video production and editing techniques.'],
                            ['name' => 'Photography', 'description' => 'Photography skills and techniques.'],
                            ['name' => 'Animation', 'description' => '2D and 3D animation courses.'],
                            ['name' => 'Other', 'description' => 'Other design and creative topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Marketing & Sales',
                'icon' => 'trending-up',
                'description' => 'Marketing strategies, sales techniques, and business growth.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Digital Marketing',
                        'description' => 'Online marketing strategies and techniques.',
                        'legacy_code' => 'digital_marketing',
                        'detailed_categories' => [
                            ['name' => 'Social Media Marketing', 'description' => 'Marketing through social media platforms.'],
                            ['name' => 'Content Marketing', 'description' => 'Creating and distributing valuable content.'],
                            ['name' => 'Email Marketing', 'description' => 'Email campaign strategies and automation.'],
                            ['name' => 'SEO & SEM', 'description' => 'Search engine optimization and marketing.'],
                        ]
                    ],
                    [
                        'name' => 'Sales Techniques',
                        'description' => 'Professional sales strategies and methods.',
                        'legacy_code' => 'sales_techniques',
                        'detailed_categories' => [
                            ['name' => 'B2B Sales', 'description' => 'Business-to-business sales strategies.'],
                            ['name' => 'B2C Sales', 'description' => 'Business-to-consumer sales techniques.'],
                            ['name' => 'Sales Psychology', 'description' => 'Understanding customer psychology in sales.'],
                            ['name' => 'Negotiation Skills', 'description' => 'Professional negotiation techniques.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other marketing and sales topics.',
                        'legacy_code' => 'marketing_other',
                        'detailed_categories' => [
                            ['name' => 'Brand Management', 'description' => 'Building and managing brand identity.'],
                            ['name' => 'Market Research', 'description' => 'Conducting effective market research.'],
                            ['name' => 'Customer Relationship Management', 'description' => 'CRM strategies and tools.'],
                            ['name' => 'Other', 'description' => 'Other marketing and sales topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Health & Fitness',
                'icon' => 'heart',
                'description' => 'Health, fitness, wellness, and lifestyle courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Fitness Training',
                        'description' => 'Physical fitness and exercise programs.',
                        'legacy_code' => 'fitness_training',
                        'detailed_categories' => [
                            ['name' => 'Strength Training', 'description' => 'Weight lifting and muscle building.'],
                            ['name' => 'Cardio Workouts', 'description' => 'Cardiovascular exercise programs.'],
                            ['name' => 'Yoga & Pilates', 'description' => 'Mind-body fitness practices.'],
                            ['name' => 'Home Workouts', 'description' => 'Exercise routines for home practice.'],
                        ]
                    ],
                    [
                        'name' => 'Nutrition & Diet',
                        'description' => 'Nutrition education and dietary guidance.',
                        'legacy_code' => 'nutrition_diet',
                        'detailed_categories' => [
                            ['name' => 'Meal Planning', 'description' => 'Strategic meal planning and preparation.'],
                            ['name' => 'Weight Management', 'description' => 'Healthy weight loss and maintenance.'],
                            ['name' => 'Sports Nutrition', 'description' => 'Nutrition for athletic performance.'],
                            ['name' => 'Special Diets', 'description' => 'Specialized dietary approaches.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other health and wellness topics.',
                        'legacy_code' => 'health_other',
                        'detailed_categories' => [
                            ['name' => 'Mental Health', 'description' => 'Mental wellness and stress management.'],
                            ['name' => 'Sleep Optimization', 'description' => 'Improving sleep quality and habits.'],
                            ['name' => 'Meditation & Mindfulness', 'description' => 'Mindfulness and meditation practices.'],
                            ['name' => 'Other', 'description' => 'Other health and wellness topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Personal Development',
                'icon' => 'user',
                'description' => 'Self-improvement, productivity, and life skills.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Productivity & Time Management',
                        'description' => 'Efficiency and time management skills.',
                        'legacy_code' => 'productivity',
                        'detailed_categories' => [
                            ['name' => 'Time Management', 'description' => 'Effective time management strategies.'],
                            ['name' => 'Goal Setting', 'description' => 'Setting and achieving personal goals.'],
                            ['name' => 'Habit Formation', 'description' => 'Building positive habits and routines.'],
                            ['name' => 'Work-Life Balance', 'description' => 'Balancing professional and personal life.'],
                        ]
                    ],
                    [
                        'name' => 'Communication Skills',
                        'description' => 'Interpersonal and professional communication.',
                        'legacy_code' => 'communication',
                        'detailed_categories' => [
                            ['name' => 'Public Speaking', 'description' => 'Confident public speaking and presentation.'],
                            ['name' => 'Writing Skills', 'description' => 'Effective written communication.'],
                            ['name' => 'Leadership Skills', 'description' => 'Leadership and team management.'],
                            ['name' => 'Conflict Resolution', 'description' => 'Managing and resolving conflicts.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other personal development topics.',
                        'legacy_code' => 'personal_other',
                        'detailed_categories' => [
                            ['name' => 'Emotional Intelligence', 'description' => 'Understanding and managing emotions.'],
                            ['name' => 'Critical Thinking', 'description' => 'Developing analytical thinking skills.'],
                            ['name' => 'Creativity & Innovation', 'description' => 'Enhancing creative thinking abilities.'],
                            ['name' => 'Other', 'description' => 'Other personal development topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Language Learning',
                'icon' => 'globe',
                'description' => 'Foreign language learning and communication.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'English Language',
                        'description' => 'English language learning and proficiency.',
                        'legacy_code' => 'english_language',
                        'detailed_categories' => [
                            ['name' => 'English Grammar', 'description' => 'English grammar rules and usage.'],
                            ['name' => 'English Conversation', 'description' => 'Conversational English skills.'],
                            ['name' => 'Business English', 'description' => 'English for professional settings.'],
                            ['name' => 'IELTS/TOEFL Preparation', 'description' => 'Test preparation for English proficiency.'],
                        ]
                    ],
                    [
                        'name' => 'Asian Languages',
                        'description' => 'Asian language learning courses.',
                        'legacy_code' => 'asian_languages',
                        'detailed_categories' => [
                            ['name' => 'Mandarin Chinese', 'description' => 'Mandarin Chinese language learning.'],
                            ['name' => 'Japanese', 'description' => 'Japanese language and culture.'],
                            ['name' => 'Korean', 'description' => 'Korean language learning.'],
                            ['name' => 'Other Asian Languages', 'description' => 'Other Asian languages and dialects.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other languages and language learning topics.',
                        'legacy_code' => 'language_other',
                        'detailed_categories' => [
                            ['name' => 'European Languages', 'description' => 'European language learning courses.'],
                            ['name' => 'Arabic', 'description' => 'Arabic language and culture.'],
                            ['name' => 'Language Teaching Methods', 'description' => 'Methods for teaching languages.'],
                            ['name' => 'Other', 'description' => 'Other languages and language topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Arts & Crafts',
                'icon' => 'palette',
                'description' => 'Visual arts, crafts, and creative expression courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Visual Arts',
                        'description' => 'Drawing, painting, and visual art techniques.',
                        'legacy_code' => 'visual_arts',
                        'detailed_categories' => [
                            ['name' => 'Drawing & Sketching', 'description' => 'Basic and advanced drawing techniques.'],
                            ['name' => 'Painting Techniques', 'description' => 'Various painting methods and styles.'],
                            ['name' => 'Digital Art', 'description' => 'Digital illustration and art creation.'],
                            ['name' => 'Watercolor Painting', 'description' => 'Watercolor techniques and projects.'],
                        ]
                    ],
                    [
                        'name' => 'Crafts & DIY',
                        'description' => 'Hands-on crafting and DIY projects.',
                        'legacy_code' => 'crafts_diy',
                        'detailed_categories' => [
                            ['name' => 'Woodworking', 'description' => 'Wood crafting and furniture making.'],
                            ['name' => 'Pottery & Ceramics', 'description' => 'Clay work and ceramic arts.'],
                            ['name' => 'Jewelry Making', 'description' => 'Creating handmade jewelry and accessories.'],
                            ['name' => 'Knitting & Crochet', 'description' => 'Textile arts and fiber crafts.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other arts and crafts topics.',
                        'legacy_code' => 'arts_other',
                        'detailed_categories' => [
                            ['name' => 'Photography', 'description' => 'Photography techniques and composition.'],
                            ['name' => 'Sculpture', 'description' => 'Three-dimensional art and sculpture.'],
                            ['name' => 'Calligraphy', 'description' => 'Beautiful handwriting and lettering arts.'],
                            ['name' => 'Other', 'description' => 'Other arts and crafts topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Music & Audio',
                'icon' => 'music',
                'description' => 'Music education, audio production, and sound design.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Musical Instruments',
                        'description' => 'Learn to play various musical instruments.',
                        'legacy_code' => 'instruments',
                        'detailed_categories' => [
                            ['name' => 'Guitar Lessons', 'description' => 'Acoustic and electric guitar instruction.'],
                            ['name' => 'Piano Lessons', 'description' => 'Piano and keyboard instruction.'],
                            ['name' => 'Violin Lessons', 'description' => 'String instrument instruction.'],
                            ['name' => 'Drums Lessons', 'description' => 'Percussion and drum kit instruction.'],
                        ]
                    ],
                    [
                        'name' => 'Music Production',
                        'description' => 'Audio recording, mixing, and production.',
                        'legacy_code' => 'music_production',
                        'detailed_categories' => [
                            ['name' => 'Audio Recording', 'description' => 'Recording techniques and equipment.'],
                            ['name' => 'Music Mixing', 'description' => 'Audio mixing and mastering.'],
                            ['name' => 'Sound Design', 'description' => 'Creating and designing audio effects.'],
                            ['name' => 'Beat Making', 'description' => 'Creating beats and rhythms.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other music and audio topics.',
                        'legacy_code' => 'music_other',
                        'detailed_categories' => [
                            ['name' => 'Music Theory', 'description' => 'Understanding music theory and composition.'],
                            ['name' => 'Songwriting', 'description' => 'Writing lyrics and composing songs.'],
                            ['name' => 'Voice Training', 'description' => 'Vocal techniques and singing instruction.'],
                            ['name' => 'Other', 'description' => 'Other music and audio topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Lifestyle & Hobbies',
                'icon' => 'home',
                'description' => 'Lifestyle improvement, hobbies, and personal interests.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Cooking & Baking',
                        'description' => 'Culinary arts and food preparation.',
                        'legacy_code' => 'cooking_baking',
                        'detailed_categories' => [
                            ['name' => 'Basic Cooking Skills', 'description' => 'Fundamental cooking techniques and methods.'],
                            ['name' => 'Baking Fundamentals', 'description' => 'Basic baking skills and recipes.'],
                            ['name' => 'International Cuisine', 'description' => 'Cooking dishes from around the world.'],
                            ['name' => 'Healthy Cooking', 'description' => 'Nutritious and healthy meal preparation.'],
                        ]
                    ],
                    [
                        'name' => 'Home & Garden',
                        'description' => 'Home improvement and gardening skills.',
                        'legacy_code' => 'home_garden',
                        'detailed_categories' => [
                            ['name' => 'Interior Design', 'description' => 'Home decoration and space design.'],
                            ['name' => 'Gardening', 'description' => 'Plant cultivation and garden maintenance.'],
                            ['name' => 'Home Organization', 'description' => 'Organizing and decluttering living spaces.'],
                            ['name' => 'DIY Home Improvement', 'description' => 'Home repair and improvement projects.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other lifestyle and hobby topics.',
                        'legacy_code' => 'lifestyle_other',
                        'detailed_categories' => [
                            ['name' => 'Travel Planning', 'description' => 'Planning and organizing travel experiences.'],
                            ['name' => 'Pet Care', 'description' => 'Caring for pets and animals.'],
                            ['name' => 'Sustainable Living', 'description' => 'Eco-friendly lifestyle practices.'],
                            ['name' => 'Other', 'description' => 'Other lifestyle and hobby topics.'],
                        ]
                    ],
                ]
            ],
        ];

        $this->createCourseCategories($courseCategories);
    }

    /**
     * Create course categories with their subcategories and detailed categories.
     */
    private function createCourseCategories(array $categories): void
    {
        foreach ($categories as $index => $categoryData) {
            $slug = Str::slug($categoryData['name']);

            // Check if the slug already exists
            $counter = 1;
            $originalSlug = $slug;
            while (ProductCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the main category
            $category = ProductCategory::create([
                'name' => $categoryData['name'],
                'slug' => $slug,
                'description' => $categoryData['description'],
                'icon' => $categoryData['icon'],
                'product_type' => $categoryData['product_type'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);

            // Create subcategories
            foreach ($categoryData['subcategories'] as $subIndex => $subcategoryData) {
                $subSlug = Str::slug($subcategoryData['name']);
                
                // Check if the subcategory slug already exists
                $subCounter = 1;
                $originalSubSlug = $subSlug;
                while (ProductSubcategory::where('slug', $subSlug)->exists()) {
                    $subSlug = $originalSubSlug . '-' . $subCounter;
                    $subCounter++;
                }

                $subcategory = ProductSubcategory::create([
                    'category_id' => $category->id,
                    'name' => $subcategoryData['name'],
                    'slug' => $subSlug,
                    'description' => $subcategoryData['description'],
                    'legacy_code' => $subcategoryData['legacy_code'],
                    'is_active' => true,
                    'sort_order' => $subIndex + 1,
                ]);

                // Create detailed categories
                foreach ($subcategoryData['detailed_categories'] as $detailIndex => $detailedCategoryData) {
                    $detailSlug = Str::slug($detailedCategoryData['name']);
                    
                    // Check if the detailed category slug already exists
                    $detailCounter = 1;
                    $originalDetailSlug = $detailSlug;
                    while (ProductDetailedCategory::where('slug', $detailSlug)->exists()) {
                        $detailSlug = $originalDetailSlug . '-' . $detailCounter;
                        $detailCounter++;
                    }

                    ProductDetailedCategory::create([
                        'subcategory_id' => $subcategory->id,
                        'name' => $detailedCategoryData['name'],
                        'slug' => $detailSlug,
                        'description' => $detailedCategoryData['description'],
                        'is_active' => true,
                        'sort_order' => $detailIndex + 1,
                    ]);
                }
            }
        }
    }
}
