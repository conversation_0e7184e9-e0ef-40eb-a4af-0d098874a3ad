<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MembershipTier;
use App\Models\UserMembership;
use App\Models\AiUsageLog;

class TestMembershipSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:membership';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the membership system functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Membership System...');
        
        // Test 1: Check if membership tiers exist
        $this->info('1. Checking membership tiers...');
        $tiers = MembershipTier::all();
        $this->table(
            ['Name', 'Slug', 'Price', 'Daily AI Prompts', 'Chatbot Limit'],
            $tiers->map(function ($tier) {
                return [
                    $tier->name,
                    $tier->slug,
                    'Rp ' . number_format($tier->price),
                    $tier->daily_ai_prompts,
                    $tier->chatbot_products_limit === -1 ? 'Unlimited' : $tier->chatbot_products_limit,
                ];
            })
        );

        // Test 2: Check if users have default memberships
        $this->info('2. Checking user memberships...');
        $users = User::with('currentMembership.membershipTier')->take(5)->get();
        
        if ($users->count() > 0) {
            $this->table(
                ['User Email', 'Current Tier', 'Status', 'Started At'],
                $users->map(function ($user) {
                    $membership = $user->currentMembership;
                    return [
                        $user->email,
                        $membership ? $membership->membershipTier->name : 'No membership',
                        $membership ? $membership->status : 'N/A',
                        $membership ? $membership->started_at->format('Y-m-d H:i') : 'N/A',
                    ];
                })
            );
        } else {
            $this->warn('No users found in the database.');
        }

        // Test 3: Test AI usage tracking
        $this->info('3. Testing AI usage tracking...');
        $testUser = User::first();
        
        if ($testUser) {
            $this->info("Testing with user: {$testUser->email}");
            
            // Get current tier
            $currentTier = $testUser->getCurrentMembershipTier();
            $this->info("Current tier: " . ($currentTier ? $currentTier->name : 'No tier'));
            
            // Check current usage
            $todayUsage = AiUsageLog::getTotalTodayUsage($testUser->id);
            $remainingPrompts = $testUser->getRemainingAiPrompts();
            $canUseAi = $testUser->canUseAi();
            
            $this->info("Today's usage: {$todayUsage}");
            $this->info("Remaining prompts: " . ($remainingPrompts === -1 ? 'Unlimited' : $remainingPrompts));
            $this->info("Can use AI: " . ($canUseAi ? 'Yes' : 'No'));
            
            // Test incrementing usage
            $this->info('Incrementing AI usage...');
            AiUsageLog::incrementUsage($testUser->id, 'chat', 'Test usage from command');
            
            $newUsage = AiUsageLog::getTotalTodayUsage($testUser->id);
            $newRemainingPrompts = $testUser->getRemainingAiPrompts();
            
            $this->info("New usage: {$newUsage}");
            $this->info("New remaining prompts: " . ($newRemainingPrompts === -1 ? 'Unlimited' : $newRemainingPrompts));
        } else {
            $this->warn('No users found to test AI usage tracking.');
        }

        // Test 4: Test chatbot permissions
        $this->info('4. Testing chatbot permissions...');
        if ($testUser) {
            $canActivateChatbot = $testUser->canActivateChatbot();
            $this->info("Can activate chatbot: " . ($canActivateChatbot ? 'Yes' : 'No'));
        }

        $this->info('Membership system test completed!');
        
        return Command::SUCCESS;
    }
}
