<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        $this->call(HelpCenterSeeder::class);
        $this->call(DocumentationSeeder::class);
        $this->call(MembershipTierSeeder::class); // Add membership tiers first
        $this->call(RoleSeeder::class); // Add roles and permissions before users
        $this->call(UserSeeder::class);
        $this->call(SellerApplicationSeeder::class); // Add this line to create seller applications
        $this->call(ProductCategorySeeder::class);
        $this->call(NewCourseCategorySeeder::class); // Add course-specific categories
        $this->call(AddProductOtherCategoriesSeeder::class); // Add "Other" categories at all levels
        $this->call(ProductSeeder::class);
        $this->call(CourseSeeder::class); // Add course content seeder
        $this->call(ExistingUserCoursePurchaseSeeder::class); // Add course purchases to existing users
        $this->call(OrderSeeder::class); // Add product orders
    }
}
