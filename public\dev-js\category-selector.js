/**
 * Category Selector Script
 *
 * This script handles the interaction between legacy categories and detailed categories
 * in the product creation/edit forms.
 */
document.addEventListener("DOMContentLoaded", function () {
    // Elements
    const legacyCategory = document.getElementById("category");
    const categorySearch = document.getElementById("category_search");
    const categoryDropdown = document.getElementById("category_dropdown");
    const categoryOptions = document.querySelectorAll(".category-option");
    const categoryIdInput = document.getElementById("category_id");
    const subcategoryIdInput = document.getElementById("subcategory_id");
    const detailedCategoryIdInput = document.getElementById(
        "detailed_category_id"
    );
    const selectedCategory = document.getElementById("selected_category");
    const selectedCategoryName = document.getElementById(
        "selected_category_name"
    );
    const clearCategory = document.getElementById("clear_category");
    const categoryGroups = document.querySelectorAll(".category-group");

    // Use the legacy category mappings provided by the server via the Blade template
    // If they're not provided, use empty objects as fallbacks
    // These variables are declared in the Blade template

    // Initialize with any existing value
    if (detailedCategoryIdInput && detailedCategoryIdInput.value) {
        const selectedOption = document.querySelector(
            `.category-option[data-id="${detailedCategoryIdInput.value}"]`
        );
        if (selectedOption) {
            selectedCategoryName.textContent =
                selectedOption.getAttribute("data-name");
            selectedCategory.classList.remove("hidden");
        }
    }

    // Show all detailed categories
    function showAllCategories() {
        // Show all top-level categories
        categoryGroups.forEach((group) => {
            group.style.display = "block";

            // Show the group header
            const groupHeader = group.querySelector(".font-medium");
            if (groupHeader) {
                groupHeader.style.display = "block";
            }

            // Show all subcategories
            const subcategories = group.querySelectorAll(".pl-3");
            subcategories.forEach((subcat) => {
                subcat.style.display = "block";

                // Show the subcategory header
                const subcatHeader = subcat.querySelector(".font-medium");
                if (subcatHeader) {
                    subcatHeader.style.display = "block";
                }

                // Show all detailed categories
                const detailedCategories =
                    subcat.querySelectorAll(".category-option");
                detailedCategories.forEach((option) => {
                    option.parentElement.style.display = "block";
                });
            });
        });
    }

    // Filter categories based on legacy category selection
    function filterCategoriesByLegacy() {
        if (!legacyCategory) return;

        const selectedLegacyCategory = legacyCategory.value; // e.g., "template"
        const selectedGroup = legacyCategoryMapping[selectedLegacyCategory]; // "Productivity Tools"
        const selectedSubcategory =
            legacySubcategoryMapping[selectedLegacyCategory]; // "Templates"

        // If no legacy category is selected, show everything
        if (!selectedLegacyCategory || !selectedGroup) {
            showAllCategories();
            return;
        }

        // Hide all groups initially
        categoryGroups.forEach((group) => {
            group.style.display = "none";
        });

        // Show only the matching group and its relevant subcategory
        categoryGroups.forEach((group) => {
            const groupName = group
                .querySelector(".font-medium")
                .textContent.trim();
            if (groupName === selectedGroup) {
                group.style.display = "block";
                // Hide the group header
                const groupHeader = group.querySelector(".font-medium");
                if (groupHeader) {
                    groupHeader.style.display = "none";
                }

                // Select only subcategory containers
                const subcategories = group.querySelectorAll(".pl-3.mb-1");
                subcategories.forEach((subcat) => {
                    const subcategoryName = subcat
                        .querySelector(".font-medium")
                        .textContent.trim();
                    if (subcategoryName === selectedSubcategory) {
                        subcat.style.display = "block";
                        // Hide the subcategory header
                        const subcatHeader =
                            subcat.querySelector(".font-medium");
                        if (subcatHeader) {
                            subcatHeader.style.display = "none";
                        }
                        // Show all detailed categories
                        const detailedCategories =
                            subcat.querySelectorAll(".category-option");
                        detailedCategories.forEach((option) => {
                            option.parentElement.style.display = "block";
                        });
                    } else {
                        subcat.style.display = "none";
                    }
                });
            }
        });
    }

    // Filter categories based on search term
    function filterCategoriesBySearch(searchTerm) {
        if (!searchTerm) {
            filterCategoriesByLegacy();
            return;
        }

        searchTerm = searchTerm.toLowerCase();
        const selectedLegacyCategory = legacyCategory
            ? legacyCategory.value
            : "";
        const selectedGroup = legacyCategoryMapping[selectedLegacyCategory];
        const selectedSubcategory =
            legacySubcategoryMapping[selectedLegacyCategory];

        // If a legacy category is selected, we only want to search within that subcategory's detailed categories
        if (selectedLegacyCategory && selectedGroup && selectedSubcategory) {
            // First apply the legacy filter to show only the detailed categories
            filterCategoriesByLegacy();

            // Then filter the detailed categories by search term
            let hasVisibleOptions = false;

            // Find the matching subcategory and filter its detailed categories
            categoryGroups.forEach((group) => {
                const groupName = group
                    .querySelector(".font-medium")
                    .textContent.trim();

                if (groupName === selectedGroup) {
                    const subcategories = group.querySelectorAll(".pl-3");
                    subcategories.forEach((subcat) => {
                        const subcategoryName = subcat
                            .querySelector(".font-medium")
                            .textContent.trim();

                        if (subcategoryName === selectedSubcategory) {
                            // Filter the detailed categories by search term
                            const detailedCategories =
                                subcat.querySelectorAll(".category-option");
                            detailedCategories.forEach((option) => {
                                const categoryName = option.textContent
                                    .trim()
                                    .toLowerCase();
                                const matches =
                                    categoryName.includes(searchTerm);

                                if (matches) {
                                    option.parentElement.style.display =
                                        "block";
                                    hasVisibleOptions = true;
                                } else {
                                    option.parentElement.style.display = "none";
                                }
                            });
                        }
                    });
                }
            });

            return hasVisibleOptions;
        } else {
            // If no legacy category is selected, show all categories and filter by search term
            showAllCategories();

            // Filter all detailed categories by search term
            let hasVisibleOptions = false;

            // Filter detailed categories
            categoryOptions.forEach((option) => {
                const categoryName = option.textContent.trim().toLowerCase();
                const categoryPath = option
                    .getAttribute("data-name")
                    .toLowerCase();
                const matches =
                    categoryName.includes(searchTerm) ||
                    categoryPath.includes(searchTerm);

                if (matches) {
                    option.parentElement.style.display = "block";
                    hasVisibleOptions = true;
                } else {
                    option.parentElement.style.display = "none";
                }
            });

            // Hide subcategories with no visible options
            document.querySelectorAll(".pl-3").forEach((subcategory) => {
                if (subcategory.style.display !== "none") {
                    const visibleOptions = Array.from(
                        subcategory.querySelectorAll(".category-option")
                    ).filter(
                        (option) =>
                            option.parentElement.style.display !== "none"
                    ).length;

                    if (visibleOptions === 0) {
                        subcategory.style.display = "none";
                    }
                }
            });

            // Hide groups with no visible subcategories
            document.querySelectorAll(".category-group").forEach((group) => {
                if (group.style.display !== "none") {
                    const visibleSubcategories = group.querySelectorAll(
                        '.pl-3[style="display: block"]'
                    ).length;

                    if (visibleSubcategories === 0) {
                        group.style.display = "none";
                    }
                }
            });

            return hasVisibleOptions;
        }
    }

    // Show dropdown on focus or click
    categorySearch.addEventListener("focus", function () {
        categoryDropdown.classList.remove("hidden");
        filterCategoriesByLegacy();
    });

    categorySearch.addEventListener("click", function () {
        categoryDropdown.classList.remove("hidden");
        filterCategoriesByLegacy();
    });

    // Filter categories on input
    categorySearch.addEventListener("input", function () {
        const searchTerm = this.value.toLowerCase();
        filterCategoriesBySearch(searchTerm);
    });

    // Select category on click
    categoryOptions.forEach((option) => {
        option.addEventListener("click", function () {
            const detailedCategoryId = this.getAttribute("data-id");
            const categoryName = this.getAttribute("data-name");
            const categoryId = this.getAttribute("data-category-id");
            const subcategoryId = this.getAttribute("data-subcategory-id");

            // Set all category IDs
            if (detailedCategoryIdInput)
                detailedCategoryIdInput.value = detailedCategoryId;
            if (subcategoryIdInput) subcategoryIdInput.value = subcategoryId;
            if (categoryIdInput) categoryIdInput.value = categoryId;

            selectedCategoryName.textContent = categoryName;
            selectedCategory.classList.remove("hidden");
            categorySearch.value = "";
            categoryDropdown.classList.add("hidden");
        });
    });

    // Clear selected category
    clearCategory.addEventListener("click", function () {
        if (detailedCategoryIdInput) detailedCategoryIdInput.value = "";
        if (subcategoryIdInput) subcategoryIdInput.value = "";
        if (categoryIdInput) categoryIdInput.value = "";

        selectedCategoryName.textContent = "";
        selectedCategory.classList.add("hidden");
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", function (event) {
        if (
            !categorySearch.contains(event.target) &&
            !categoryDropdown.contains(event.target)
        ) {
            categoryDropdown.classList.add("hidden");
        }
    });

    // Update detailed categories when legacy category changes
    if (legacyCategory) {
        legacyCategory.addEventListener("change", function () {
            // Clear the current selection
            if (detailedCategoryIdInput) detailedCategoryIdInput.value = "";
            if (subcategoryIdInput) subcategoryIdInput.value = "";
            if (categoryIdInput) categoryIdInput.value = "";

            selectedCategoryName.textContent = "";
            selectedCategory.classList.add("hidden");

            // Show the dropdown and filter categories
            categoryDropdown.classList.remove("hidden");
            filterCategoriesByLegacy();
        });
    }
});
