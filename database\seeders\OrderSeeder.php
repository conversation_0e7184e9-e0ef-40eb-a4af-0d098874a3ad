<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\Payment;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all buyers (users with only 'user' role)
        $buyers = User::whereHas('activeRoles', function ($query) {
            $query->where('slug', 'user');
        })->whereDoesntHave('activeRoles', function ($query) {
            $query->whereIn('slug', ['seller', 'admin', 'superadmin']);
        })->get();

        // Get all active products
        $products = Product::where('status', 'active')->get();

        if ($buyers->isEmpty()) {
            $this->command->info('No buyers found. Please run UserSeeder first.');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->info('No active products found. Please run ProductSeeder first.');
            return;
        }

        $this->command->info('Creating orders for ' . $buyers->count() . ' buyers...');

        // Order statuses and payment methods
        $orderStatuses = ['success', 'pending', 'cancel', 'expired', 'failed'];
        $paymentMethods = ['credit_card', 'bank_transfer', 'e_wallet'];

        // Create orders
        $ordersCreated = 0;
        $paymentsCreated = 0;

        foreach ($buyers as $buyer) {
            // Create 0-5 orders per buyer
            $numOrders = rand(0, 5);

            for ($i = 1; $i <= $numOrders; $i++) {
                // Randomly select a product
                $product = $products->random();

                // Calculate amount (use discount price if available)
                $amount = $product->discount_price ?? $product->price;

                // Randomly select order status
                $orderStatus = $orderStatuses[array_rand($orderStatuses)];

                // Create a unique order ID
                $orderId = 'ORD-' . strtoupper(Str::random(8));

                // Random dates within the last 90 days
                $createdAt = now()->subDays(rand(1, 90));
                $updatedAt = clone $createdAt;

                // For non-pending statuses, update date should be after created date
                if ($orderStatus !== 'pending') {
                    // Different time ranges based on status
                    if ($orderStatus === 'success') {
                        // Success typically takes longer
                        $updatedAt = (clone $createdAt)->addHours(rand(1, 72));
                    } elseif ($orderStatus === 'expired') {
                        // Expiration typically happens after 24 hours
                        $updatedAt = (clone $createdAt)->addHours(24)->addMinutes(rand(1, 60));
                    } elseif ($orderStatus === 'cancel') {
                        // Cancellations typically happen quickly
                        $updatedAt = (clone $createdAt)->addMinutes(rand(5, 120));
                    } elseif ($orderStatus === 'failed') {
                        // Failures typically happen quickly
                        $updatedAt = (clone $createdAt)->addMinutes(rand(1, 30));
                    }
                }

                // Create the order
                $order = Order::create([
                    'id' => (string) Str::uuid(),
                    'order_id' => $orderId,
                    'buyer_id' => $buyer->id,
                    'seller_id' => $product->seller_id,
                    'product_id' => $product->id,
                    'amount' => $amount,
                    'status' => $orderStatus,
                    'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                    'snap_token' => in_array($orderStatus, ['success', 'pending']) ? Str::random(32) : null,
                    'created_at' => $createdAt,
                    'updated_at' => $updatedAt,
                ]);

                $ordersCreated++;

                // Create payment record for all orders except pending ones
                if ($orderStatus !== 'pending') {
                    $amount = $order->amount;
                    $fee = round($amount * 0.05, 2); // 5% platform fee
                    $netAmount = $amount - $fee;

                    // Payment date should be after order date but before or equal to update date
                    $paidAt = null;
                    $paymentStatus = 'pending';

                    if ($orderStatus === 'success') {
                        $paymentStatus = 'paid';
                        $paidAt = (clone $createdAt)->addHours(rand(1, 24));
                        if ($paidAt > $updatedAt) {
                            $paidAt = $updatedAt;
                        }
                    } elseif ($orderStatus === 'failed' || $orderStatus === 'expired') {
                        $paymentStatus = 'failed';
                    } elseif ($orderStatus === 'cancel') {
                        // For cancelled orders, payment could be pending or failed
                        $paymentStatus = ['pending', 'failed'][array_rand([0, 1])];
                    }

                    Payment::create([
                        'id' => (string) Str::uuid(),
                        'seller_id' => $product->seller_id,
                        'order_id' => $order->id,
                        'amount' => $amount,
                        'fee' => $fee,
                        'net_amount' => $netAmount,
                        'status' => $paymentStatus,
                        'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                        'transaction_id' => 'TXN-' . strtoupper(Str::random(10)),
                        'paid_at' => $paidAt,
                        'created_at' => $createdAt,
                        'updated_at' => $updatedAt,
                    ]);

                    $paymentsCreated++;
                }
            }
        }

        $this->command->info("Created $ordersCreated orders and $paymentsCreated payments successfully!");
    }
}
