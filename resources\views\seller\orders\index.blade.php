@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div class="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div class="animate-fadeIn">
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">Orders</h1>
            <p class="text-gray-600">Manage your customer orders</p>
        </div>
        <div class="flex items-center gap-3">
            <div x-data="{ open: false }" class="relative">
                <button @click="open = !open" class="inline-flex items-center gap-1 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50 hover:shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Export
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 transition-transform duration-200" :class="{'rotate-180': open}">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 rounded-lg bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-10">
                    <a href="{{ route('seller.orders.export.csv', [], false) }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <span>Export as CSV</span>
                    </a>

                </div>
            </div>
        </div>
    </div>

    <div class="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        <div class="stat-card transform hover:-translate-y-1 transition-all duration-200">
            <div class="flex items-center justify-between pb-2">
                <h3>Gross Revenue</h3>
                <div class="p-2 rounded-full bg-blue-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-blue-500">
                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                </div>
            </div>
            <div class="value text-blue-700">Rp {{ number_format($totalRevenue ?? 0, 0, ',', '.') }}</div>
            <p>Total sales before fees</p>
        </div>

        <div class="stat-card transform hover:-translate-y-1 transition-all duration-200">
            <div class="flex items-center justify-between pb-2">
                <h3>Net Revenue</h3>
                <div class="p-2 rounded-full bg-green-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-green-500">
                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                </div>
            </div>
            <div class="value text-green-700">Rp {{ number_format($totalNetRevenue ?? 0, 0, ',', '.') }}</div>
            <p>Your earnings after fees</p>
        </div>

        <div class="stat-card transform hover:-translate-y-1 transition-all duration-200">
            <div class="flex items-center justify-between pb-2">
                <h3>Total Fees</h3>
                <div class="p-2 rounded-full bg-red-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-red-500">
                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                </div>
            </div>
            <div class="value text-red-700">Rp {{ number_format($totalFees ?? 0, 0, ',', '.') }}</div>
            <p>Platform & transaction fees</p>
        </div>
        <div class="stat-card transform hover:-translate-y-1 transition-all duration-200">
            <div class="flex items-center justify-between pb-2">
                <h3>Total Orders</h3>
                <div class="p-2 rounded-full bg-indigo-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-indigo-600">
                        <circle cx="8" cy="21" r="1"></circle>
                        <circle cx="19" cy="21" r="1"></circle>
                        <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                    </svg>
                </div>
            </div>
            <div class="value text-indigo-700">{{ $orders->total() ?? 0 }}</div>
            <p>All time orders</p>
        </div>
        <div class="stat-card transform hover:-translate-y-1 transition-all duration-200 sm:col-span-2 md:col-span-1">
            <div class="flex items-center justify-between pb-2">
                <h3>Pending Orders</h3>
                <div class="p-2 rounded-full bg-amber-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-amber-500">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                </div>
            </div>
            <div class="value text-amber-700">{{ $pendingOrders ?? 0 }}</div>
            <p>Require your attention</p>
        </div>
    </div>

    <div class="rounded-lg border bg-white shadow-sm">
        <div class="flex flex-col gap-4 border-b p-6 sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h3 class="text-lg font-medium">Order History</h3>
                <p class="text-sm text-gray-500">Manage and track your customer orders</p>
            </div>
            <div class="flex flex-col sm:flex-row items-center gap-3">
                <form action="{{ route('seller.orders.index') }}" method="GET" class="w-full sm:w-auto">
                    <div class="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.3-4.3"></path>
                        </svg>
                        <input type="search" name="search" placeholder="Search orders..." value="{{ request('search') }}" class="w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 text-sm md:w-[200px] lg:w-[300px] transition-all focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <button type="submit" class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <span class="sr-only">Search</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <polyline points="9 18 15 12 9 6"></polyline>
                            </svg>
                        </button>
                    </div>
                </form>
                <!-- Status filter removed - only showing successful orders -->
            </div>
        </div>
        <div class="table-container overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr>
                        <th class="hidden md:table-cell">Order ID</th>
                        <th>Customer</th>
                        <th>Product</th>
                        <th class="text-right">Amount</th>
                        <th class="hidden sm:table-cell">Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders ?? collect() as $order)
                    <tr class="hover:bg-gray-50 transition-colors">
                        <td class="font-medium hidden md:table-cell">#{{ $order->order_id ?? 'N/A' }}</td>
                        <td>
                            <div class="md:hidden font-bold text-xs text-gray-500 mb-1">Order #{{ $order->order_id ?? 'N/A' }}</div>
                            {{ $order->user->name ?? 'Unknown Customer' }}
                        </td>
                        <td>{{ $order->item_name }}</td>
                        <td class="text-right font-medium">Rp {{ number_format($order->amount ?? 0, 0, ',', '.') }}</td>
                        <td class="whitespace-nowrap hidden sm:table-cell">{{ $order->created_at->format('M d, Y') ?? 'N/A' }}</td>
                        <td>
                            <div class="sm:hidden text-xs text-gray-500 mb-1">{{ $order->created_at->format('M d, Y') ?? 'N/A' }}</div>
                            <span class="status-label inline-flex items-center justify-center
                                @if(($order->status ?? 'pending') === 'success') success
                                @elseif(($order->status ?? 'pending') === 'pending') pending
                                @elseif(($order->status ?? 'pending') === 'cancel') cancel
                                @elseif(($order->status ?? 'pending') === 'expired') expired
                                @elseif(($order->status ?? 'pending') === 'failed') failed
                                @endif">
                                {{ ucfirst($order->status ?? 'pending') }}
                            </span>
                        </td>
                        <td>
                            <a href="{{ route('seller.orders.show', $order, false) ?? '#' }}" class="text-indigo-600 hover:text-indigo-900 font-medium transition-colors">View</a>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center text-gray-500 py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                            </svg>
                            <p>No orders found</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="border-t px-6 py-4">
            {{ ($orders ?? collect())->links() }}
        </div>
    </div>
</div>
@endsection