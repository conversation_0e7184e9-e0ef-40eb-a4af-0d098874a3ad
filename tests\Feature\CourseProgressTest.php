<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\UserCourseProgress;
use App\Models\CourseCurriculumItem;
use App\Models\CourseSection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CourseProgressTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_course_progress_creation()
    {
        // Create a user and course
        $user = User::factory()->create();
        $course = Course::factory()->create();

        // Create progress record
        $progress = UserCourseProgress::getOrCreate($user->id, $course->id);

        $this->assertInstanceOf(UserCourseProgress::class, $progress);
        $this->assertEquals(0.00, $progress->progress_percentage);
        $this->assertFalse($progress->is_completed);
        $this->assertEquals([], $progress->completed_items);
    }

    public function test_marking_curriculum_item_completed()
    {
        // Create test data
        $user = User::factory()->create();
        $course = Course::factory()->create();
        $section = CourseSection::factory()->create(['course_id' => $course->id]);
        $item = CourseCurriculumItem::factory()->create([
            'course_id' => $course->id,
            'section_id' => $section->id,
            'is_active' => true
        ]);

        // Create progress and mark item completed
        $progress = UserCourseProgress::getOrCreate($user->id, $course->id);
        $progress->markItemCompleted($item->id);

        $this->assertTrue($progress->isItemCompleted($item->id));
        $this->assertContains($item->id, $progress->completed_items);
        $this->assertEquals($item->id, $progress->current_curriculum_item_id);
    }

    public function test_progress_percentage_calculation()
    {
        // Create test data
        $user = User::factory()->create();
        $course = Course::factory()->create();
        $section = CourseSection::factory()->create(['course_id' => $course->id]);
        
        // Create 4 curriculum items
        $items = [];
        for ($i = 0; $i < 4; $i++) {
            $items[] = CourseCurriculumItem::factory()->create([
                'course_id' => $course->id,
                'section_id' => $section->id,
                'is_active' => true
            ]);
        }

        $progress = UserCourseProgress::getOrCreate($user->id, $course->id);

        // Complete 2 out of 4 items (50%)
        $progress->markItemCompleted($items[0]->id);
        $progress->markItemCompleted($items[1]->id);

        $this->assertEquals(50.00, $progress->progress_percentage);
        $this->assertFalse($progress->is_completed);

        // Complete all items (100%)
        $progress->markItemCompleted($items[2]->id);
        $progress->markItemCompleted($items[3]->id);

        $this->assertEquals(100.00, $progress->progress_percentage);
        $this->assertTrue($progress->is_completed);
        $this->assertNotNull($progress->completed_at);
    }

    public function test_reset_progress()
    {
        // Create test data
        $user = User::factory()->create();
        $course = Course::factory()->create();
        $section = CourseSection::factory()->create(['course_id' => $course->id]);
        $item = CourseCurriculumItem::factory()->create([
            'course_id' => $course->id,
            'section_id' => $section->id,
            'is_active' => true
        ]);

        // Create progress and complete it
        $progress = UserCourseProgress::getOrCreate($user->id, $course->id);
        $progress->markItemCompleted($item->id);
        $progress->update(['is_completed' => true, 'completed_at' => now()]);

        // Reset progress
        $progress->resetProgress();

        $this->assertEquals(0.00, $progress->progress_percentage);
        $this->assertFalse($progress->is_completed);
        $this->assertNull($progress->completed_at);
        $this->assertEquals([], $progress->completed_items);
        $this->assertNull($progress->current_curriculum_item_id);
    }
}
