/* Shared styles for user dashboard pages */

/* Cards */
.stat-card {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.stat-card h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.stat-card .value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0.5rem 0;
}

.stat-card p {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Dashboard cards */
.dashboard-card {
    background-color: #fff;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.dashboard-card .card-header {
    padding: 1.25rem;
    border-bottom: 1px solid #e5e7eb;
}

.dashboard-card .card-body {
    padding: 1.25rem;
}

.dashboard-card .card-footer {
    padding: 0.75rem 1.25rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

/* Buttons and Links */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #4f46e5;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #4338ca;
}

.btn-secondary {
    background-color: #ffffff;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background-color: #f9fafb;
}

/* Tables */
.table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-container table {
    width: 100%;
    border-collapse: collapse;
}

.table-container th,
.table-container td {
    padding: 1rem;
    text-align: left;
    font-size: 0.875rem;
}

.table-container th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    font-size: 0.75rem;
}

.table-container td {
    color: #374151;
}

.table-container tr {
    border-bottom: 1px solid #e5e7eb;
}

.table-container tbody tr:hover {
    background-color: #f9fafb;
}

/* Status Labels */
.status-label {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-label.completed,
.status-label.success,
.status-label.paid {
    border: 1px solid #d4f4e2;
    background-color: #e6f9ed;
    color: #2f855a;
}

.status-label.pending {
    border: 1px solid #feebc8;
    background-color: #fffaf0;
    color: #c05621;
}

.status-label.cancelled,
.status-label.cancel,
.status-label.failed {
    border: 1px solid #fee2e2;
    background-color: #fef2f2;
    color: #dc2626;
}

.status-label.expired {
    border: 1px solid #e5e7eb;
    background-color: #f3f4f6;
    color: #6b7280;
}

/* Purchase items */
.purchase-item {
    display: flex;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.purchase-item:last-child {
    border-bottom: none;
}

.purchase-item .purchase-image {
    width: 4rem;
    height: 4rem;
    border-radius: 0.375rem;
    overflow: hidden;
    border: 1px solid #e5e7eb;
}

.purchase-item .purchase-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.purchase-item .purchase-details {
    flex: 1;
    margin-left: 1rem;
}

.purchase-item .purchase-title {
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

.purchase-item .purchase-meta {
    font-size: 0.75rem;
    color: #6b7280;
}

.purchase-item .purchase-actions {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
}

.purchase-item .purchase-actions a {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4f46e5;
}

.purchase-item .purchase-actions a:hover {
    color: #4338ca;
}

/* Empty states */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1.5rem;
    text-align: center;
}

.empty-state svg {
    width: 3rem;
    height: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    /* Cards */
    .stat-card {
        padding: 1rem;
    }

    .stat-card .value {
        font-size: 1.25rem;
    }

    .stat-card h3,
    .stat-card p {
        font-size: 0.75rem;
    }

    /* Tables */
    .table-container th,
    .table-container td {
        padding: 0.75rem;
        font-size: 0.75rem;
    }

    .table-container th {
        font-size: 0.675rem;
    }

    /* Stack cards on mobile */
    .grid.md\:grid-cols-3,
    .grid.md\:grid-cols-2 {
        grid-template-columns: 1fr;
    }

    /* Forms and Selects */
    select,
    input {
        font-size: 0.75rem;
        padding: 0.5rem;
    }
}

@media (max-width: 640px) {
    /* Further adjustments for very small screens */
    .table-container th,
    .table-container td {
        padding: 0.5rem;
    }

    .stat-card .value {
        font-size: 1.125rem;
    }
}
