<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing products that don't have a product_type set
        // Set default based on content_type: course -> course, simple -> digital
        DB::table('products')
            ->whereNull('product_type')
            ->where('content_type', 'course')
            ->update(['product_type' => 'course']);

        DB::table('products')
            ->whereNull('product_type')
            ->where('content_type', 'simple')
            ->update(['product_type' => 'digital']);

        // For any remaining products without content_type, set both to defaults
        DB::table('products')
            ->whereNull('product_type')
            ->update([
                'product_type' => 'digital',
                'content_type' => 'simple'
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't need to reverse this as it's just setting default values
        // for backward compatibility
    }
};
