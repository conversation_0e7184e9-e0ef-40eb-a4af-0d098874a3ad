<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;


class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $sellerId = Auth::id();
        $search = $request->input('search');

        // Only show successful orders to sellers
        // Get orders for products and courses sold by this seller with status 'success'
        $query = Order::where('seller_id', $sellerId)
            ->where('status', 'success')
            ->with(['product', 'course', 'user']);

        // Apply search if provided
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('order_id', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('product', function($productQuery) use ($search) {
                      $productQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('course', function($courseQuery) use ($search) {
                      $courseQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString(); // Preserve search parameters in pagination links

        // Calculate total revenue from successful orders of seller's products and courses
        $successfulOrders = Order::where('seller_id', $sellerId)
            ->where('status', 'success')
            ->get();

        $feeService = new \App\Services\FeeCalculationService();
        $revenueData = $feeService->calculateTotalSellerRevenue($successfulOrders);

        $totalRevenue = $revenueData['total_gross_amount'];
        $totalNetRevenue = $revenueData['total_net_amount'];
        $totalFees = $revenueData['total_fees'];

        // We're not showing pending orders anymore, but keeping the variable for the view
        $pendingOrders = 0;

        // Set status to 'success' for the view
        $status = 'success';

        return view('seller.orders.index', compact('orders', 'status', 'totalRevenue', 'totalNetRevenue', 'totalFees', 'pendingOrders'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order)
    {
        $sellerId = Auth::id();

        // Ensure the order belongs to this seller AND has status 'success'
        if ($order->seller_id !== $sellerId || $order->status !== 'success') {
            abort(403, 'This order does not belong to you or is not a successful order');
        }

        $order->load(['product', 'course', 'user']);

        return view('seller.orders.show', compact('order'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Order $order)
    {
        // Since we're only showing successful orders to sellers,
        // we should disable the ability to update order status
        abort(403, 'Updating order status is not allowed');

        // Keeping the code below for reference, but it won't be executed
        $sellerId = Auth::id();

        // Ensure the order is for a product sold by this seller
        if (!$order->product || $order->product->seller_id !== $sellerId) {
            abort(403, 'This order does not belong to your products');
        }

        $validated = $request->validate([
            'status' => 'required|in:success',
        ]);

        $order->status = $validated['status'];
        $order->save();

        return redirect()->route('seller.orders.show', $order)
            ->with('success', 'Order status updated successfully!');
    }

    /**
     * Download the order invoice.
     */
    public function downloadInvoice(Order $order)
    {
        $sellerId = Auth::id();

        // Ensure the order belongs to this seller AND has status 'success'
        if ($order->seller_id !== $sellerId || $order->status !== 'success') {
            abort(403, 'This order does not belong to you or is not a successful order');
        }

        // In a real application, you would generate a PDF invoice here
        // For this example, we'll just return a message

        return back()->with('info', 'Invoice download functionality would be implemented here.');
    }

    /**
     * Export orders as CSV
     */
    public function exportCsv()
    {
        $sellerId = Auth::id();

        // Get orders for products and courses sold by this seller with status 'success'
        $orders = Order::where('seller_id', $sellerId)
            ->where('status', 'success')
            ->with(['product', 'course', 'user'])
            ->orderBy('created_at', 'desc')
            ->get();

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="orders.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'Order ID',
                'Customer',
                'Product',
                'Amount (Rp)',
                'Date',
                'Status'
            ]);

            // Add order data
            foreach ($orders as $order) {
                fputcsv($file, [
                    '#' . ($order->order_id ?? 'N/A'),
                    $order->user->name ?? 'Unknown Customer',
                    $order->item_name,
                    number_format($order->amount ?? 0, 0, ',', '.'),
                    $order->created_at->format('M d, Y') ?? 'N/A',
                    ucfirst($order->status ?? 'pending')
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }


}