@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div class="flex items-center gap-4">
        <a href="{{ route('seller.payments.index', [], false) ?? '#' }}" class="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                <path d="m15 18-6-6 6-6"></path>
            </svg>
            <span class="sr-only">Back</span>
        </a>
        <!-- TODO: Revisit route 'seller.payments.index' if it fails -->
        <div>
            <h1 class="text-3xl font-bold tracking-tight">Payment Details</h1>
            <p class="text-gray-500">Transaction ID: {{ $payment->transaction_id ?? 'N/A' }}</p>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-3">
        <div class="md:col-span-2 space-y-6">
            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Payment Information</h3>
                </div>
                <div class="p-6">
                    @php
                        $sellerApplication = $payment->seller->sellerApplication;
                    @endphp
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Transaction ID</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $payment->transaction_id ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $payment->created_at->format('F d, Y') ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="mt-1 text-sm">
                                <span class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium
                                    {{ ($payment->status ?? 'pending') === 'paid' ? 'border-green-100 bg-green-50 text-green-700' : 
                                       (($payment->status ?? 'pending') === 'failed' ? 'border-red-100 bg-red-50 text-red-700' : 
                                       'border-amber-100 bg-amber-50 text-amber-700') }}">
                                    {{ ucfirst($payment->status ?? 'pending') }}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ ucfirst($sellerApplication->payment_method ?? 'N/A') }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Order Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">#{{ $payment->order->order_number ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Customer</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $payment->order->user->name ?? 'Unknown Customer' }}</dd>
                        </div>
                        @if($payment->paid_at)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Paid Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $payment->paid_at->format('F d, Y') ?? 'N/A' }}</dd>
                        </div>
                        @endif
                    </dl>
                </div>
            </div>

            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Product Information</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-4">
                        <div class="h-16 w-16 flex-shrink-0 rounded-md bg-gray-100">
                            <img src="{{ asset('images/placeholder.jpg') }}" alt="{{ $payment->order->product->name ?? 'Product' }}" class="h-16 w-16 rounded-md object-cover">
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">{{ $payment->order->product->name ?? 'Deleted Product' }}</h4>
                            <p class="text-sm text-gray-500">{{ ucfirst($payment->order->product->category ?? 'Uncategorized') }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">${{ number_format($payment->order->product->price ?? 0, 2) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="space-y-6">
            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Payment Summary</h3>
                </div>
                <div class="p-6">
                    <dl class="space-y-4">
                        <div class="flex items-center justify-between">
                            <dt class="text-sm text-gray-500">Gross Amount</dt>
                            <dd class="text-sm font-medium text-gray-900">${{ number_format($payment->amount ?? 0, 2) }}</dd>
                        </div>
                        <div class="flex items-center justify-between">
                            <dt class="text-sm text-gray-500">Platform Fee</dt>
                            <dd class="text-sm font-medium text-gray-900">${{ number_format($payment->fee ?? 0, 2) }}</dd>
                        </div>
                        <div class="border-t border-gray-200 pt-4 flex items-center justify-between">
                            <dt class="text-base font-medium text-gray-900">Net Amount</dt>
                            <dd class="text-base font-medium text-gray-900">${{ number_format($payment->net_amount ?? 0, 2) }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Actions</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <a href="{{ route('seller.payments.downloadReceipt', $payment, false) ?? '#' }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Download Receipt
                        </a>
                        <!-- TODO: Revisit route 'seller.payments.downloadReceipt' if it fails -->
                        
                        <a href="{{ route('seller.orders.show', $payment->order, false) ?? '#' }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <circle cx="8" cy="21" r="1"></circle>
                                <circle cx="19" cy="21" r="1"></circle>
                                <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                            </svg>
                            View Order Details
                        </a>
                        <!-- TODO: Revisit route 'seller.orders.show' if it fails -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection