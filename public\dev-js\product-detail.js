/**
 * Product detail page functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Thumbnail click handler for image gallery
    const mainImage = document.getElementById('main-image');
    const thumbnailContainers = document.querySelectorAll('.thumbnail-container');

    // Add click event to each thumbnail
    thumbnailContainers.forEach(container => {
        container.addEventListener('click', function() {
            // Get the image source from the thumbnail
            const thumbnailImg = this.querySelector('.thumbnail');
            const imgSrc = thumbnailImg.getAttribute('data-src');

            // Update the main image
            mainImage.src = imgSrc;

            // Remove border from all thumbnails
            thumbnailContainers.forEach(item => {
                item.classList.remove('border-2', 'border-indigo-500');
            });

            // Add border to the clicked thumbnail
            this.classList.add('border-2', 'border-indigo-500');
        });
    });
});
