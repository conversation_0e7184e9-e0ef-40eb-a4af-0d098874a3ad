<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        // Create Admin User
        $adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Adm!n2024#SecurePlatform'),
        ]);
        $adminUser->assignRole('admin');

        // Create Super Admin User (renata)
        $superAdminUser = User::create([
            'name' => 'renata',
            'email' => '<EMAIL>',
            'password' => Hash::make('Ren@ta2024#Secure!'),
        ]);
        $superAdminUser->assignRole('superadmin');
        $superAdminUser->assignRole('seller'); // Also assign seller role

        // Create Regular User
        $regularUser = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#A!'),
        ]);
        $regularUser->assignRole('user');

        // Create more regular users
        $customerB = User::create([
            'name' => 'Regular User B',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#B!'),
        ]);
        $customerB->assignRole('user');

        $customerC = User::create([
            'name' => 'Regular User C',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#C!'),
        ]);
        $customerC->assignRole('user');

        $customerD = User::create([
            'name' => 'Regular User D',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer@2024#D!'),
        ]);
        $customerD->assignRole('user');


    }
}
