@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div class="flex items-center gap-4">
        <a href="{{ route('seller.documentation.index') }}" class="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
            </svg>
            <span class="sr-only">Back</span>
        </a>
        <div>
            <h1 class="text-3xl font-bold tracking-tight">{{ $topic->title }}</h1>
            <p class="text-gray-500">{{ $topic->section->title }} • Last updated: {{ is_string($topic->last_updated) ? $topic->last_updated : $topic->last_updated->format('Y-m-d') }}</p>
        </div>
    </div>

    @if(session('success'))
    <div class="rounded-md bg-green-50 p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-green-800">{{ session('success') }}</p>
            </div>
        </div>
    </div>
    @endif

    <div class="grid gap-6 md:grid-cols-4">
        <div class="md:col-span-3">
            <div class="rounded-lg border bg-white p-6 shadow-sm">
                <div class="prose max-w-none">
                    {!! $topic->content !!}
                </div>

                <div class="mt-8 border-t pt-6">
                    <h3 class="text-sm font-medium text-gray-900">Was this article helpful?</h3>
                    <div class="mt-2 flex space-x-2">
                        <form action="{{ route('seller.documentation.feedback', $topic->slug) }}" method="POST">
                            @csrf
                            <input type="hidden" name="type" value="yes">
                            <button type="submit" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium leading-4 text-gray-700 shadow-sm hover:bg-gray-50">
                                <svg xmlns="http://www.w3.org/2000/svg" class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                                </svg>
                                Yes, it helped ({{ $topic->helpful_yes }})
                            </button>
                        </form>
                        <form action="{{ route('seller.documentation.feedback', $topic->slug) }}" method="POST">
                            @csrf
                            <input type="hidden" name="type" value="no">
                            <button type="submit" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium leading-4 text-gray-700 shadow-sm hover:bg-gray-50">
                                <svg xmlns="http://www.w3.org/2000/svg" class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018a2 2 0 01.485.06l3.76.94m-7 10v5a2 2 0 002 2h.096c.5 0 .905-.405.905-.904 0-.715.211-1.413.608-2.008L17 13V4m-7 10h2" />
                                </svg>
                                No, I need more help ({{ $topic->helpful_no }})
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div class="rounded-lg border bg-white p-6 shadow-sm">
                <h3 class="text-sm font-medium text-gray-900">In This Article</h3>
                <nav class="mt-4">
                    <ul class="space-y-3 text-sm">
                        @foreach($toc as $item)
                        <li>
                            <a href="#{{ $item['slug'] }}" class="text-indigo-600 hover:text-indigo-800 hover:underline">{{ $item['text'] }}</a>
                        </li>
                        @endforeach
                    </ul>
                </nav>

                <div class="mt-6 border-t pt-6">
                    <h3 class="text-sm font-medium text-gray-900">Related Articles</h3>
                    <ul class="mt-4 space-y-3">
                        @foreach($topic->relatedTopics as $related)
                        <li>
                            <a href="{{ route('seller.documentation.show', $related->slug) }}" class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 mt-0.5 h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm text-indigo-600 hover:text-indigo-800 hover:underline">{{ $related->title }}</span>
                            </a>
                        </li>
                        @endforeach
                    </ul>
                </div>

                <div class="mt-6 border-t pt-6">
                    <h3 class="text-sm font-medium text-gray-900">Need More Help?</h3>
                    <p class="mt-2 text-sm text-gray-500">Can't find what you're looking for?</p>
                    <div class="mt-4">
                        <a href="{{ route('seller.help-center.contact') }}" class="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-3 py-2 text-sm font-medium leading-4 text-white shadow-sm hover:bg-indigo-700">
                            Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .prose .tip, .prose .note, .prose .warning, .prose .success {
        padding: 1rem;
        margin: 1.5rem 0;
        border-radius: 0.375rem;
    }

    .prose .tip {
        background-color: #EFF6FF;
        border-left: 4px solid #3B82F6;
    }

    .prose .note {
        background-color: #F3F4F6;
        border-left: 4px solid #6B7280;
    }

    .prose .warning {
        background-color: #FEF2F2;
        border-left: 4px solid #EF4444;
    }

    .prose .success {
        background-color: #ECFDF5;
        border-left: 4px solid #10B981;
    }

    .prose .lead {
        font-size: 1.125rem;
        line-height: 1.75rem;
        margin-bottom: 1.5rem;
    }
</style>
@endsection