<?php

if (!function_exists('js_path')) {
    /**
     * Get the path to JavaScript files based on the current environment.
     *
     * This function determines which JavaScript path to use:
     * - If APP_ENV is 'local', it will use 'dev-js'
     * - Otherwise, it will use 'js' (obfuscated files)
     *
     * @return string Either 'dev-js' or 'js' depending on the environment
     */
    function js_path()
    {
        // Use app()->environment() which reads from config('app.env')
        // This is more reliable than env() after the application has been bootstrapped
        return app()->environment('local') ? 'dev-js' : 'js';
    }
}
