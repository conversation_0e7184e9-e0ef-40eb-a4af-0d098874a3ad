<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('seller_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->string('name');
            $table->string('slug')->unique();
            $table->longText('description');
            $table->string('category'); // Keep for backward compatibility
            $table->foreignUuid('category_id')->nullable()->constrained('product_categories')->nullOnDelete();
            $table->foreignUuid('subcategory_id')->nullable()->constrained('product_subcategories')->nullOnDelete();
            $table->foreignUuid('detailed_category_id')->nullable()->constrained('product_detailed_categories')->nullOnDelete();
            $table->decimal('price', 10, 2);
            $table->decimal('discount_price', 10, 2)->nullable();
            $table->enum('status', ['draft', 'active'])->default('draft');
            $table->string('image')->nullable();
            $table->json('files')->nullable(); // Keep for backward compatibility
            $table->boolean('is_featured')->default(false);
            $table->float('average_rating')->nullable();
            $table->unsignedInteger('reviews_count')->default(0);
            $table->uuid('chatbot_data_id')->nullable();
            $table->enum('content_type', ['simple', 'course'])->default('simple'); // New field to distinguish product types
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
