<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class TruncateTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:table:truncate {table : The table to truncate}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Truncate a specific database table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $table = $this->argument('table');
        
        if (!Schema::hasTable($table)) {
            $this->error("Table '$table' does not exist.");
            return 1;
        }
        
        $this->info("Truncating table: $table");
        
        // Disable foreign key checks to allow truncating tables with foreign key constraints
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table($table)->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        $this->info("Table '$table' has been truncated successfully.");
        
        return 0;
    }
}
