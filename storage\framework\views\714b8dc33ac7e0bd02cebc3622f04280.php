<?php $__env->startSection('content'); ?>
<div class="max-w-6xl mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Course</h1>
            <p class="text-gray-600">Update your course information and settings</p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Status Controls in Top Right -->
            <div class="flex items-center space-x-3 bg-white rounded-lg border border-gray-200 px-4 py-2 shadow-sm">
                <label for="header-status" class="text-sm font-medium text-gray-700">Status:</label>
                <select id="header-status" 
                        name="header_status"
                        class="border-0 bg-transparent text-sm font-medium focus:ring-0 focus:outline-none"
                        onchange="updateFormStatus(this.value)">
                    <option value="draft" <?php echo e(old('status', $course->status) === 'draft' ? 'selected' : ''); ?>>Draft</option>
                    <option value="active" <?php echo e(old('status', $course->status) === 'active' ? 'selected' : ''); ?>>Active</option>
                    <option value="inactive" <?php echo e(old('status', $course->status) === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                </select>
            </div>
            <a href="<?php echo e(route('seller.courses.show', $course)); ?>" 
               class="text-gray-600 hover:text-gray-900 font-medium">
                ← Back to Course
            </a>
        </div>
    </div>

    <!-- Course Edit Form -->
    <form action="<?php echo e(route('seller.courses.update', $course)); ?>" method="POST" enctype="multipart/form-data" id="course-edit-form">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        
        <div class="grid gap-6 md:grid-cols-6">
            <div class="space-y-6 md:col-span-4">
        
                <!-- Basic Information -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Course Information</h3>
                        <p class="text-sm text-gray-600">Basic information about your course</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="space-y-2">
                            <label for="title" class="block text-sm font-medium text-gray-700">
                                Course Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   value="<?php echo e(old('title', $course->title)); ?>"
                                   required
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                   placeholder="e.g., Complete Guide to Digital Marketing for Beginners">
                            <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="space-y-2">
                            <label for="short_description" class="block text-sm font-medium text-gray-700">
                                Short Description
                            </label>
                            <textarea id="short_description"
                                      name="short_description"
                                      rows="3"
                                      class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                      placeholder="A concise overview of what this course covers and who it's for..."><?php echo e(old('short_description', $course->short_description)); ?></textarea>
                            <p class="text-xs text-gray-500">
                                Brief summary for course listings (recommended: 100-200 characters)
                            </p>
                            <?php $__errorArgs = ['short_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="space-y-2">
                            <label for="description" class="block text-sm font-medium text-gray-700">
                                Description <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description"
                                      name="description"
                                      rows="8"
                                      required
                                      class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                      placeholder="Provide a comprehensive description of your course including what students will learn, course objectives, prerequisites, and target audience..."><?php echo e(old('description', $course->description)); ?></textarea>
                            <p class="text-xs text-gray-500">
                                Provide a detailed description of your course, including its features and benefits.
                            </p>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Category Selection -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Category</h3>
                        <p class="text-sm text-gray-600">Choose the appropriate category for your course</p>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Main Category *</label>
                    <select id="category_id" 
                            name="category_id" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <option value="">Select Category</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id', $course->category_id) == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="subcategory_id" class="block text-sm font-medium text-gray-700 mb-2">Subcategory *</label>
                    <select id="subcategory_id" 
                            name="subcategory_id" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 <?php $__errorArgs = ['subcategory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <option value="">Select Subcategory</option>
                    </select>
                    <?php $__errorArgs = ['subcategory_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="detailed_category_id" class="block text-sm font-medium text-gray-700 mb-2">Detailed Category *</label>
                    <select id="detailed_category_id" 
                            name="detailed_category_id" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 <?php $__errorArgs = ['detailed_category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <option value="">Select Detailed Category</option>
                    </select>
                    <?php $__errorArgs = ['detailed_category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Pricing and Details -->
    <div class="rounded-xl border bg-white shadow-lg">
        <div class="border-b border-gray-100 p-6">
            <h3 class="text-lg font-semibold text-gray-900">Pricing & Details</h3>
            <p class="text-sm text-gray-600">Set your course pricing and difficulty level</p>
        </div>
        <div class="p-6">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price (IDR) *</label>
                    <input type="number" 
                           id="price" 
                           name="price" 
                           value="<?php echo e(old('price', $course->price)); ?>"
                           min="5000"
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="50000">
                    <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="discount_price" class="block text-sm font-medium text-gray-700 mb-2">Discount Price (IDR)</label>
                    <input type="number" 
                           id="discount_price" 
                           name="discount_price" 
                           value="<?php echo e(old('discount_price', $course->discount_price)); ?>"
                           min="5000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 <?php $__errorArgs = ['discount_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="40000">
                    <?php $__errorArgs = ['discount_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="difficulty_level" class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level *</label>
                    <select id="difficulty_level" 
                            name="difficulty_level" 
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 <?php $__errorArgs = ['difficulty_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                        <option value="">Select Difficulty</option>
                        <option value="beginner" <?php echo e(old('difficulty_level', $course->difficulty_level) === 'beginner' ? 'selected' : ''); ?>>Beginner</option>
                        <option value="intermediate" <?php echo e(old('difficulty_level', $course->difficulty_level) === 'intermediate' ? 'selected' : ''); ?>>Intermediate</option>
                        <option value="advanced" <?php echo e(old('difficulty_level', $course->difficulty_level) === 'advanced' ? 'selected' : ''); ?>>Advanced</option>
                    </select>
                    <?php $__errorArgs = ['difficulty_level'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div>
                    <label for="estimated_duration" class="block text-sm font-medium text-gray-700 mb-2">Estimated Duration (minutes)</label>
                    <input type="number" 
                           id="estimated_duration" 
                           name="estimated_duration" 
                           value="<?php echo e(old('estimated_duration', $course->estimated_duration)); ?>"
                           min="1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 <?php $__errorArgs = ['estimated_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                           placeholder="120">
                    <?php $__errorArgs = ['estimated_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Learning Outcomes and Requirements -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Learning Outcomes & Requirements</h3>
                        <p class="text-sm text-gray-600">Define what students will learn and what they need to know</p>
                    </div>
                    <div class="p-6 space-y-5">

                        <!-- What You Will Learn -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                What You Will Learn
                            </label>
                    <div id="what-you-will-learn-container" class="space-y-3">
                        <?php
                            $whatYouWillLearn = old('what_you_will_learn', $course->what_you_will_learn);
                            $whatYouWillLearn = is_array($whatYouWillLearn) ? $whatYouWillLearn : [];
                        ?>
                        <?php if(count($whatYouWillLearn) > 0): ?>
                            <?php $__currentLoopData = $whatYouWillLearn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $outcome): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="what-you-will-learn-item flex items-center space-x-3">
                                    <input type="text"
                                           name="what_you_will_learn[]"
                                           value="<?php echo e($outcome); ?>"
                                           class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="e.g., Master the fundamentals of digital marketing">
                                    <button type="button"
                                            onclick="removeItem(this)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="what-you-will-learn-item flex items-center space-x-3">
                                <input type="text"
                                       name="what_you_will_learn[]"
                                       class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="e.g., Master the fundamentals of digital marketing">
                                <button type="button"
                                        onclick="removeItem(this)"
                                        class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                            <button type="button"
                                    onclick="addLearningOutcome()"
                                    class="mt-3 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Learning Outcome
                            </button>
                            <p class="text-xs text-gray-500">
                                Key learning outcomes for students
                            </p>
                        </div>

                        <!-- Requirements -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                Requirements
                            </label>
                    <div id="requirements-container" class="space-y-3">
                        <?php
                            $requirements = old('requirements', $course->requirements);
                            $requirements = is_array($requirements) ? $requirements : [];
                        ?>
                        <?php if(count($requirements) > 0): ?>
                            <?php $__currentLoopData = $requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="requirements-item flex items-center space-x-3">
                                    <input type="text"
                                           name="requirements[]"
                                           value="<?php echo e($requirement); ?>"
                                           class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="e.g., Basic computer skills required">
                                    <button type="button"
                                            onclick="removeItem(this)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="requirements-item flex items-center space-x-3">
                                <input type="text"
                                       name="requirements[]"
                                       class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="e.g., Basic computer skills required">
                                <button type="button"
                                        onclick="removeItem(this)"
                                        class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                            <button type="button"
                                    onclick="addRequirement()"
                                    class="mt-3 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Requirement
                            </button>
                            <p class="text-xs text-gray-500">
                                Prerequisites and what students need
                            </p>
                        </div>

                        <!-- Target Audience -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                                Target Audience
                            </label>
                    <div id="target-audience-container" class="space-y-3">
                        <?php
                            $targetAudience = old('target_audience', $course->target_audience);
                            $targetAudience = is_array($targetAudience) ? $targetAudience : [];
                        ?>
                        <?php if(count($targetAudience) > 0): ?>
                            <?php $__currentLoopData = $targetAudience; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $audience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="target-audience-item flex items-center space-x-3">
                                    <input type="text"
                                           name="target_audience[]"
                                           value="<?php echo e($audience); ?>"
                                           class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="e.g., Business owners looking to grow their online presence">
                                    <button type="button"
                                            onclick="removeItem(this)"
                                            class="text-red-600 hover:text-red-800 p-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="target-audience-item flex items-center space-x-3">
                                <input type="text"
                                       name="target_audience[]"
                                       class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="e.g., Business owners looking to grow their online presence">
                                <button type="button"
                                        onclick="removeItem(this)"
                                        class="text-red-600 hover:text-red-800 p-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                        <?php endif; ?>
                    </div>
                            <button type="button"
                                    onclick="addTargetAudience()"
                                    class="mt-3 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Target Audience
                            </button>
                            <p class="text-xs text-gray-500">
                                Who this course is designed for
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Current Images Display -->
                <?php if($course->thumbnail || $course->images): ?>
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Current Images</h3>
                        <p class="text-sm text-gray-600">Manage your existing course images</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <?php if($course->thumbnail): ?>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">Current Thumbnail</label>
                            <div class="relative">
                                <img src="<?php echo e($course->thumbnail_url); ?>" alt="Course thumbnail" class="w-full h-32 object-cover rounded-lg border border-gray-200">
                                <div class="mt-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="remove_thumbnail" value="1" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-600">Remove current thumbnail</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if($course->images && count($course->images) > 0): ?>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">Current Additional Images</label>
                            <div class="grid grid-cols-2 gap-2">
                                <?php $__currentLoopData = $course->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(is_array($image) && isset($image['path'])): ?>
                                <div class="relative">
                                    <img src="<?php echo e(Storage::url($image['path'])); ?>" alt="Course image" class="w-full h-20 object-cover rounded border border-gray-200">
                                    <div class="mt-1">
                                        <label class="flex items-center">
                                            <input type="checkbox" name="remove_images[]" value="<?php echo e($index); ?>" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-1 text-xs text-gray-600">Remove</span>
                                        </label>
                                    </div>
                                </div>
                                <?php elseif(is_string($image)): ?>
                                <div class="relative">
                                    <img src="<?php echo e(Storage::url($image)); ?>" alt="Course image" class="w-full h-20 object-cover rounded border border-gray-200">
                                    <div class="mt-1">
                                        <label class="flex items-center">
                                            <input type="checkbox" name="remove_images[]" value="<?php echo e($index); ?>" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                            <span class="ml-1 text-xs text-gray-600">Remove</span>
                                        </label>
                                    </div>
                                </div>
                                <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Course Images -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Update Course Images</h3>
                        <p class="text-sm text-gray-600">Upload new images for your course</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="space-y-2">
                            <label for="thumbnail" class="block text-sm font-medium text-gray-700">
                                New Course Thumbnail
                            </label>
                            <input type="file"
                                   id="thumbnail"
                                   name="thumbnail"
                                   accept="image/*"
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors <?php $__errorArgs = ['thumbnail'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <p class="mt-1 text-xs text-gray-500">Recommended: 1280x720px, max 2MB. Leave empty to keep current thumbnail.</p>
                            <?php $__errorArgs = ['thumbnail'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="space-y-2">
                            <label for="images" class="block text-sm font-medium text-gray-700">
                                New Additional Images
                            </label>
                            <input type="file"
                                   id="images"
                                   name="images[]"
                                   accept="image/*"
                                   multiple
                                   class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors <?php $__errorArgs = ['images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <p class="mt-1 text-xs text-gray-500">Max 5 images, 2MB each. These will be added to existing images.</p>
                            <?php $__errorArgs = ['images'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Sidebar -->
            <div class="space-y-6 md:col-span-2">
                <!-- Publishing -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <h3 class="text-lg font-semibold text-gray-900">Publishing</h3>
                        <p class="text-sm text-gray-600">Control your course visibility</p>
                    </div>
                    <div class="p-6 space-y-5">
                        <div class="space-y-2">
                            <label for="status" class="block text-sm font-medium text-gray-700">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select id="status"
                                    name="status"
                                    required
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <option value="draft" <?php echo e(old('status', $course->status) === 'draft' ? 'selected' : ''); ?>>Draft</option>
                                <option value="active" <?php echo e(old('status', $course->status) === 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="inactive" <?php echo e(old('status', $course->status) === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            </select>
                            <p class="mt-1 text-xs text-gray-500">Draft courses are not visible to students. Inactive courses are hidden but can be reactivated.</p>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

                <!-- Next Steps Information -->
                <div class="rounded-xl border border-blue-200 bg-blue-50 p-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-blue-800 mb-1">Next Steps</h3>
                            <p class="text-sm text-blue-700">
                                After updating your course details, you can proceed to add or edit course content including chapters, lessons, and materials.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex flex-col sm:flex-row gap-3 pt-6">
                    <a href="<?php echo e(route('seller.courses.index')); ?>"
                       class="inline-flex justify-center items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        Cancel
                    </a>

                    <button type="button"
                            id="save-draft-btn"
                            onclick="saveDraft()"
                            class="inline-flex justify-center items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Save as Draft
                    </button>

                    <button type="submit"
                            class="inline-flex justify-center items-center rounded-lg border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                        </svg>
                        Update Course
                    </button>

                    <a href="<?php echo e(route('seller.courses.show', $course->id)); ?>"
                       onclick="saveBeforeNavigate(event, this.href)"
                       class="inline-flex justify-center items-center rounded-lg border border-transparent bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                        Next Step: Add Content
                    </a>
                </div>
    </form>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Category cascade functionality with existing data
const categories = <?php echo json_encode($categories, 15, 512) ?>;
const categorySelect = document.getElementById('category_id');
const subcategorySelect = document.getElementById('subcategory_id');
const detailedCategorySelect = document.getElementById('detailed_category_id');

// Current course data
const currentCourse = {
    category_id: '<?php echo e($course->category_id); ?>',
    subcategory_id: '<?php echo e($course->subcategory_id); ?>',
    detailed_category_id: '<?php echo e($course->detailed_category_id); ?>'
};

// Initialize subcategories and detailed categories on page load
document.addEventListener('DOMContentLoaded', function() {
    if (currentCourse.category_id) {
        populateSubcategories(currentCourse.category_id, currentCourse.subcategory_id);
    }
});

categorySelect.addEventListener('change', function() {
    const categoryId = this.value;
    populateSubcategories(categoryId);
});

subcategorySelect.addEventListener('change', function() {
    const subcategoryId = this.value;
    populateDetailedCategories(subcategoryId);
});

function populateSubcategories(categoryId, selectedSubcategoryId = null) {
    // Clear subcategory and detailed category
    subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';
    detailedCategorySelect.innerHTML = '<option value="">Select Detailed Category</option>';

    if (categoryId) {
        const category = categories.find(cat => cat.id === categoryId);
        if (category && category.active_subcategories) {
            category.active_subcategories.forEach(subcategory => {
                const option = document.createElement('option');
                option.value = subcategory.id;
                option.textContent = subcategory.name;
                if (selectedSubcategoryId && subcategory.id === selectedSubcategoryId) {
                    option.selected = true;
                }
                subcategorySelect.appendChild(option);
            });

            // If we have a selected subcategory, populate detailed categories
            if (selectedSubcategoryId) {
                populateDetailedCategories(selectedSubcategoryId, currentCourse.detailed_category_id);
            }
        }
    }
}

function populateDetailedCategories(subcategoryId, selectedDetailedCategoryId = null) {
    // Clear detailed category
    detailedCategorySelect.innerHTML = '<option value="">Select Detailed Category</option>';

    if (subcategoryId) {
        const categoryId = categorySelect.value;
        const category = categories.find(cat => cat.id === categoryId);
        if (category && category.active_subcategories) {
            const subcategory = category.active_subcategories.find(sub => sub.id === subcategoryId);
            if (subcategory && subcategory.active_detailed_categories) {
                subcategory.active_detailed_categories.forEach(detailedCategory => {
                    const option = document.createElement('option');
                    option.value = detailedCategory.id;
                    option.textContent = detailedCategory.name;
                    if (selectedDetailedCategoryId && detailedCategory.id === selectedDetailedCategoryId) {
                        option.selected = true;
                    }
                    detailedCategorySelect.appendChild(option);
                });
            }
        }
    }
}

// Enhanced Auto-save functionality for edit form
let isFormDirty = false;
let autoSaveTimer = null;
let lastAutoSave = null;
let isSubmitting = false; // Flag to track form submission
const form = document.getElementById('course-edit-form');
const saveDraftBtn = document.getElementById('save-draft-btn');

// Auto-save configuration
const AUTO_SAVE_INTERVAL = 30000; // 30 seconds
const AUTO_SAVE_DELAY = 2000; // 2 seconds after user stops typing

// Create auto-save indicator
function createAutoSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'auto-save-indicator';
    indicator.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform translate-y-full opacity-0 z-50';
    document.body.appendChild(indicator);
    return indicator;
}

const autoSaveIndicator = createAutoSaveIndicator();

function showAutoSaveStatus(message, type = 'info') {
    const colors = {
        info: 'bg-blue-500 text-white',
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        saving: 'bg-yellow-500 text-white'
    };

    autoSaveIndicator.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 z-50 ${colors[type]}`;
    autoSaveIndicator.textContent = message;
    autoSaveIndicator.style.transform = 'translateY(0)';
    autoSaveIndicator.style.opacity = '1';

    if (type !== 'saving') {
        setTimeout(() => {
            autoSaveIndicator.style.transform = 'translateY(100%)';
            autoSaveIndicator.style.opacity = '0';
        }, 3000);
    }
}

function collectFormData() {
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        if (key !== '_method' && key !== '_token') { // Exclude Laravel method and token fields
            // Handle array fields
            if (key.endsWith('[]')) {
                const arrayKey = key.slice(0, -2);
                if (!data[arrayKey]) {
                    data[arrayKey] = [];
                }
                data[arrayKey].push(value);
            } else {
                data[key] = value;
            }
        }
    }

    return data;
}

async function performAutoSave() {
    if (!isFormDirty || isSubmitting) return;

    try {
        showAutoSaveStatus('Saving...', 'saving');

        const data = collectFormData();

        const response = await fetch('<?php echo e(route('seller.courses.auto-save')); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            lastAutoSave = new Date();
            showAutoSaveStatus('Auto-saved', 'success');
            isFormDirty = false;
        } else {
            showAutoSaveStatus('Auto-save failed', 'error');
        }
    } catch (error) {
        console.error('Auto-save error:', error);
        showAutoSaveStatus('Auto-save failed', 'error');
    }
}

// Track form changes with debounced auto-save
if (form) {
    form.addEventListener('input', function() {
        isFormDirty = true;

        // Clear existing timer
        if (autoSaveTimer) {
            clearTimeout(autoSaveTimer);
        }

        // Set new timer for auto-save
        autoSaveTimer = setTimeout(performAutoSave, AUTO_SAVE_DELAY);
    });
}

// Periodic auto-save
setInterval(() => {
    if (isFormDirty) {
        performAutoSave();
    }
}, AUTO_SAVE_INTERVAL);

// Enhanced save as draft functionality
function saveDraft() {
    // Mark as submitting to prevent beforeunload warning
    isSubmitting = true;

    // Update status to draft and submit
    const statusSelect = document.getElementById('status');
    const originalStatus = statusSelect.value;
    statusSelect.value = 'draft';

    // Submit the form
    form.submit();
}

if (saveDraftBtn) {
    saveDraftBtn.addEventListener('click', function(e) {
        e.preventDefault();
        saveDraft();
    });
}

// Track form submission to prevent beforeunload warning
if (form) {
    form.addEventListener('submit', function(e) {
        // Mark as submitting to prevent beforeunload warning
        isSubmitting = true;

        // Clear any pending auto-save timers
        if (autoSaveTimer) {
            clearTimeout(autoSaveTimer);
        }
    });
}

// Handle "Next Step: Add Content" button
function saveBeforeNavigate(event, href) {
    // If form has unsaved changes, save them first
    if (isFormDirty) {
        event.preventDefault();

        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<span>Saving...</span>';
        button.style.pointerEvents = 'none';

        // Save the form data first
        const formData = new FormData(form);

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success || data.message) {
                // Mark as not dirty and navigate
                isFormDirty = false;
                isSubmitting = true;
                window.location.href = href;
            } else {
                // Restore button and show error
                button.innerHTML = originalText;
                button.style.pointerEvents = 'auto';
                alert('Error saving changes. Please try again.');
            }
        })
        .catch(error => {
            // Restore button and show error
            button.innerHTML = originalText;
            button.style.pointerEvents = 'auto';
            alert('Error saving changes. Please try again.');
        });
    } else {
        // No unsaved changes, allow normal navigation
        isSubmitting = true;
    }
}

const nextStepBtn = document.querySelector('a[href*="courses.show"]');
if (nextStepBtn) {
    nextStepBtn.addEventListener('click', function(e) {
        saveBeforeNavigate(e, this.href);
    });
}

// Auto-save before leaving page
window.addEventListener('beforeunload', function(e) {
    // Only show warning if form is dirty AND not currently submitting
    if (isFormDirty && !isSubmitting) {
        // Try to auto-save (this may not complete due to page unload)
        performAutoSave();

        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        return 'You have unsaved changes. Are you sure you want to leave?';
    }
});

// Dynamic field management functions
window.addLearningOutcome = function() {
    const container = document.getElementById('what-you-will-learn-container');
    const newItem = document.createElement('div');
    newItem.className = 'what-you-will-learn-item flex items-center space-x-3';
    newItem.innerHTML = `
        <input type="text"
               name="what_you_will_learn[]"
               class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
               placeholder="e.g., Master the fundamentals of digital marketing">
        <button type="button"
                onclick="removeItem(this)"
                class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(newItem);
    isFormDirty = true;
};

window.addRequirement = function() {
    const container = document.getElementById('requirements-container');
    const newItem = document.createElement('div');
    newItem.className = 'requirements-item flex items-center space-x-3';
    newItem.innerHTML = `
        <input type="text"
               name="requirements[]"
               class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
               placeholder="e.g., Basic computer skills required">
        <button type="button"
                onclick="removeItem(this)"
                class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(newItem);
    isFormDirty = true;
};

window.addTargetAudience = function() {
    const container = document.getElementById('target-audience-container');
    const newItem = document.createElement('div');
    newItem.className = 'target-audience-item flex items-center space-x-3';
    newItem.innerHTML = `
        <input type="text"
               name="target_audience[]"
               class="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
               placeholder="e.g., Business owners looking to grow their online presence">
        <button type="button"
                onclick="removeItem(this)"
                class="text-red-600 hover:text-red-800 p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
        </button>
    `;
    container.appendChild(newItem);
    isFormDirty = true;
};

window.removeItem = function(button) {
    const item = button.closest('.what-you-will-learn-item, .requirements-item, .target-audience-item');
    const container = item.parentNode;

    // Don't remove if it's the last item
    if (container.children.length > 1) {
        item.remove();
        isFormDirty = true;
    }
};
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('seller.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/seller/courses/edit.blade.php ENDPATH**/ ?>