<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class HelpArticle extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = ['category_id', 'title', 'slug', 'content', 'helpful_yes', 'helpful_no'];

    public function category()
    {
        return $this->belongsTo(HelpCategory::class);
    }

    public function relatedArticles()
    {
        return $this->belongsToMany(HelpArticle::class, 'help_article_related', 'article_id', 'related_article_id');
    }
}
