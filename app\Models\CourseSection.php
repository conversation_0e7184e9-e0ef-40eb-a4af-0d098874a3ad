<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CourseSection extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'course_id',
        'title',
        'description',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the course that owns the section
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get all curriculum items for the section
     */
    public function curriculumItems()
    {
        return $this->hasMany(CourseCurriculumItem::class, 'section_id')->orderBy('sort_order');
    }

    /**
     * Get all active curriculum items for the section
     */
    public function activeCurriculumItems()
    {
        return $this->hasMany(CourseCurriculumItem::class, 'section_id')->where('is_active', true)->orderBy('sort_order');
    }

    /**
     * Backward compatibility: alias for curriculumItems
     */
    public function materials()
    {
        return $this->curriculumItems();
    }

    /**
     * Scope to get only active sections
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get sections ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get the total duration of the section in minutes
     */
    public function getTotalDurationAttribute()
    {
        return $this->curriculumItems()->sum('estimated_duration') ?? 0;
    }

    /**
     * Get the total number of curriculum items in the section
     */
    public function getTotalItemsAttribute()
    {
        return $this->curriculumItems()->count();
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($section) {
            if (!$section->sort_order) {
                $maxOrder = self::where('course_id', $section->course_id)->max('sort_order') ?? 0;
                $section->sort_order = $maxOrder + 1;
            }
        });

        static::deleting(function ($section) {
            // Delete all curriculum items in this section
            $section->curriculumItems()->delete();
        });
    }
}
