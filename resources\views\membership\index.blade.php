@extends('layouts.user-dashboard')

@section('title', 'Digitora Membership Tiers')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Digitora Membership Tiers</h1>
            <p class="text-xl text-gray-600">Choose the perfect plan for your digital marketplace journey</p>
        </div>

        @if($currentTier)
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mb-8 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-blue-900">Current Membership</h3>
                    <p class="text-blue-700 font-medium">{{ $currentTier->name }}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-blue-600">AI Usage Today</p>
                    <p class="text-lg font-bold text-blue-900">
                        {{ $todayUsage }} /
                        @if($currentTier->slug === 'starter')
                            Limited
                        @else
                            {{ $currentTier->daily_ai_prompts }}
                        @endif
                    </p>
                    @if($remainingPrompts !== -1)
                    <p class="text-sm text-blue-600">
                        @if($currentTier->slug === 'starter')
                             prompts remaining (throttled)
                        @else
                            {{ $remainingPrompts }} prompts remaining
                        @endif
                    </p>
                    @endif
                </div>
            </div>
        </div>
        @endif

        <!-- Comparison Table -->
        <div class="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="bg-gray-50 border-b border-gray-200">
                            <th class="px-6 py-4 text-left">
                                <span class="text-sm font-medium text-gray-900">Features</span>
                            </th>
                            @foreach($tiers as $tier)
                            <th class="px-6 py-4 text-center {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                <div class="text-lg font-bold text-gray-900">{{ explode(' (', $tier->name)[0] }}</div>
                                <div class="text-sm text-gray-600">
                                    @if($tier->price == 0)
                                        Free
                                    @else
                                        Rp {{ number_format($tier->price, 0, ',', '.') }}
                                        <div class="text-xs">per month</div>
                                    @endif
                                </div>
                            </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <!-- Daily AI Prompts -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Daily AI Prompts</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                <span class="font-medium {{ $tier->slug === 'starter' ? 'text-orange-600' : 'text-blue-600' }}">
                                    @if($tier->slug === 'starter')
                                        Limited (throttled)
                                    @else
                                        {{ $tier->daily_ai_prompts . ' Messages' }}
                                    @endif
                                </span>
                            </td>
                            @endforeach
                        </tr>

                        <!-- Product Chatbots (moved to second row for visibility) -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Product Chatbots</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                @if($tier->chatbot_products_limit === -1)
                                <span class="font-medium text-purple-600">Unlimited</span>
                                @elseif($tier->chatbot_products_limit > 0)
                                <span class="font-medium text-blue-600">Up to {{ $tier->chatbot_products_limit }} Products</span>
                                @else
                                <span class="text-red-600 font-medium">✗ Not Available</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>

                        <!-- Sell Products -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Sell Products</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                <span class="text-green-600 font-medium">✓ Unlimited</span>
                            </td>
                            @endforeach
                        </tr>

                        <!-- Digital Products Transaction Fee -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Digital Products Transaction Fee</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                @if($tier->digital_product_transaction_fee < 5)
                                <div class="flex items-center justify-center space-x-2">
                                    <span class="text-gray-400 line-through text-xs">5%</span>
                                    <span class="font-medium text-green-600">{{ $tier->digital_product_transaction_fee }}%</span>
                                </div>
                                @else
                                <span class="font-medium text-gray-900">{{ $tier->digital_product_transaction_fee }}%</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>

                        <!-- Online Courses Transaction Fee -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Online Courses Transaction Fee</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                @if($tier->course_transaction_fee < 10)
                                <div class="flex items-center justify-center space-x-2">
                                    <span class="text-gray-400 line-through text-xs">10%</span>
                                    <span class="font-medium text-green-600">{{ $tier->course_transaction_fee }}%</span>
                                </div>
                                @else
                                <span class="font-medium text-gray-900">{{ $tier->course_transaction_fee }}%</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>

                        <!-- Basic DigiAI Access -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Basic DigiAI Access</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                <span class="text-green-600 font-medium">✓ Included</span>
                            </td>
                            @endforeach
                        </tr>

                        <!-- AI Tools -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">AI Tools (Description, SEO)</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                @if($tier->ai_tools_access)
                                <span class="text-green-600 font-medium">✓ Included</span>
                                @else
                                <span class="text-red-600 font-medium">✗ Not Available</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>



                        <!-- Sales Trend Analytics -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Sales Trend Analytics</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                <span class="text-green-600 font-medium">✓ Included</span>
                            </td>
                            @endforeach
                        </tr>

                        <!-- Priority Support -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Priority Support</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                @if($tier->priority_support)
                                <span class="text-green-600 font-medium">✓ Included</span>
                                @else
                                <span class="text-gray-600 font-medium">Standard Support</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>

                        <!-- Listing Customization -->
                        <tr>
                            <td class="px-6 py-3 text-sm font-medium text-gray-900">Listing Customization</td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-3 text-center text-sm {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                @if($tier->custom_listing)
                                <span class="text-purple-600 font-medium">✓ Video/Images</span>
                                @else
                                <span class="text-red-600 font-medium">✗ Not Available</span>
                                @endif
                            </td>
                            @endforeach
                        </tr>

                        <!-- Action Buttons Row -->
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900"></td>
                            @foreach($tiers as $tier)
                            <td class="px-6 py-4 text-center {{ $currentTier && $currentTier->id === $tier->id ? 'bg-blue-50' : '' }}">
                                @if($currentTier && $currentTier->id === $tier->id)
                                <button class="w-full bg-gray-400 text-white py-2 px-4 rounded font-medium cursor-not-allowed" disabled>
                                    Current Plan
                                </button>
                                @else
                                <button onclick="upgradeMembership('{{ $tier->slug }}')"
                                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition duration-200 font-medium">
                                    @if($tier->price == 0)
                                        Select Free Plan
                                    @else
                                        Upgrade to {{ explode(' (', $tier->name)[0] }}
                                    @endif
                                </button>
                                @endif
                            </td>
                            @endforeach
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function upgradeMembership(tierSlug) {
    if (!confirm('Are you sure you want to upgrade to this membership tier?')) {
        return;
    }

    fetch('{{ route("membership.upgrade") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            tier_slug: tierSlug
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Membership upgraded successfully!');
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while upgrading membership.');
    });
}
</script>
@endsection
