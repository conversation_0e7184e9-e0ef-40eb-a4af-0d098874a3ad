<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use App\Models\User; // Explicitly import the User model
use App\Models\SellerApplication; // Import SellerApplication model

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Please log in to access the settings page.');
        }

        // Get the seller application
        $sellerApplication = $user->sellerApplication;

        return view('seller.settings.index', compact('user', 'sellerApplication'));
    }

    /**
     * Update the seller's profile information.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Please log in to update your profile.');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            // Email is validated but not updated since it's a unique identifier
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20', 'regex:/^[+]?[0-9\s-]+$/'],
            'bio' => ['nullable', 'string', 'max:1000'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ], [
            'phone.regex' => 'The phone number format is invalid. Use numbers, spaces, or + only.',
            'avatar.max' => 'The profile picture must not be larger than 2MB.',
        ]);

        // Update user data directly since fill() might not be available
        $userData = [
            'name' => $validated['name'],
            'phone' => $validated['phone'] ?? $user->phone,
            'bio' => $validated['bio'] ?? $user->bio,
        ];

        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists and is not the default
            if ($user->avatar && !str_contains($user->avatar, 'default-avatar')) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store avatar in a user-specific folder
            $userId = $user->id;
            $path = $request->file('avatar')->store("avatars/{$userId}", 'public');
            $userData['avatar'] = $path;
        }

        // Update the user using Eloquent query
        User::where('id', $user->id)->update($userData);

        return redirect()->route('seller.settings')->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the seller's store information.
     */
    public function updateStore(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Please log in to update your store information.');
        }

        // Get the seller application
        $sellerApplication = $user->sellerApplication;
        if (!$sellerApplication) {
            return redirect()->route('seller.settings')->with('error', 'Seller application not found.');
        }

        // Create a validator instance
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'store_name' => ['required', 'string', 'max:255'],
            'store_description' => ['nullable', 'string', 'max:1000'],
            'store_logo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ], [
            'store_logo.max' => 'The store logo must not be larger than 2MB.',
        ]);

        // Add custom validation to check if the store name slug is unique
        $validator->after(function ($validator) use ($request, $sellerApplication) {
            // Only check if the store name has changed
            if ($sellerApplication->store_name !== $request->store_name) {
                $storeNameSlug = Str::slug($request->store_name);

                // Check if the slug already exists in approved applications (excluding current application)
                $existingApplication = SellerApplication::where('store_name_slug', $storeNameSlug)
                    ->where('id', '!=', $sellerApplication->id)
                    ->where('status', 'approved')
                    ->exists();

                if ($existingApplication) {
                    $validator->errors()->add(
                        'store_name',
                        'This store name is already taken. Please choose a different name.'
                    );
                }
            }
        });

        // Run the validation
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $validated = $validator->validated();

        // Check if store name has changed
        $storeNameChanged = $sellerApplication->store_name !== $validated['store_name'];

        // Update seller application data
        $sellerApplication->store_name = $validated['store_name'];
        $sellerApplication->store_description = $validated['store_description'] ?? $sellerApplication->store_description;

        // If store name changed, update the slug and check for uniqueness
        if ($storeNameChanged) {
            // Generate a new slug based on the store name
            $baseSlug = Str::slug($validated['store_name']);
            $slug = $baseSlug;
            $counter = 1;

            // Check if the slug already exists in the database (excluding the current application)
            while (SellerApplication::where('store_name_slug', $slug)
                ->where('id', '!=', $sellerApplication->id)
                ->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            // Update the store name slug
            $sellerApplication->store_name_slug = $slug;

            // Log the slug change
            Log::info("Store slug updated for user ID {$user->id} from {$sellerApplication->getOriginal('store_name_slug')} to {$slug}");
        }

        if ($request->hasFile('store_logo')) {
            // Delete old logo if exists and is not the default
            if ($sellerApplication->store_logo) {
                if (str_starts_with($sellerApplication->store_logo, 'private/')) {
                    // Delete from local disk if it's a private file
                    Storage::disk('local')->delete($sellerApplication->store_logo);
                } else if (!str_contains($sellerApplication->store_logo, 'default-logo')) {
                    // Delete from public disk if it's a public file
                    Storage::disk('public')->delete($sellerApplication->store_logo);
                }
            }

            // Store logo in a seller-specific folder in public storage
            $userId = $user->id;
            $path = $request->file('store_logo')->store("store_logos/{$userId}", 'public');
            $sellerApplication->store_logo = $path;
        }

        // Save the seller application
        $sellerApplication->save();

        return redirect()->route('seller.settings')->with('success', 'Store information updated successfully!');
    }

    /**
     * Update the seller's password.
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Please log in to update your password.');
        }

        // Rate limit password updates to prevent abuse (e.g., 3 attempts per hour)
        $throttleKey = 'password-update:' . $user->id;
        if (RateLimiter::tooManyAttempts($throttleKey, 3)) {
            $seconds = RateLimiter::availableIn($throttleKey);
            return back()->withErrors(['password' => "Too many attempts. Please try again in {$seconds} seconds."]);
        }

        $validated = $request->validate([
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        // Update the password using Eloquent query
        User::where('id', $user->id)->update([
            'password' => Hash::make($validated['password']),
        ]);

        RateLimiter::clear($throttleKey); // Clear throttle on success

        return redirect()->route('seller.settings')->with('success', 'Password updated successfully!');
    }

    /**
     * Update notification preferences.
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return redirect()->route('login')->with('error', 'Please log in to update your notification preferences.');
        }

        // No need to validate as we're using the presence of the checkbox in the request
        // to determine the boolean value (checked = true, unchecked = false)

        // Update user data directly
        $userData = [
            'notification_order' => $request->has('notification_order') ? 1 : 0,
            'notification_payment' => $request->has('notification_payment') ? 1 : 0,
            'notification_product' => $request->has('notification_product') ? 1 : 0,
            'notification_marketing' => $request->has('notification_marketing') ? 1 : 0,
        ];

        // Update the user using Eloquent query
        User::where('id', $user->id)->update($userData);

        return redirect()->route('seller.settings')->with('success', 'Notification preferences updated successfully!');
    }
}
