<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MembershipTier;

class MembershipTierSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Delete existing tiers first
        MembershipTier::query()->delete();

        $tiers = [
            [
                'name' => 'Starter (Free)',
                'slug' => 'starter',
                'price' => 0,
                'daily_ai_prompts' => 5,
                'unlimited_products' => true, // All tiers have unlimited products
                'ai_tools_access' => false,
                'chatbot_products_limit' => 0,
                'sales_analytics' => true, // All tiers have sales analytics
                'priority_support' => false,
                'custom_listing' => false,
                'digital_product_transaction_fee' => 5, // 5% for digital products
                'course_transaction_fee' => 10, // 10% for courses
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Basic (Rp 79K/Month)',
                'slug' => 'basic',
                'price' => 79000,
                'daily_ai_prompts' => 100,
                'unlimited_products' => true, // All tiers have unlimited products
                'ai_tools_access' => true,
                'chatbot_products_limit' => 5,
                'sales_analytics' => true, // All tiers have sales analytics
                'priority_support' => true,
                'custom_listing' => false,
                'digital_product_transaction_fee' => 3, // 3% for digital products (discounted from 5%)
                'course_transaction_fee' => 8, // 8% for courses (discounted from 10%)
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Pro (Rp 99K/Month)',
                'slug' => 'pro',
                'price' => 99000,
                'daily_ai_prompts' => 200, // 200 prompts per day (changed from unlimited)
                'unlimited_products' => true, // All tiers have unlimited products
                'ai_tools_access' => true,
                'chatbot_products_limit' => -1, // Unlimited
                'sales_analytics' => true, // All tiers have sales analytics
                'priority_support' => true,
                'custom_listing' => true,
                'digital_product_transaction_fee' => 3, // 3% for digital products (discounted from 5%)
                'course_transaction_fee' => 8, // 8% for courses (discounted from 10%)
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($tiers as $tier) {
            MembershipTier::create($tier);
        }
    }
}
