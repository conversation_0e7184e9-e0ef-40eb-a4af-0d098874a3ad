# Course System Separation Documentation

## Overview

This document outlines the clear separation between the legacy product-based course system and the new independent course system in Digitora.

## System Architecture

### Legacy Product-Based Course System

**Models:**
- `Product` (with `content_type = 'course'`)
- `ProductResource` (for course materials)
- `ProductImage` (for course images)

**Key Features:**
- Simple course structure
- File-based materials
- Basic resource management
- Integrated with existing product system

**Routes:**
- `/seller/products/create?type=course&content_type=course`
- `/course/{product:id}/access` (legacy route)

### New Independent Course System

**Models:**
- `Course` (independent course entity)
- `CourseChapter` (course chapters)
- `CourseSubchapter` (sub-chapters within chapters)
- `CourseMaterial` (individual learning materials)

**Key Features:**
- Hierarchical course structure (Course → Chapter → Sub-chapter → Materials)
- Multiple content types (video links, text content, file attachments)
- Advanced course management
- Udemy-style learning experience
- Progress tracking capabilities
- Preview materials

**Routes:**
- `/seller/courses/*` (seller management)
- `/courses/*` (public course viewing)
- `/api/course/*` (course content API)

## Database Structure

### Legacy System Tables
```
products
├── content_type = 'course'
├── product_type = 'course'
└── files (JSON field)

product_resources
├── product_id
├── type (video, pdf, text)
└── content
```

### New System Tables
```
courses
├── seller_id
├── title, description
├── category relationships
├── pricing information
└── course metadata

course_chapters
├── course_id
├── title, description
└── sort_order

course_subchapters
├── course_id
├── chapter_id
├── title, description
└── estimated_duration

course_materials
├── course_id
├── chapter_id
├── subchapter_id
├── type (text, video, file)
├── content
└── is_preview
```

## Key Differences

### Content Organization

**Legacy System:**
- Flat structure with resources
- Limited content types
- Basic organization

**New System:**
- Hierarchical structure (3 levels)
- Rich content types
- Advanced organization with chapters and sub-chapters

### User Experience

**Legacy System:**
- Simple resource listing
- Basic download functionality
- Limited learning experience

**New System:**
- Udemy-style course player
- Progressive learning path
- Interactive course navigation
- Preview capabilities

### Management Interface

**Legacy System:**
- Basic product creation form
- Simple resource management
- Limited course structure

**New System:**
- Dedicated course creation workflow
- Advanced chapter/material management
- Real-time course structure editing
- Drag-and-drop organization

## Migration Strategy

### Current State
- Both systems coexist
- Legacy courses remain functional
- New courses use independent system

### Future Considerations
- Legacy system maintained for backward compatibility
- New courses should use the independent system
- Consider migration tools for legacy courses

## Purchase Integration

### Current Implementation
- Course viewing system is implemented
- Purchase integration is placeholder-based
- TODO: Implement course-specific order tracking

### Required Updates
1. Extend Order model to support Course purchases
2. Update cart system for courses
3. Implement course-specific payment flow
4. Add course access validation

## API Endpoints

### Course Viewing
- `GET /courses` - List all courses
- `GET /courses/{slug}` - Course details
- `GET /courses/{slug}/access` - Course player (authenticated)

### Course Content API
- `GET /api/course/{course}/subchapter/{subchapter}` - Get subchapter content

### Seller Management
- `GET /seller/courses` - Seller course list
- `POST /seller/courses` - Create new course
- `GET /seller/courses/{course}` - Course management
- Various chapter/subchapter/material management endpoints

## File Organization

### Views
```
resources/views/
├── courses/                    # New system views
│   ├── index.blade.php        # Course listing
│   ├── show.blade.php         # Course details
│   └── access.blade.php       # Course player
├── seller/courses/            # Seller management
│   ├── index.blade.php
│   ├── create.blade.php
│   └── show.blade.php
└── users/course-access-old.blade.php  # Legacy system
```

### Controllers
```
app/Http/Controllers/
├── CourseViewController.php    # New public course viewing
├── Seller/
│   ├── CourseController.php   # New course management
│   ├── CourseChapterController.php
│   ├── CourseSubchapterController.php
│   └── CourseMaterialController.php
└── UserController.php         # Legacy course access methods
```

## Testing

### Test Coverage
- `NewCourseSystemTest.php` - Tests new course system
- `CourseCreationSystemTest-old.php` - Tests legacy system
- System separation validation

### Key Test Cases
- Course creation in new system
- Chapter/subchapter management
- Material handling
- System independence verification

## Best Practices

### For Developers
1. Use new Course system for all new course functionality
2. Maintain legacy system for existing courses
3. Clearly separate routes and controllers
4. Use appropriate models for each system

### For Content Creators
1. Use `/seller/courses/create` for new courses
2. Leverage hierarchical structure for better organization
3. Utilize preview materials for marketing
4. Take advantage of rich content types

## Future Enhancements

### Planned Features
1. Course progress tracking
2. Student analytics
3. Course completion certificates
4. Advanced video player integration
5. Course reviews and ratings
6. Bulk course operations

### Technical Improvements
1. Course caching for better performance
2. CDN integration for course materials
3. Advanced search and filtering
4. Course recommendation engine
