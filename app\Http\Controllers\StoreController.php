<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\SellerApplication;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;


class StoreController extends Controller
{
    /**
     * Display the seller's store front.
     *
     * @param  string  $storeNameSlug
     * @return \Illuminate\View\View
     */
    public function show($storeNameSlug)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with(['sellerApplication', 'currentMembership.membershipTier'])
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Get seller's current membership tier
        $sellerMembershipTier = $seller->getCurrentMembershipTier();

        // Get products with active chatbots for this seller (AI-Powered Products section)
        $aiPoweredProducts = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereHas('chatbotData', function ($query) {
                $query->where('is_active', true);
            })
            ->with(['chatbotData', 'productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->take(6)
            ->get();

        // Get featured products for this seller
        $featuredProducts = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->where('is_featured', true)
            ->with(['chatbotData', 'productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->take(4)
            ->get();

        $products = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['chatbotData', 'productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->paginate(10);

        // Get courses for this seller
        $courses = \App\Models\Course::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['category', 'subcategory', 'detailedCategory', 'sections.curriculumItems'])
            ->paginate(10);

        // Get featured courses for this seller
        $featuredCourses = \App\Models\Course::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->where('is_featured', true)
            ->with(['category', 'subcategory', 'detailedCategory', 'sections.curriculumItems'])
            ->take(4)
            ->get();

        // Get product categories for filtering
        // First try to get categories from the new structure
        $categoriesQuery = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id');

        $hasNewCategories = $categoriesQuery->exists();

        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $categoryName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($categoryName && $product->subcategory_id) {
                    $subCat = ProductSubcategory::find($product->subcategory_id);
                    if ($subCat) {
                        // Use slug as the key for better SEO-friendly URLs
                        $subCatSlug = $subCat->slug;
                        if (!isset($categories[$subCatSlug])) {
                            $categories[$subCatSlug] = $categoryName;
                            $categoryProductCounts[$subCatSlug] = 1;
                        } else {
                            $categoryProductCounts[$subCatSlug]++;
                        }
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $category) {
                $categoryProductCounts[$category] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $category)
                    ->count();
            }
        }



        return view('store.show', compact('seller', 'products', 'courses', 'featuredProducts', 'featuredCourses', 'aiPoweredProducts', 'categories', 'categoryProductCounts', 'hasNewCategories', 'sellerMembershipTier'));
    }

    /**
     * Display all of the seller's products with filtering options.
     *
     * @param  string  $storeNameSlug
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function allProducts($storeNameSlug, Request $request)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Get seller's current membership tier
        $sellerMembershipTier = $seller->getCurrentMembershipTier();

        // Get courses for store layout statistics
        $courses = \App\Models\Course::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['category', 'subcategory', 'detailedCategory', 'sections.curriculumItems'])
            ->paginate(10);

        // Start building the query
        $query = Product::where('seller_id', $seller->id)
            ->where('status', 'active');

        // Apply price range filter
        $priceRange = $request->input('price_range');
        if ($priceRange && $priceRange != 'all') {
            if (strpos($priceRange, '-') !== false) {
                list($min, $max) = explode('-', $priceRange);
                $query->whereBetween('price', [$min, $max]);
            } elseif (strpos($priceRange, '+') !== false) {
                $min = str_replace('+', '', $priceRange);
                $query->where('price', '>=', $min);
            }
        }

        // Apply rating filter
        $rating = $request->input('rating');
        if ($rating && $rating != 'all') {
            $query->where('average_rating', '>=', $rating);
        }

        // Apply sorting
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Apply pagination - always use 10 items per page
        $products = $query->paginate(10)->withQueryString();

        // Get all categories for the sidebar
        // First try to get categories from the new structure
        $categoriesQuery = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id');

        $hasNewCategories = $categoriesQuery->exists();

        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $categoryName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($categoryName) {
                    if (!isset($categories[$product->subcategory_id])) {
                        $categories[$product->subcategory_id] = $categoryName;
                        $categoryProductCounts[$product->subcategory_id] = 1;
                    } else {
                        $categoryProductCounts[$product->subcategory_id]++;
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $cat) {
                $categoryProductCounts[$cat] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $cat)
                    ->count();
            }
        }

        // Set category to 'all' for the view
        $category = 'all';

        return view('store.category', compact('seller', 'products', 'categories', 'category', 'categoryProductCounts', 'hasNewCategories', 'sellerMembershipTier', 'courses'));
    }

    /**
     * Display the seller's products filtered by category.
     *
     * @param  string  $storeNameSlug
     * @param  string  $category
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function category($storeNameSlug, $categorySlug, Request $request)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Get seller's current membership tier
        $sellerMembershipTier = $seller->getCurrentMembershipTier();

        // Get courses for store layout statistics
        $courses = \App\Models\Course::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['category', 'subcategory', 'detailedCategory', 'sections.curriculumItems'])
            ->paginate(10);

        // Check if we're using the new category structure
        $hasNewCategories = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id')
            ->exists();

        // Start building the query
        $query = Product::where('seller_id', $seller->id)
            ->where('status', 'active');

        if ($hasNewCategories) {
            // Always try to find by subcategory slug first (preferred method)
            $subcategory = ProductSubcategory::where('slug', $categorySlug)->first();

            if ($subcategory) {
                $query->where('subcategory_id', $subcategory->id);
                $categoryName = $subcategory->name;
                $categoryId = $subcategory->id;
            } else {
                // Try to find by category slug
                $category = ProductCategory::where('slug', $categorySlug)->first();

                if ($category) {
                    $query->where('category_id', $category->id);
                    $categoryName = $category->name;
                    $categoryId = null;
                } else {
                    // Try to find by detailed category slug
                    $detailedCategory = ProductDetailedCategory::where('slug', $categorySlug)->first();

                    if ($detailedCategory) {
                        $query->where('detailed_category_id', $detailedCategory->id);
                        $categoryName = $detailedCategory->name;
                        $categoryId = null;
                    } else {
                        // Fallback to legacy category or UUID (for backward compatibility)
                        if (is_string($categorySlug) && preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $categorySlug)) {
                            // It's a UUID, so check if it's a subcategory ID
                            $subcategory = ProductSubcategory::find($categorySlug);
                            if ($subcategory) {
                                $query->where('subcategory_id', $categorySlug);
                                $categoryName = $subcategory->name;
                                $categoryId = $subcategory->id;
                            } else {
                                // Check if it's a detailed category ID
                                $detailedCategory = ProductDetailedCategory::find($categorySlug);
                                if ($detailedCategory) {
                                    $query->where('detailed_category_id', $categorySlug);
                                    $categoryName = $detailedCategory->name;
                                    $categoryId = null;
                                } else {
                                    // Fallback to legacy category
                                    $query->where('category', $categorySlug);
                                    $categoryName = ucfirst($categorySlug);
                                    $categoryId = null;
                                }
                            }
                        } else {
                            // Fallback to legacy category
                            $query->where('category', $categorySlug);
                            $categoryName = ucfirst($categorySlug);
                            $categoryId = null;
                        }
                    }
                }
            }
        } else {
            // Use legacy category field
            $query->where('category', $categorySlug);
            $categoryName = ucfirst($categorySlug);
            $categoryId = null;
        }

        // Apply price range filter
        $priceRange = $request->input('price_range');
        if ($priceRange && $priceRange != 'all') {
            if (strpos($priceRange, '-') !== false) {
                list($min, $max) = explode('-', $priceRange);
                $query->whereBetween('price', [$min, $max]);
            } elseif (strpos($priceRange, '+') !== false) {
                $min = str_replace('+', '', $priceRange);
                $query->where('price', '>=', $min);
            }
        }

        // Apply rating filter
        $rating = $request->input('rating');
        if ($rating && $rating != 'all') {
            $query->where('average_rating', '>=', $rating);
        }

        // Apply sorting
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Apply pagination - always use 10 items per page
        $products = $query->paginate(10)->withQueryString();

        // Get all categories for the sidebar
        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $subCatName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($subCatName && $product->subcategory_id) {
                    $subCat = ProductSubcategory::find($product->subcategory_id);
                    if ($subCat) {
                        // Use slug as the key for better SEO-friendly URLs
                        $subCatSlug = $subCat->slug;
                        if (!isset($categories[$subCatSlug])) {
                            $categories[$subCatSlug] = $subCatName;
                            $categoryProductCounts[$subCatSlug] = 1;
                        } else {
                            $categoryProductCounts[$subCatSlug]++;
                        }
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $cat) {
                $categoryProductCounts[$cat] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $cat)
                    ->count();
            }
        }

        // Use the actual category name for display
        $category = $categoryName;

        // Pass the category slug for links
        $categorySlugForLinks = $categorySlug;

        return view('store.category', compact('seller', 'products', 'categories', 'category', 'categoryProductCounts', 'hasNewCategories', 'categorySlugForLinks', 'sellerMembershipTier', 'courses'));
    }

    /**
     * Display a specific product in the store.
     *
     * @param  string  $storeNameSlug
     * @param  string  $productSlug
     * @return \Illuminate\View\View
     */
    public function product($storeNameSlug, $slug)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Get seller's current membership tier
        $sellerMembershipTier = $seller->getCurrentMembershipTier();

        // Get products and courses for store layout statistics
        $products = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['chatbotData', 'productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->paginate(10);

        $courses = \App\Models\Course::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['category', 'subcategory', 'detailedCategory', 'sections.curriculumItems'])
            ->paginate(10);

        // Get the specific product with its category relationships, chatbot data, and resources
        $product = Product::where('seller_id', $seller->id)
            ->where('slug', $slug)
            ->where('status', 'active')
            ->with(['productCategory', 'productSubcategory', 'productDetailedCategory', 'chatbotData', 'activeResources'])
            ->firstOrFail();

        // Check if the user has already purchased this product
        $alreadyPurchased = false;
        if (auth()->check()) {
            $alreadyPurchased = Order::where('buyer_id', auth()->id())
                ->where('product_id', $product->id)
                ->where('status', 'success')
                ->exists();
        }

        // Check if we're using the new category structure
        $hasNewCategories = $product->category_id || $product->subcategory_id || $product->detailed_category_id;

        // Get related products
        $relatedQuery = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->where('id', '!=', $product->id);

        if ($hasNewCategories) {
            // Try to find related products by subcategory first
            if ($product->subcategory_id) {
                $relatedQuery->where('subcategory_id', $product->subcategory_id);
            } elseif ($product->category_id) {
                // Fallback to category
                $relatedQuery->where('category_id', $product->category_id);
            } else {
                // Fallback to legacy category
                $relatedQuery->where('category', $product->category);
            }
        } else {
            // Use legacy category field
            $relatedQuery->where('category', $product->category);
        }

        $relatedProducts = $relatedQuery->with('chatbotData')->take(4)->get();

        // Check if the user has already purchased any of the related products
        $purchasedProductIds = [];
        if (auth()->check()) {
            $purchasedProductIds = Order::where('buyer_id', auth()->id())
                ->where('status', 'success')
                ->pluck('product_id')
                ->toArray();
        }

        // Get category name for display
        if ($hasNewCategories) {
            if ($product->productDetailedCategory) {
                $categoryName = $product->productDetailedCategory->name;
            } elseif ($product->productSubcategory) {
                $categoryName = $product->productSubcategory->name;
            } elseif ($product->productCategory) {
                $categoryName = $product->productCategory->name;
            } else {
                $categoryName = ucfirst($product->category);
            }
        } else {
            $categoryName = ucfirst($product->category);
        }

        return view('store.product', compact('seller', 'product', 'relatedProducts', 'categoryName', 'hasNewCategories', 'alreadyPurchased', 'purchasedProductIds', 'sellerMembershipTier', 'products', 'courses'));
    }



    /**
     * Search for products in the store.
     *
     * @param  string  $storeNameSlug
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function search($storeNameSlug, Request $request)
    {
        // Find the seller by store name using the sellerApplication relationship
        $seller = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function ($query) use ($storeNameSlug) {
                $query->where('store_name_slug', $storeNameSlug)
                    ->where('status', 'approved');
            })
            ->with('sellerApplication')
            ->firstOrFail();

        // Add a computed store_slug property to the seller
        $seller->store_slug = $storeNameSlug;

        // Get seller's current membership tier
        $sellerMembershipTier = $seller->getCurrentMembershipTier();

        // Get courses for store layout statistics
        $courses = \App\Models\Course::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->with(['category', 'subcategory', 'detailedCategory', 'sections.curriculumItems'])
            ->paginate(10);

        $query = $request->input('query');

        // Search for products
        $products = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%")
                    ->orWhere('category', 'like', "%{$query}%");
            })
            ->paginate(10);

        // Get all categories for the sidebar
        // Check if we're using the new category structure
        $hasNewCategories = Product::where('seller_id', $seller->id)
            ->where('status', 'active')
            ->whereNotNull('category_id')
            ->exists();

        if ($hasNewCategories) {
            // Use the new category structure
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->whereNotNull('category_id')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Group by category and get counts
            $categories = [];
            $categoryProductCounts = [];

            foreach ($categoryData as $product) {
                // Use subcategory name if available, otherwise fallback to category
                $categoryName = $product->productSubcategory ?
                    $product->productSubcategory->name : ($product->productCategory ? $product->productCategory->name : null);

                if ($categoryName && $product->subcategory_id) {
                    $subCat = ProductSubcategory::find($product->subcategory_id);
                    if ($subCat) {
                        // Use slug as the key for better SEO-friendly URLs
                        $subCatSlug = $subCat->slug;
                        if (!isset($categories[$subCatSlug])) {
                            $categories[$subCatSlug] = $categoryName;
                            $categoryProductCounts[$subCatSlug] = 1;
                        } else {
                            $categoryProductCounts[$subCatSlug]++;
                        }
                    }
                }
            }
        } else {
            // Fallback to legacy category field
            $categories = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->select('category')
                ->distinct()
                ->get()
                ->pluck('category');

            // Get product count per category
            $categoryProductCounts = [];
            foreach ($categories as $category) {
                $categoryProductCounts[$category] = Product::where('seller_id', $seller->id)
                    ->where('status', 'active')
                    ->where('category', $category)
                    ->count();
            }
        }

        return view('store.search', compact('seller', 'products', 'categories', 'categoryProductCounts', 'query', 'hasNewCategories', 'sellerMembershipTier', 'courses'));
    }

    /**
     * Serve the store logo from storage.
     *
     * @param  string  $storeSlug
     * @return \Illuminate\Http\Response
     */
    public function getStoreLogo($storeSlug)
    {
        // Find the seller application by store slug
        $sellerApplication = SellerApplication::where('store_name_slug', $storeSlug)
            ->where('status', 'approved')
            ->firstOrFail();

        // Check if the store logo exists
        if (!$sellerApplication->store_logo) {
            // Return a default image if the store logo doesn't exist
            return response()->file(public_path('images/default-store-logo.png'));
        }

        // Check if the logo is stored in private or public storage
        if (str_starts_with($sellerApplication->store_logo, 'private/')) {
            // Check if the file exists in local storage
            if (!Storage::disk('local')->exists($sellerApplication->store_logo)) {
                return response()->file(public_path('images/default-store-logo.png'));
            }

            // Serve the file from private storage
            return response()->file(storage_path('app/' . $sellerApplication->store_logo));
        } else {
            // Check if the file exists in public storage
            if (!Storage::disk('public')->exists($sellerApplication->store_logo)) {
                return response()->file(public_path('images/default-store-logo.png'));
            }

            // Serve the file from public storage
            return response()->file(storage_path('app/public/' . $sellerApplication->store_logo));
        }
    }
}
