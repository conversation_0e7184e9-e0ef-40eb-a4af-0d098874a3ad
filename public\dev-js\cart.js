/**
 * Shopping cart functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Product image gallery functionality
    const mainImage = document.getElementById('main-image');
    const thumbnailContainers = document.querySelectorAll('.thumbnail-container');

    // Add click event to each thumbnail
    if (thumbnailContainers.length > 0 && mainImage) {
        thumbnailContainers.forEach(container => {
            container.addEventListener('click', function() {
                // Get the image source from the thumbnail
                const thumbnailImg = this.querySelector('.thumbnail');
                const imgSrc = thumbnailImg.getAttribute('data-src');

                // Update the main image
                mainImage.src = imgSrc;

                // Remove border from all thumbnails
                thumbnailContainers.forEach(item => {
                    item.classList.remove('border-2', 'border-indigo-500');
                    item.classList.add('hover:opacity-80');
                });

                // Add border to the clicked thumbnail
                this.classList.add('border-2', 'border-indigo-500');
                this.classList.remove('hover:opacity-80');
            });
        });
    }

    // Buy form submission
    const buyForm = document.getElementById('buy_form');
    if (buyForm) {
        buyForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            Swal.fire({
                title: "Loading",
                text: "Mohon tunggu sebentar",
                timerProgressBar: true,
                didOpen: () => {
                    Swal.showLoading();
                },
            });

            const productIds = this.querySelector("input[name='product_ids']").value;
            const csrfToken = document.getElementById('csrf_token').value;
            const appUrl = window.location.origin;

            // Check transactions
            fetch('/cart/check-transactions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken
                },
                body: JSON.stringify({
                    productIds: productIds
                })
            })
            .then(response => response.json())
            .then(data => {
                Swal.close();
                if (data.status === 'new-order') {
                    // Proceed with new transaction
                    getSnapToken(productIds);
                } else if (data.status === 'pending') {
                    // There's a pending transaction
                    Swal.fire({
                        title: "Transaksi tertunda",
                        text: data.message,
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonText: "Buat transaksi baru",
                        cancelButtonText: "Lanjut transaksi sebelumnya",
                        reverseButtons: true,
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // Cancel old transaction and create new one
                            Swal.fire({
                                title: "Loading",
                                text: "Mohon tunggu sebentar",
                                timerProgressBar: true,
                                didOpen: () => {
                                    Swal.showLoading();
                                },
                            });

                            fetch('/cart/cancel-transactions', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': csrfToken
                                },
                                body: JSON.stringify({
                                    orderId: data.orderId
                                })
                            })
                            .then(response => response.json())
                            .then(cancelData => {
                                Swal.close();
                                getSnapToken(productIds);
                            })
                            .catch(error => {
                                Swal.close();
                                Swal.fire("Gagal", "Gagal membatalkan transaksi lama.", "error");
                            });
                        } else {
                            // Continue with previous transaction
                            snap.pay(data.snap_token, {
                                onSuccess: function(result) {
                                    window.location.href = `${appUrl}/user/purchases`;
                                },
                                onPending: function(result) {
                                    window.location.href = `${appUrl}/user/purchases`;
                                },
                                onError: function(result) {
                                    location.reload();
                                },
                            });
                        }
                    });
                } else if (data.status === 'success') {
                    // Transaction is already completed
                    Swal.fire("Transaksi Selesai", data.message, "info");
                } else {
                    Swal.fire("Gagal", data.message ?? "Terjadi kesalahan.", "error");
                }
            })
            .catch(error => {
                Swal.close();
                Swal.fire("Oops!", "Gagal memeriksa transaksi. Coba lagi nanti.", "error");
            });
        });
    }
});

// Get Snap token for payment
function getSnapToken(productIds) {
    const csrfToken = document.getElementById('csrf_token').value;
    const appUrl = window.location.origin;
    
    fetch('/cart/process-payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            productIds: productIds
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log(data.snap_token);
        snap.pay(data.snap_token, {
            onSuccess: function(result) {
                window.location.href = `${appUrl}/user/purchases`;
            },
            onPending: function(result) {
                window.location.href = `${appUrl}/user/purchases`;
            },
            onError: function(result) {
                location.reload();
            },
        });
    })
    .catch(error => {
        Swal.fire("Oops!", "Gagal memproses pembayaran. Coba lagi nanti.", "error");
    });
}
