<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_course_progress', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->references('id')->on('users')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignUuid('course_id')->references('id')->on('courses')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignUuid('current_curriculum_item_id')->nullable()->references('id')->on('course_curriculum_items')->onUpdate('cascade')->onDelete('set null');
            $table->decimal('progress_percentage', 5, 2)->default(0.00); // 0.00 to 100.00
            $table->boolean('is_completed')->default(false);
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('last_accessed_at')->nullable();
            $table->json('completed_items')->nullable(); // Array of completed curriculum item IDs
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'course_id'], 'user_course_progress_user_course_idx');
            $table->index(['user_id', 'is_completed'], 'user_course_progress_user_completed_idx');
            $table->index(['course_id', 'is_completed'], 'user_course_progress_course_completed_idx');
            $table->index('last_accessed_at', 'user_course_progress_last_accessed_idx');

            // Ensure one progress record per user-course combination
            $table->unique(['user_id', 'course_id'], 'user_course_progress_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_course_progress');
    }
};
