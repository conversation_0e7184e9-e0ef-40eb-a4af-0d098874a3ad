<?php

namespace Database\Seeders;

use App\Models\SellerApplication;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class SellerApplicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure storage directory exists
        Storage::disk('public')->makeDirectory('seller/documents');
        Storage::disk('public')->makeDirectory('seller/logos');
        
        // Get all users who have the seller role
        $sellers = User::whereHas('activeRoles', function ($query) {
            $query->where('slug', 'seller');
        })->get();

        if ($sellers->isEmpty()) {
            $this->command->info('No sellers found. Please run UserSeeder first and ensure at least one user has the seller role.');
            return;
        }
        
        $this->command->info('Creating seller applications for ' . $sellers->count() . ' sellers...');
        
        // Sample bank names
        $bankNames = ['BCA', 'BNI', 'BRI', 'Mandiri', 'CIMB Niaga', 'Permata', 'Danamon'];
        
        // Sample payment methods
        $paymentMethods = ['bank_transfer', 'e-wallet', 'credit_card'];
        
        // Sample ID types
        $idTypes = ['KTP', 'SIM', 'Passport'];
        
        // Create seller applications for each seller
        foreach ($sellers as $seller) {
            // Generate a store name based on the user's name
            $storeName = $seller->name . "'s Store";
            $storeNameSlug = Str::slug($storeName);
            
            // Check if the slug already exists
            $counter = 1;
            $originalSlug = $storeNameSlug;
            while (SellerApplication::where('store_name_slug', $storeNameSlug)->exists()) {
                $storeNameSlug = $originalSlug . '-' . $counter;
                $counter++;
            }
            
            // Generate a random ID document file
            $idDocumentPath = $this->generateRandomDocument('seller/documents', 'id_document_' . Str::random(8) . '.pdf');
            
            // Generate a random store logo
            $storeLogoPath = $this->generateRandomLogo('seller/logos', 'store_logo_' . Str::random(8) . '.png');
            
            // Create the seller application
            SellerApplication::create([
                'user_id' => $seller->id,
                'id_type' => $idTypes[array_rand($idTypes)],
                'id_number' => '1234' . rand(100000, 999999),
                'id_document' => $idDocumentPath,
                'bank_name' => $bankNames[array_rand($bankNames)],
                'account_number' => rand(**********, **********),
                'account_holder_name' => $seller->name,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'store_name' => $storeName,
                'store_name_slug' => $storeNameSlug,
                'store_description' => 'This is a sample store description for ' . $storeName . '. We sell high-quality digital products.',
                'store_logo' => $storeLogoPath,
                'status' => 'approved',
            ]);
        }
        
        $this->command->info('Seller applications created successfully!');
    }
    
    /**
     * Generate a random document file
     *
     * @param string $directory
     * @param string $filename
     * @return string
     */
    protected function generateRandomDocument(string $directory, string $filename): string
    {
        $path = $directory . '/' . $filename;
        
        // Create a simple PDF-like content (not a real PDF, just for testing)
        $content = "This is a sample document for testing purposes.\n";
        $content .= "Generated at: " . now() . "\n";
        $content .= "Random data: " . Str::random(1000);
        
        // Save the file to storage
        Storage::disk('public')->put($path, $content);
        
        return $path;
    }
    
    /**
     * Generate a random logo placeholder
     *
     * @param string $directory
     * @param string $filename
     * @return string
     */
    protected function generateRandomLogo(string $directory, string $filename): string
    {
        $path = $directory . '/' . $filename;

        // Create a simple text file as a logo placeholder
        $content = "This is a sample logo placeholder for testing purposes.\n";
        $content .= "Generated at: " . now() . "\n";
        $content .= "Logo name: STORE LOGO\n";
        $content .= "Random data: " . Str::random(100);

        // Save the file to storage
        Storage::disk('public')->put($path, $content);

        return $path;
    }
}
