@extends('layouts.browse')

@section('title', $course->title . ' - Course Access')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/course-learning.css') }}?v={{ time() }}">
@endpush

@push('scripts')
<script src="{{ asset('dev-js/course-learning.js') }}?v={{ time() }}" defer></script>
@endpush

@section('content')
<div class="course-learning-container">
    <!-- Course Header -->
    <div class="course-header">
        <div class="course-header-content">
            <div class="course-header-left">
                <a href="{{ route('browse.courses.show', $course) }}" class="course-header-back">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div>
                    <h1 class="course-header-title">{{ $course->title }}</h1>
                    <p class="course-header-subtitle">Course Overview</p>
                    <div class="lesson-meta">
                        @if($course->difficulty_level)
                            <span class="lesson-type-badge lesson-type-overview">
                                {{ ucfirst($course->difficulty_level) }} Level
                            </span>
                        @endif
                        <span class="lesson-duration">by {{ $course->seller->name }}</span>
                    </div>
                </div>
            </div>
            <div class="course-header-right">
                <span class="course-progress-text">Progress: {{ number_format($progress->progress_percentage, 0) }}%</span>
                <div class="course-progress-bar">
                    <div class="course-progress-fill" style="width: {{ $progress->progress_percentage }}%"></div>
                </div>

                <!-- Sidebar Toggle Button -->
                <button class="sidebar-toggle active" aria-label="Hide course content sidebar">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <div class="course-layout">
        <!-- Course Sidebar -->
        <div class="course-sidebar">
            <div class="course-sidebar-header">
                Course Content
            </div>

            <!-- Course Overview Section -->
            <div class="course-section">
                <a href="{{ route('browse.courses.access', $course) }}"
                   class="course-curriculum-item course-overview-item active">
                    <div class="curriculum-item-content">
                        <div class="curriculum-item-details">
                            <p class="curriculum-item-title">Overview</p>
                        </div>
                    </div>
                </a>
            </div>

            @foreach($course->sections as $sectionIndex => $section)
                <div class="course-section">
                    <div class="course-section-header">
                        {{ $sectionIndex + 1 }}. {{ $section->title }}
                    </div>
                    @foreach($section->curriculumItems as $materialIndex => $material)
                        <a href="{{ route('browse.courses.curriculum-item', [$course, $material]) }}"
                           class="course-curriculum-item {{ $progress->isItemCompleted($material->id) ? 'visited' : '' }}">
                            <div class="curriculum-item-content">
                                @if($material->type === 'lecture')
                                    <svg class="curriculum-item-icon icon-lecture" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                @elseif($material->type === 'video')
                                    <svg class="curriculum-item-icon icon-video" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                @else
                                    <svg class="curriculum-item-icon icon-document" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                @endif
                                <div class="curriculum-item-details">
                                    <p class="curriculum-item-title">
                                        {{ $sectionIndex + 1 }}.{{ $materialIndex + 1 }} {{ $material->title }}
                                    </p>
                                    @if($material->estimated_duration)
                                        <p class="curriculum-item-duration">{{ $material->estimated_duration }} min</p>
                                    @endif
                                </div>
                            </div>
                        </a>
                    @endforeach
                </div>
            @endforeach
        </div>

        <!-- Main Content Area -->
        <div class="course-content-area">
            <div class="course-content-padding">
                <div class="course-overview-simple">
                    <h1 class="course-overview-title">{{ $course->title }}</h1>
                    <p class="course-overview-description">{{ $course->short_description }}</p>

                    @if($course->what_you_will_learn && count($course->what_you_will_learn) > 0)
                        <div class="course-learning-list">
                            <h3>What you'll learn</h3>
                            <ul>
                                @foreach(array_slice($course->what_you_will_learn, 0, 4) as $objective)
                                    <li>{{ $objective }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <div class="course-info-grid">
                        <div class="info-item">
                            <span class="info-number">{{ $course->sections->count() }}</span>
                            <span class="info-label">Sections</span>
                        </div>
                        <div class="info-item">
                            <span class="info-number">{{ $course->sections->sum(function($section) { return $section->curriculumItems->count(); }) }}</span>
                            <span class="info-label">Lessons</span>
                        </div>
                        @if($course->estimated_duration)
                            <div class="info-item">
                                <span class="info-number">{{ $course->estimated_duration }}</span>
                                <span class="info-label">Minutes</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Navigation -->
                <div class="course-navigation">
                    <div>
                        <!-- No previous item for course overview -->
                    </div>

                    <div class="flex gap-3">
                        @if($progress->is_completed)
                            <div class="text-center">
                                <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                                    <div class="flex items-center justify-center mb-2">
                                        <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-semibold text-green-800 mb-1">Congratulations!</h3>
                                    <p class="text-green-700">You've completed this course</p>
                                    @if($progress->completed_at)
                                        <p class="text-sm text-green-600 mt-1">Completed on {{ $progress->completed_at->format('F j, Y') }}</p>
                                    @endif
                                </div>
                                <div class="flex gap-3 justify-center">
                                    @if($firstItem)
                                        <a href="{{ route('browse.courses.curriculum-item', [$course, $firstItem]) }}"
                                           class="course-nav-button secondary">
                                            Review Course
                                            <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                    @endif
                                    <a href="{{ route('browse.courses') }}" class="course-nav-button">
                                        Find Other Courses
                                        <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        @else
                            @if($firstItem)
                                <a href="{{ route('browse.courses.curriculum-item', [$course, $firstItem]) }}"
                                   class="course-nav-button">
                                    Start Learning
                                    <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection
