<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    //midtrans
    'midtrans' => [
        // Midtrans server key
        'serverKey' => env('MIDTRANS_SERVERKEY'),
        // Midtrans client key
        'clientKey' => env('MIDTRANS_CLIENTKEY'),
        // Isi false jika masih tahap development dan true jika sudah di production, default false (development)
        'isProduction' => env('MIDTRANS_IS_PRODUCTION', false),
        'isSanitized' => env('MIDTRANS_IS_SANITIZED', true),
        'is3ds' => env('MIDTRANS_IS_3DS', true),
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_CLIENT_REDIRECT'),
    ],

    'gemini' => [
        'api_key' => env('GEMINI_API_KEY'),
        'project_number' => env('GEMINI_PROJECT_NUMBER'),
        'model' => env('GEMINI_MODEL', 'gemini-2.0-flash'),
    ],
];
