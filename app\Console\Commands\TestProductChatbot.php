<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductChatbotData;
use App\Models\MembershipTier;
use App\Models\UserMembership;

class TestProductChatbot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:product-chatbot';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the product chatbot functionality by setting up sample data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up test data for product chatbot...');

        // Get a test user (first user)
        $user = User::first();
        if (!$user) {
            $this->error('No users found. Please create a user first.');
            return Command::FAILURE;
        }

        $this->info("Using user: {$user->email}");

        // Upgrade user to Basic membership to allow chatbot
        $basicTier = MembershipTier::where('slug', 'basic')->first();
        if ($basicTier) {
            // Check if user already has basic membership
            $existingMembership = $user->memberships()->where('status', 'active')->first();

            if (!$existingMembership || $existingMembership->membershipTier->slug !== 'basic') {
                // Delete existing memberships to avoid constraint issues
                $user->memberships()->delete();

                // Create basic membership
                UserMembership::create([
                    'user_id' => $user->id,
                    'membership_tier_id' => $basicTier->id,
                    'started_at' => now(),
                    'expires_at' => now()->addMonth(),
                    'status' => 'active',
                    'amount_paid' => $basicTier->price,
                    'payment_method' => 'test',
                ]);

                $this->info('Upgraded user to Basic membership');
            } else {
                $this->info('User already has Basic membership');
            }
        }

        // Get a test product from this user, or any product if none found
        $product = $user->products()->first();
        if (!$product) {
            $product = Product::first();
            if (!$product) {
                $this->error('No products found in the system. Please create a product first.');
                return Command::FAILURE;
            }
            $this->info('Using first available product from system');
            // Update the user to be the product owner for testing
            $user = $product->seller;
        }

        $this->info("Using product: {$product->name}");

        // Create or update chatbot data
        $chatbotData = $product->chatbotData ?: new ProductChatbotData();

        $chatbotData->fill([
            'product_id' => $product->id,
            'main_function' => 'Template Excel ini digunakan untuk membuat laporan keuangan bulanan yang profesional dan mudah dipahami.',
            'key_features' => 'Formula otomatis, grafik dinamis, dashboard interaktif, support Excel 2016+, template siap pakai.',
            'target_users' => 'UMKM, akuntan, pemilik bisnis kecil, freelancer, dan siapa saja yang butuh laporan keuangan rapi.',
            'requirements' => 'Microsoft Excel 2016 atau lebih baru, kompatibel dengan Windows dan Mac, minimal 4GB RAM.',
            'usage_instructions' => 'Download file, buka di Excel, masukkan data keuangan Anda di sheet "Input Data", dan laporan akan ter-generate otomatis di sheet "Dashboard".',
            'unique_selling_points' => 'Template ini menghemat waktu hingga 80% dibanding membuat laporan manual, dengan formula yang sudah teruji akurat.',
            'limitations' => 'Tidak support Excel versi di bawah 2016, maksimal 1000 transaksi per bulan.',
            'troubleshooting' => 'Jika grafik tidak muncul, pastikan macro sudah diaktifkan. Jika formula error, cek format tanggal di kolom input.',
            'tips_optimization' => 'Gunakan fitur filter untuk analisis data yang lebih detail, backup file secara berkala.',
            'language_format_support' => 'Tersedia dalam Bahasa Indonesia dan English, format mata uang Rupiah dan Dollar.',
            'common_questions' => 'Q: Apakah bisa digunakan di Mac? A: Ya, selama menggunakan Excel 2016+. Q: Bisakah menambah kategori? A: Ya, bisa customize sesuai kebutuhan.',
            'contact_info' => 'Hubungi saya di email: <EMAIL> atau WhatsApp: 08123456789 untuk support.',
            'is_active' => true,
            'language' => 'id',
        ]);

        $chatbotData->save();

        // Update product relationship
        $product->update(['chatbot_data_id' => $chatbotData->id]);

        $this->info('Created chatbot data for product');
        $this->info("Completion: {$chatbotData->completion_percentage}%");
        $this->info("Has required data: " . ($chatbotData->hasRequiredData() ? 'Yes' : 'No'));
        $this->info("Is active: " . ($chatbotData->is_active ? 'Yes' : 'No'));

        // Test the chatbot functionality
        $this->info('Testing chatbot functionality...');

        // Refresh the product to get the latest data
        $product = $product->fresh(['chatbotData']);

        $this->info('Debug info:');
        $this->info('- Product ID: ' . $product->id);
        $this->info('- Chatbot data ID: ' . ($product->chatbot_data_id ?? 'null'));
        $this->info('- Has chatbot data: ' . ($product->chatbotData ? 'Yes' : 'No'));

        if ($product->chatbotData) {
            $this->info('- Chatbot is active: ' . ($product->chatbotData->is_active ? 'Yes' : 'No'));
            $this->info('- Has required data: ' . ($product->chatbotData->hasRequiredData() ? 'Yes' : 'No'));
        }

        if ($product->hasActiveChatbot()) {
            $this->info('✓ Product has active chatbot');

            // Test the formatted data for AI
            $formattedData = $chatbotData->getFormattedDataForAi();
            $this->info('✓ Formatted data for AI generated successfully');
            $this->line('Sample formatted data:');
            $this->line(substr($formattedData, 0, 200) . '...');
        } else {
            $this->error('✗ Product chatbot is not active');
        }

        $this->info('Test completed successfully!');
        $this->info("You can now test the chatbot on the product page:");
        $this->info("Product URL: /store/{$product->seller->sellerApplication->store_name_slug}/{$product->slug}");

        return Command::SUCCESS;
    }
}
