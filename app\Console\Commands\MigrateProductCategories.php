<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\ProductSubcategory;
use Illuminate\Console\Command;

class MigrateProductCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:migrate-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing products to the new category system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting migration of product categories...');

        // Get all products without a detailed_category_id
        $products = Product::whereNull('detailed_category_id')->get();
        $this->info("Found {$products->count()} products to migrate.");

        if ($products->isEmpty()) {
            $this->info('No products to migrate.');
            return 0;
        }

        // Legacy categories mapping to subcategories
        $legacyMapping = [
            'template' => 'templates',
            'spreadsheet' => 'spreadsheets',
            'dashboard' => 'dashboards',
            'planner' => 'planners',
            'worksheet' => 'worksheets',
            'graphic' => 'graphics',
            'font' => 'fonts',
            'illustration' => 'illustrations',
            'ui_kit' => 'ui-kits',
            'code' => 'code',
            'plugin' => 'plugins',
            'api_template' => 'api-templates',
            'course_material' => 'course-materials',
            'ebook' => 'e-books',
        ];

        $bar = $this->output->createProgressBar($products->count());
        $bar->start();

        $migrated = 0;
        $skipped = 0;

        foreach ($products as $product) {
            $legacyCategory = $product->category;

            // Skip if no legacy category
            if (empty($legacyCategory) || !isset($legacyMapping[$legacyCategory])) {
                $skipped++;
                $bar->advance();
                continue;
            }

            // Get the subcategory slug for this legacy category
            $subcategorySlug = $legacyMapping[$legacyCategory];

            // Find the subcategory
            $subcategory = ProductSubcategory::where('slug', $subcategorySlug)->first();

            if (!$subcategory) {
                $skipped++;
                $bar->advance();
                continue;
            }

            // Get the category through the subcategory
            $category = $subcategory->category;

            if (!$category) {
                $skipped++;
                $bar->advance();
                continue;
            }

            // Get a random detailed category from this subcategory
            $detailedCategory = $subcategory->detailedCategories()->inRandomOrder()->first();

            if (!$detailedCategory) {
                $skipped++;
                $bar->advance();
                continue;
            }

            // Update the product with the new category IDs
            $product->category_id = $category->id;
            $product->subcategory_id = $subcategory->id;
            $product->detailed_category_id = $detailedCategory->id;
            $product->save();

            $migrated++;
            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        $this->info("Migration completed: {$migrated} products migrated, {$skipped} products skipped.");

        return 0;
    }
}
