@extends('layouts.main')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumbs -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700 transition-colors duration-200">Home</a>
                </li>
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <a href="{{ route('help') }}" class="ml-2 text-gray-500 hover:text-gray-700 transition-colors duration-200">Help Center</a>
                </li>
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <a href="#" class="ml-2 text-gray-500 hover:text-gray-700 transition-colors duration-200">{{ $article->category->name }}</a>
                </li>
                <li class="flex items-center">
                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="ml-2 text-gray-700 font-medium">{{ $article->title }}</span>
                </li>
            </ol>
        </nav>

        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-8">
                <div class="p-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-6">{{ $article->title }}</h1>
                    
                    <!-- Article Content -->
                    <div class="prose prose-purple max-w-none">
                        {!! $article->content !!}
                    </div>
                    
                    <!-- Article Metadata -->
                    <div class="mt-8 pt-6 border-t border-gray-200 flex items-center justify-between">
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Last updated: {{ $article->updated_at->format('F j, Y') }}
                        </div>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-4">Was this article helpful?</span>
                            <button class="text-gray-500 hover:text-green-600 transition-colors duration-200 mr-2">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                </svg>
                            </button>
                            <button class="text-gray-500 hover:text-red-600 transition-colors duration-200">
                                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018a2 2 0 01.485.06l3.76.94m-7 10v5a2 2 0 002 2h.096c.5 0 .905-.405.905-.904 0-.715.211-1.413.608-2.008L17 13V4m-7 10h2"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Articles -->
            @if($article->relatedArticles->count() > 0)
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Articles</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        @foreach($article->relatedArticles as $relatedArticle)
                            <a href="{{ route('help.article', $relatedArticle->slug) }}" class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $relatedArticle->title }}</h3>
                                <p class="text-gray-600 text-sm mb-4">{{ Str::limit(strip_tags($relatedArticle->content), 100) }}</p>
                                <div class="flex items-center text-purple-600 font-medium">
                                    <span>Read Article</span>
                                    <svg class="h-5 w-5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </div>
                            </a>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Contact Support -->
            <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-8 text-center text-white">
                <h2 class="text-2xl font-bold mb-4">Still Need Help?</h2>
                <p class="mb-6 max-w-2xl mx-auto">Can't find what you're looking for? Our support team is here to help.</p>
                <a href="{{ route('contact') }}" class="inline-block bg-white text-purple-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
                    Contact Support
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
