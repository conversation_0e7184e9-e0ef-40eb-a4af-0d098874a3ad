<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_chatbot_data', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->references('id')->on('products')->onUpdate('cascade')->onDelete('cascade');

            // Required questions for chatbot activation (nullable for draft saving)
            $table->text('main_function')->nullable(); // Apa fungsi utama produk ini?
            $table->text('key_features')->nullable(); // Apa saja fitur utama produk ini?
            $table->text('target_users')->nullable(); // Siapa target pengguna produk ini?
            $table->text('requirements')->nullable(); // Apa saja persyaratan untuk menggunakan produk ini?
            $table->text('usage_instructions')->nullable(); // Bagaimana cara menggunakan produk ini?

            // Optional questions for enhanced responses
            $table->text('unique_selling_points')->nullable(); // Apa yang membuat produk ini berbeda?
            $table->text('limitations')->nullable(); // Apakah ada batasan atau hal yang perlu diperhatikan?
            $table->text('troubleshooting')->nullable(); // Bagaimana cara mengatasi masalah umum?
            $table->text('tips_optimization')->nullable(); // Tips untuk memaksimalkan penggunaan?
            $table->text('language_format_support')->nullable(); // Dukungan bahasa atau format tertentu?

            // Additional fields for better chatbot responses
            $table->text('common_questions')->nullable(); // FAQ yang sering ditanyakan
            $table->text('contact_info')->nullable(); // Info kontak seller untuk pertanyaan lanjutan

            // Chatbot status and settings
            $table->boolean('is_active')->default(false); // Whether chatbot is active for this product
            $table->enum('language', ['id', 'en'])->default('id'); // Chatbot response language
            $table->json('custom_responses')->nullable(); // Custom Q&A pairs

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_chatbot_data');
    }
};
