@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div class="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">Products</h1>
            <p class="text-gray-600">Manage your digital products with ease</p>
        </div>
        <div class="flex items-center gap-3">
            {{-- <div x-data="{ open: false }" class="relative">
                <button @click="open = !open" class="inline-flex items-center gap-1 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Export
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 rounded-lg bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-10">
                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <span>Export as CSV</span>
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <span>Export as PDF</span>
                    </a>
                </div>
            </div> --}}
            <a href="{{ route('seller.products.select-type', [], false) ?? '#' }}" class="inline-flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-md hover:bg-indigo-700 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                    <path d="M5 12h14"></path>
                    <path d="M12 5v14"></path>
                </svg>
                Add Product
            </a>
        </div>
    </div>

    <div class="rounded-xl border bg-white shadow-lg">
        <div class="flex flex-col gap-4 border-b border-gray-100 p-6 sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Your Products</h3>
                <p class="text-sm text-gray-600">
                    @if($products instanceof \Illuminate\Pagination\LengthAwarePaginator)
                        You have {{ $products->total() }} products
                    @else
                        You have {{ count($products ?? []) }} products
                    @endif
                </p>
            </div>
            <form class="flex items-center gap-3" method="GET" action="{{ route('seller.products.index') }}">
                <div class="relative">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-2.5 h-4 w-4 text-gray-400">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.3-4.3"></path>
                    </svg>
                    <input type="search" name="search" value="{{ request('search') }}" placeholder="Search products..." class="w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 text-sm focus:border-indigo-500 focus:ring-indigo-500 md:w-[200px] lg:w-[300px] transition-colors">
                </div>
                <select name="status" class="rounded-lg border border-gray-200 py-2 pl-3 pr-10 text-sm focus:border-indigo-500 focus:ring-indigo-500 transition-colors" onchange="this.form.submit()">
                    <option value="all" {{ request('status') == 'all' || !request('status') ? 'selected' : '' }}>All</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                    <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                </select>
            </form>
        </div>
        <div class="p-6">
            @if($products->isEmpty())
                <p class="text-center text-gray-500">No products found</p>
            @else
                <div class="product-grid">
                    @foreach($products as $product)
                        <div class="product-card">
                            @if($product->status === 'draft' && $product->content_type === 'course')
                                <a href="{{ route('seller.products.continue-draft', $product) }}" class="product-card-link">
                            @else
                                <a href="{{ route('seller.products.edit', $product, false) ?? '#' }}" class="product-card-link">
                            @endif
                                <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}" alt="{{ $product->name ?? 'Product' }}" class="product-card-image">
                                <div class="product-card-content">
                                    <h4 class="product-card-title">{{ $product->name ?? 'N/A' }}</h4>
                                    <div class="product-card-meta">
                                        <span>{{ $product->getDetailedCategoryNameAttribute() ?? $product->getSubcategoryNameAttribute() ?? $product->getCategoryNameAttribute() ?? ucfirst($product->category ?? 'Uncategorized') }}</span>
                                        @if($product->status === 'draft' && $product->content_type === 'course')
                                            <span class="status-label inline-flex items-center rounded-full border border-blue-200 bg-blue-50 px-3 py-1 text-xs font-medium text-blue-700">
                                                <svg class="mr-1 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Course Draft
                                            </span>
                                        @else
                                            <span class="status-label inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium {{ $product->status === 'active' ? 'border-green-100 bg-green-50 text-green-700' : 'border-orange-100 bg-orange-50 text-orange-700' }}">
                                                {{ ucfirst($product->status ?? 'draft') }}
                                            </span>
                                        @endif
                                    </div>
                                    <div class="product-card-meta">
                                        <span class="product-card-price">Rp {{ number_format($product->price ?? 0, 0, ',', '.') }}</span>
                                        <span>Sales: {{ $product->orders_count ?? 0 }}</span>
                                    </div>
                                </div>
                            </a>
                            <div class="product-card-actions" x-data="{ open: false }">
                                <button @click="open = !open" class="dropdown-toggle">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-900">
                                        <circle cx="12" cy="12" r="1"></circle>
                                        <circle cx="19" cy="12" r="1"></circle>
                                        <circle cx="5" cy="12" r="1"></circle>
                                    </svg>
                                </button>
                                <div x-show="open" @click.away="open = false" class="dropdown-menu">
                                    @if($product->status === 'draft' && $product->content_type === 'course')
                                        <a href="{{ route('seller.products.continue-draft', $product) }}" class="text-blue-600 font-medium">
                                            <svg class="inline w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            Continue Editing
                                        </a>
                                    @else
                                        <a href="{{ route('products.show', $product->slug, false) ?? '#' }}" target="_blank">Preview Product</a>
                                        <a href="{{ route('seller.products.edit', $product, false) ?? '#' }}">Edit</a>
                                    @endif
                                    <form action="{{ route('seller.products.destroy', $product, false) ?? '#' }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="delete" onclick="return confirm('Are you sure you want to delete this product?')">Delete</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
        <div class="border-t border-gray-100 px-6 py-4">
            @if($products instanceof \Illuminate\Pagination\LengthAwarePaginator)
                <div class="pagination-container">
                    {{ $products->links('vendor.pagination.custom-tailwind') }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection