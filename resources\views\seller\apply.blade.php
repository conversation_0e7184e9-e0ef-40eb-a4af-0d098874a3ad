@extends('layouts.main')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-white seller-page">
        <div class="container max-w-2xl mx-auto pb-8 px-4">
            <!-- Back to Home -->
            {{-- <a href="{{ route('user.dashboard') }}" class="back-link text-indigo-600 hover:text-indigo-700">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span>Back to Home</span>
            </a> --}}

            <!-- Header -->
            <div class="header-section text-center">
                <h1 class="text-3xl font-bold tracking-tight md:text-4xl text-gray-900">Become a Seller on Digitora</h1>
                <p class="mt-3 text-gray-600 text-lg">Join thousands of creators selling their digital products worldwide
                </p>
            </div>

            <!-- Progress Bar -->
            <div class="progress-section">
                <div class="flex justify-between max-w-md mx-auto">
                    @php
                        $steps = [
                            ['name' => 'Basic Info', 'step' => 1],
                            ['name' => 'Identity', 'step' => 2],
                            ['name' => 'Payment', 'step' => 3],
                            ['name' => 'Store Setup', 'step' => 4],
                        ];
                    @endphp
                    @foreach ($steps as $step)
                        <div class="flex flex-col items-center">
                            <div
                                class="flex h-12 w-12 items-center justify-center rounded-full border-2 {{ $currentStep > $step['step'] ? 'border-indigo-600 bg-indigo-600 text-white' : ($currentStep === $step['step'] ? 'border-indigo-600 text-indigo-600' : 'border-gray-300 text-gray-300') }}">
                                @if ($currentStep > $step['step'])
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                @else
                                    {{ $step['step'] }}
                                @endif
                            </div>
                            <span
                                class="mt-2 text-sm font-medium {{ $currentStep >= $step['step'] ? 'text-indigo-600' : 'text-gray-400' }}">{{ $step['name'] }}</span>
                        </div>
                    @endforeach
                </div>
                <div class="relative mt-4 max-w-md mx-auto">
                    <div class="absolute h-1 w-full bg-gray-200 rounded-full" style="height: 4px;"></div>
                    <div class="absolute h-1 bg-indigo-600 rounded-full transition-all duration-300"
                        style="width: {{ (($currentStep - 1) / (count($steps) - 1)) * 100 }}%; height: 4px;"></div>
                </div>
            </div>

            <!-- Form -->
            <form method="POST" action="{{ route('seller.store') }}" enctype="multipart/form-data"
                class="seller-apply-form" id="seller-form">
                @csrf
                <div class="bg-white border-0 shadow-lg rounded-xl mx-auto form-container" style="max-width: 550px;">
                    <!-- Step 1: Basic Info -->
                    @if ($currentStep === 1)
                        <div class="p-6">
                            <h2 class="text-xl font-semibold text-gray-900">Basic Information</h2>
                            <p class="text-gray-600 text-sm mt-1">Let's start with your basic details</p>
                        </div>
                        <div class="px-6 pb-6 space-y-6">
                            <div class="space-y-2">
                                <label for="email" class="block text-sm font-medium text-gray-700">
                                    Email Address <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="email" name="email" id="email"
                                        value="{{ old('email', $formData['email'] ?? '') }}" placeholder="<EMAIL>"
                                        class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm bg-gray-100 cursor-not-allowed pr-10"
                                        readonly required>
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <svg class="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500">We'll use your account email for all communications.
                                    This field cannot be changed.</p>
                                @error('email')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="full_name" class="block text-sm font-medium text-gray-700">
                                    Full Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="full_name" id="full_name"
                                    value="{{ old('full_name', $formData['full_name'] ?? '') }}" placeholder="John Doe"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>
                                <p class="text-xs text-gray-500">This will update your account name. Please enter your legal name as it appears on your
                                    identification.</p>
                                @error('full_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="phone" class="block text-sm font-medium text-gray-700">
                                    Phone Number / WhatsApp <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="phone" id="phone"
                                    value="{{ old('phone', $formData['phone'] ?? Auth::user()->phone ?? '') }}" placeholder="+62 812 3456 7890"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>
                                <p class="text-xs text-gray-500">Your active phone number for verification and communication purposes.</p>
                                @error('phone')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    @endif

                    <!-- Step 2: Identity Verification -->
                    @if ($currentStep === 2)
                        <div class="p-6">
                            <h2 class="text-xl font-semibold text-gray-900">Identity Verification</h2>
                            <p class="text-gray-600 text-sm mt-1">We need to verify your identity to comply with regulations
                                and protect our community</p>
                        </div>
                        <div class="px-6 pb-6 space-y-6">
                            <div class="rounded-lg bg-indigo-50 p-4 flex items-start gap-3">
                                <svg class="h-5 w-5 text-indigo-600 mt-0.5" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 11c0-1.1-.9-2-2-2H8a2 2 0 00-2 2v4a2 2 0 002 2h2a2 2 0 002-2v-1m0-3v-1m4 3h2a2 2 0 002-2v-4a2 2 0 00-2-2h-2a2 2 0 00-2 2v1m0 3v1">
                                    </path>
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900">Your data is secure</h3>
                                    <p class="text-xs text-gray-600">We use industry-standard encryption to protect your
                                        personal information. Your data is never shared with third parties without your
                                        consent.</p>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <label for="id_type" class="block text-sm font-medium text-gray-700">ID Type</label>
                                <select name="id_type" id="id_type"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300">
                                    <option value="passport"
                                        {{ old('id_type', $formData['id_type'] ?? '') === 'passport' ? 'selected' : '' }}>
                                        Passport</option>
                                    <option value="national_id"
                                        {{ old('id_type', $formData['id_type'] ?? '') === 'national_id' ? 'selected' : '' }}>
                                        National ID</option>
                                    <option value="drivers_license"
                                        {{ old('id_type', $formData['id_type'] ?? '') === 'drivers_license' ? 'selected' : '' }}>
                                        Driver's License</option>
                                </select>
                                @error('id_type')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="id_number" class="block text-sm font-medium text-gray-700">
                                    ID Number <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="id_number" id="id_number"
                                    value="{{ old('id_number', $formData['id_number'] ?? '') }}"
                                    placeholder="Enter your ID number"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>
                                @error('id_number')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="rounded-lg border border-dashed border-gray-300 p-4 text-center">
                                <p class="text-sm font-medium text-gray-900">Upload a photo of your ID (Optional)</p>
                                <p class="text-xs text-gray-600 mt-1">You can complete this step later, but verification
                                    will be faster if you upload it now</p>
                                <p class="text-xs text-gray-500 mt-1">Accepted formats: JPG, PNG, PDF. Max size: 2MB</p>
                                @if (isset($formData['id_document_path']) && $formData['id_document_path'])
                                    <p class="text-xs text-green-600 mt-1">File uploaded successfully</p>
                                    <input type="hidden" name="id_document_path"
                                        value="{{ $formData['id_document_path'] }}">
                                @endif
                                <input type="file" name="id_document" id="id_document" accept=".jpg,.jpeg,.png,.pdf"
                                    class="mt-2 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                @error('id_document')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                @error('file_upload')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    @endif

                    <!-- Step 3: Payment Information -->
                    @if ($currentStep === 3)
                        <div class="p-6">
                            <h2 class="text-xl font-semibold text-gray-900">Payment Information</h2>
                            <p class="text-gray-600 text-sm mt-1">Tell us where to send your earnings from sales on
                                Digitora</p>
                        </div>
                        <div class="px-6 pb-6 space-y-6">
                            <div class="space-y-2">
                                <label for="bank_name" class="block text-sm font-medium text-gray-700">
                                    Bank Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="bank_name" id="bank_name"
                                    value="{{ old('bank_name', $formData['bank_name'] ?? '') }}"
                                    placeholder="Enter your bank name"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>
                                @error('bank_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="account_number" class="block text-sm font-medium text-gray-700">
                                    Account Number <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="account_number" id="account_number"
                                    value="{{ old('account_number', $formData['account_number'] ?? '') }}"
                                    placeholder="Enter your account number"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>
                                @error('account_number')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="account_holder_name" class="block text-sm font-medium text-gray-700">
                                    Account Holder Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="account_holder_name" id="account_holder_name"
                                    value="{{ old('account_holder_name', $formData['account_holder_name'] ?? '') }}"
                                    placeholder="Enter the name on your account"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>
                                <p class="text-xs text-gray-500">This should match the name on your bank account exactly
                                </p>
                                @error('account_holder_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex items-start gap-3 rounded-lg bg-indigo-50 p-4">
                                <svg class="h-5 w-5 text-indigo-600 mt-0.5" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900">Payment Schedule</h3>
                                    <p class="text-xs text-gray-600">Payments are processed on the 15th of each month for the previous month's revenue.
                                       </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Step 4: Store Setup -->
                    @if ($currentStep === 4)
                        <div class="p-6">
                            <h2 class="text-xl font-semibold text-gray-900">Store Setup</h2>
                            <p class="text-gray-600 text-sm mt-1">Create your store profile that customers will see when
                                browsing your products</p>
                        </div>
                        <div class="px-6 pb-6 space-y-6">
                            <div class="space-y-2">
                                <label for="store_name" class="block text-sm font-medium text-gray-700">
                                    Store Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="store_name" id="store_name"
                                    value="{{ old('store_name', $formData['store_name'] ?? '') }}"
                                    placeholder="Your Creative Store Name"
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>
                                <p class="text-xs text-gray-500">Choose a memorable name that represents your brand (max 50
                                    characters)</p>
                                @error('store_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="store_description" class="block text-sm font-medium text-gray-700">
                                    Store Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="store_description" id="store_description" rows="4"
                                    placeholder="Tell potential customers about your store and what makes your products special..."
                                    class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                    required>{{ old('store_description', $formData['store_description'] ?? '') }}</textarea>
                                <p class="text-xs text-gray-500">Describe what you sell and what makes your products unique
                                    (max 500 characters)</p>
                                @error('store_description')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            {{-- <div class="space-y-2">
                                <label for="store_category" class="block text-sm font-medium text-gray-700">Primary Category</label>
                                <select name="store_category" id="store_category" class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300">
                                    <option value="digital-products" {{ old('store_category', $formData['store_category'] ?? '') === 'digital-products' ? 'selected' : '' }}>Digital Products (General)</option>
                                    <option value="templates" {{ old('store_category', $formData['store_category'] ?? '') === 'templates' ? 'selected' : '' }}>Templates & Themes</option>
                                    <option value="graphics" {{ old('store_category', $formData['store_category'] ?? '') === 'graphics' ? 'selected' : '' }}>Graphics & Design Assets</option>
                                    <option value="ebooks" {{ old('store_category', $formData['store_category'] ?? '') === 'ebooks' ? 'selected' : '' }}>eBooks & Guides</option>
                                    <option value="courses" {{ old('store_category', $formData['store_category'] ?? '') === 'courses' ? 'selected' : '' }}>Online Courses</option>
                                    <option value="software" {{ old('store_category', $formData['store_category'] ?? '') === 'software' ? 'selected' : '' }}>Software & Apps</option>
                                </select>
                                @error('store_category')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div> --}}

                            <div class="rounded-lg border border-dashed border-gray-300 p-4 text-center">
                                <p class="text-sm font-medium text-gray-900">Upload Store Logo (Optional)</p>
                                <p class="text-xs text-gray-600 mt-1">Recommended size: 500x500px. Max file size: 2MB</p>
                                <p class="text-xs text-gray-500 mt-1">Accepted formats: JPG, PNG</p>
                                @if (isset($formData['store_logo_path']) && $formData['store_logo_path'])
                                    <p class="text-xs text-green-600 mt-1">File uploaded successfully</p>
                                    <input type="hidden" name="store_logo_path"
                                        value="{{ $formData['store_logo_path'] }}">
                                @endif
                                <input type="file" name="store_logo" id="store_logo" accept=".jpg,.jpeg,.png"
                                    class="mt-2 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                                @error('store_logo')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                                @error('file_upload')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    @endif

                    <!-- Error Messages -->
                    @if ($errors->any())
                        <div class="px-6 py-3 bg-red-50 border-t border-b border-red-200">
                            @foreach ($errors->all() as $error)
                                <p class="text-red-600 text-sm">{{ $error }}</p>
                            @endforeach
                        </div>
                    @endif

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between border-t p-6">
                        @if ($currentStep > 1)
                            <button type="submit" name="step" value="{{ $currentStep - 1 }}"
                                class="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors duration-200 font-medium">Back</button>
                        @else
                            <div></div>
                        @endif
                        <button type="submit" name="step" value="{{ $currentStep < 4 ? $currentStep + 1 : 5 }}"
                            class="bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition-colors duration-200 flex items-center gap-2 font-medium"
                            style="background-color: #4f46e5;">
                            {{ $currentStep < 4 ? 'Continue' : 'Submit Application' }}
                            @if ($currentStep < 4)
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7"></path>
                                </svg>
                            @endif
                        </button>
                    </div>
                </div>
            </form>

            <!-- Terms -->
            <div class="mt-3 text-center text-sm text-gray-600">
                <p>By continuing, you agree to Digitora's <a href="{{ route('terms.alt') }}" class="text-indigo-600 hover:underline">Terms of Service</a>, and <a href="{{ route('privacy.alt') }}" class="text-indigo-600 hover:underline">Privacy Policy</a>.</p>
            </div>
        </div>
    </div>
@endsection
