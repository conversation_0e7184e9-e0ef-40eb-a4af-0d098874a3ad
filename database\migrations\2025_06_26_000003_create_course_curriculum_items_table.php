<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_curriculum_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('course_id')->references('id')->on('courses')->onUpdate('cascade')->onDelete('cascade');
            $table->foreignUuid('section_id')->references('id')->on('course_sections')->onUpdate('cascade')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['lecture', 'video', 'pdf', 'document']); // Udemy-style content types
            $table->longText('content')->nullable(); // For lecture content or video URLs
            $table->string('file_path')->nullable(); // For PDF and document attachments
            $table->json('metadata')->nullable(); // Additional metadata (file size, video duration, etc.)
            $table->integer('sort_order')->default(0);
            $table->integer('estimated_duration')->nullable(); // Duration in minutes
            $table->boolean('is_active')->default(true);
            $table->boolean('is_preview')->default(false); // Can be accessed before purchase
            $table->timestamps();

            // Indexes for better performance
            $table->index(['course_id', 'section_id', 'sort_order'], 'course_curriculum_hierarchy_sort_idx');
            $table->index(['section_id', 'sort_order'], 'course_curriculum_section_sort_idx');
            $table->index(['course_id', 'type'], 'course_curriculum_course_type_idx');
            $table->index(['is_preview'], 'course_curriculum_preview_idx');
            $table->index(['is_active'], 'course_curriculum_active_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_curriculum_items');
    }
};
