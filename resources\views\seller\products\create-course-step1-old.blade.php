@extends('seller.layouts.app')

@push('scripts')
    <script>
        // Pass the category mappings from the server to JavaScript
        const legacyCategoryMapping = @json($legacyCategoryMapping ?? []);
        const legacySubcategoryMapping = @json($legacySubcategoryMapping ?? []);
        // Pass course data for image loading
        const courseData = @json($courseData ?? []);
        // Pass category tree for mapping legacy codes to IDs
        const categoryTree = @json($categoryTree ?? []);
    </script>
    <script src="{{ asset(js_path() . '/course-image-upload.js') }}"></script>
@endpush

@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center gap-4">
            <a href="{{ route('seller.products.select-type') }}"
                class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
                <span class="sr-only">Back</span>
            </a>
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Create New Course</h1>
                <p class="text-gray-600">Step 1 of 2: Basic Course Information</p>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="bg-white rounded-lg border shadow-sm p-6">
            @if(isset($isDraftContinuation) && $isDraftContinuation)
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-sm text-blue-800">
                            <strong>Continuing Draft:</strong> You're continuing a previously saved course draft.
                        </span>
                    </div>
                </div>
            @elseif(isset($isNewCourse) && $isNewCourse)
                <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span class="text-sm text-green-800">
                            <strong>New Course:</strong> Starting fresh course creation.
                        </span>
                    </div>
                </div>
            @endif

            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">
                        1
                    </div>
                    <span class="text-sm font-medium text-blue-600">Basic Information</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-500 rounded-full text-sm font-medium">
                        2
                    </div>
                    <span class="text-sm text-gray-500">Course Structure & Content</span>
                </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 50%"></div>
            </div>
        </div>

        <!-- Form -->
        <form action="{{ route('seller.products.save-course-step', ['step' => 1]) }}" method="POST" enctype="multipart/form-data">
            @csrf
            
            <div class="grid gap-6 md:grid-cols-6">
                <!-- Left Column -->
                <div class="space-y-6 md:col-span-4">
                    <!-- Course Information -->
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Course Information</h3>
                            <p class="text-sm text-gray-600">Basic information about your course</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <!-- Course Title -->
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-medium text-gray-700">
                                    Course Title <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" id="name"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="e.g., Complete Web Development Bootcamp" 
                                    value="{{ old('name', $courseData['step1']['name'] ?? '') }}" required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Course Description -->
                            <div class="space-y-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">
                                    Course Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" id="description" rows="6"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="Describe what students will learn in this course..." required>{{ old('description', $courseData['step1']['description'] ?? '') }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Category Selection -->
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Category</h3>
                            <p class="text-sm text-gray-600">Choose the most relevant category for your course</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="category" class="block text-sm font-medium text-gray-700">
                                    Category <span class="text-red-500">*</span>
                                </label>
                                <select name="category" id="category"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    required>
                                    <option value="">Select a category</option>
                                    @foreach($categoryGroups as $categoryName => $subcategories)
                                        <optgroup label="{{ $categoryName }}">
                                            @foreach($subcategories as $legacyCode => $subcategoryName)
                                                <option value="{{ $legacyCode }}" 
                                                    {{ old('category', $courseData['step1']['category'] ?? '') == $legacyCode ? 'selected' : '' }}>
                                                    {{ $subcategoryName }}
                                                </option>
                                            @endforeach
                                        </optgroup>
                                    @endforeach
                                </select>
                                @error('category')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Hidden fields for category IDs -->
                            <input type="hidden" name="category_id" id="category_id" value="{{ old('category_id', $courseData['step1']['category_id'] ?? '') }}">
                            <input type="hidden" name="subcategory_id" id="subcategory_id" value="{{ old('subcategory_id', $courseData['step1']['subcategory_id'] ?? '') }}">
                            <input type="hidden" name="detailed_category_id" id="detailed_category_id" value="{{ old('detailed_category_id', $courseData['step1']['detailed_category_id'] ?? '') }}">
                        </div>
                    </div>

                    <!-- Course Images -->
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Course Images</h3>
                            <p class="text-sm text-gray-600">Upload images to showcase your course (max 10 images, 2MB each)</p>
                        </div>
                        <div class="p-6">
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 transition-colors duration-200 hover:border-gray-400">
                                <div class="text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="mt-4">
                                        <label for="images" class="cursor-pointer">
                                            <span class="mt-2 block text-sm font-medium text-gray-900">Upload course images</span>
                                            <input id="images" name="images[]" type="file" class="sr-only" multiple accept="image/*">
                                        </label>
                                        <p class="mt-1 text-sm text-gray-500">PNG, JPG, GIF, WebP up to 2MB each (max 10 images)</p>
                                        <p class="mt-1 text-xs text-gray-400">Drag and drop files here or click to browse</p>
                                    </div>
                                </div>
                            </div>
                            @error('images')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            @error('images.*')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6 md:col-span-2">
                    <!-- Pricing -->
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Pricing</h3>
                            <p class="text-sm text-gray-600">Set your course pricing</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="price" class="block text-sm font-medium text-gray-700">
                                    Price (Rp) <span class="text-red-500">*</span>
                                </label>
                                <input type="number" name="price" id="price" step="0.01" min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="99000" value="{{ old('price', $courseData['step1']['price'] ?? '') }}" required>
                                <p class="mt-1 text-xs text-gray-500">Minimum price is Rp 5,000</p>
                                @error('price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" name="has_discount" id="has_discount" value="1"
                                    class="rounded border-gray-200 text-indigo-600 focus:ring-indigo-500"
                                    {{ old('has_discount', $courseData['step1']['has_discount'] ?? false) ? 'checked' : '' }}>
                                <label for="has_discount" class="text-sm font-medium text-gray-700">Enable discount price</label>
                            </div>
                            
                            <div id="discount-price-container" class="space-y-2 {{ old('has_discount', $courseData['step1']['has_discount'] ?? false) ? '' : 'hidden' }}">
                                <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price (Rp)</label>
                                <input type="number" name="discount_price" id="discount_price" step="0.01" min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="79000" value="{{ old('discount_price', $courseData['step1']['discount_price'] ?? '') }}">
                                <p class="mt-1 text-xs text-gray-500">Minimum discount price is Rp 5,000</p>
                                @error('discount_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="p-6">
                            <div class="flex flex-col gap-3">
                                <button type="submit"
                                    class="inline-flex w-full items-center justify-center rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-md hover:bg-blue-700 transition-colors">
                                    Continue to Step 2
                                    <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </button>
                                <button type="button" id="save-draft-btn"
                                    class="inline-flex w-full items-center justify-center rounded-lg border border-amber-300 bg-amber-50 px-4 py-2 text-sm font-medium text-amber-700 shadow-sm hover:bg-amber-100 transition-colors">
                                    <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    Save as Draft
                                </button>
                                <button type="button" id="cancel-btn"
                                    class="inline-flex w-full items-center justify-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script>
        // Handle discount price toggle
        document.getElementById('has_discount').addEventListener('change', function() {
            const discountContainer = document.getElementById('discount-price-container');
            if (this.checked) {
                discountContainer.classList.remove('hidden');
            } else {
                discountContainer.classList.add('hidden');
                document.getElementById('discount_price').value = '';
            }
        });

        // Handle category selection and populate hidden fields
        document.getElementById('category').addEventListener('change', function() {
            const selectedLegacyCode = this.value;

            // Clear hidden fields first
            document.getElementById('category_id').value = '';
            document.getElementById('subcategory_id').value = '';
            document.getElementById('detailed_category_id').value = '';

            if (!selectedLegacyCode) {
                return;
            }

            // Find the matching subcategory and populate the hidden fields
            if (categoryTree && categoryTree.length > 0) {
                for (const category of categoryTree) {
                    if (category.active_subcategories) {
                        for (const subcategory of category.active_subcategories) {
                            if (subcategory.legacy_code === selectedLegacyCode) {
                                document.getElementById('category_id').value = category.id;
                                document.getElementById('subcategory_id').value = subcategory.id;

                                // Set the first detailed category if available
                                if (subcategory.active_detailed_categories && subcategory.active_detailed_categories.length > 0) {
                                    document.getElementById('detailed_category_id').value = subcategory.active_detailed_categories[0].id;
                                }
                                return;
                            }
                        }
                    }
                }
            }
        });

        // Auto-save and draft functionality
        document.addEventListener('DOMContentLoaded', function() {
            const saveDraftBtn = document.getElementById('save-draft-btn');
            const cancelBtn = document.getElementById('cancel-btn');
            const form = document.getElementById('course-form');
            let isFormDirty = false;

            // Track form changes
            if (form) {
                form.addEventListener('input', function() {
                    isFormDirty = true;
                });
            }

            // Save as draft button
            if (saveDraftBtn) {
                saveDraftBtn.addEventListener('click', function() {
                    saveCourseAsDraft();
                });
            }

            // Cancel button with confirmation
            if (cancelBtn) {
                cancelBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleCancel();
                });
            }

            // Auto-save on page unload if form is dirty
            window.addEventListener('beforeunload', function(e) {
                if (isFormDirty && form) {
                    // Try to save as draft before leaving
                    saveCourseAsDraft(false); // Don't show notifications on auto-save
                }
            });

            function saveCourseAsDraft(showNotification = true) {
                // First save the current form data to session
                if (form) {
                    const formData = new FormData(form);

                    // Submit the form data to save to session first
                    fetch(form.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    }).then(() => {
                        // Then save as draft
                        return fetch('{{ route('seller.products.save-course-as-draft') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });
                    }).then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            isFormDirty = false;
                            if (showNotification) {
                                alert('Course saved as draft successfully!');
                                window.location.href = data.redirect_url;
                            }
                        } else {
                            if (showNotification) {
                                alert('Failed to save draft: ' + data.message);
                            }
                        }
                    }).catch(error => {
                        console.error('Error saving draft:', error);
                        if (showNotification) {
                            alert('Error saving draft. Please try again.');
                        }
                    });
                }
            }

            function handleCancel() {
                if (isFormDirty) {
                    const userChoice = confirm(
                        'You have unsaved changes. What would you like to do?\n\n' +
                        'Click "OK" to save as draft and exit\n' +
                        'Click "Cancel" to discard changes and exit'
                    );

                    if (userChoice) {
                        // User chose to save as draft
                        saveCourseAsDraft();
                    } else {
                        // User chose to discard changes
                        if (confirm('Are you sure you want to discard all changes? This cannot be undone.')) {
                            isFormDirty = false;
                            window.location.href = '{{ route('seller.products.select-type') }}';
                        }
                    }
                } else {
                    // No changes, just redirect
                    window.location.href = '{{ route('seller.products.select-type') }}';
                }
            }
        });
    </script>
@endsection
