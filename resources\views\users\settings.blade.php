@extends('layouts.user-dashboard')

@section('content')
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-bold text-gray-900">Account Settings</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Manage your account preferences</p>
    </div>

    <div class="border-t border-gray-200">
        <form action="{{ route('user.settings.update') }}" method="POST" class="divide-y divide-gray-200">
            @csrf
            @method('PUT')

            <div class="py-6 px-4 sm:p-6">
                <div class="max-w-xl">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Notifications</h3>
                    <p class="mt-1 text-sm text-gray-500">Decide which communications you'd like to receive</p>
                </div>

                <div class="mt-6">
                    <fieldset>
                        <legend class="text-base font-medium text-gray-900">By Email</legend>
                        <div class="mt-4 space-y-4">
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="email_order_updates" name="email_order_updates" type="checkbox" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" checked>
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="email_order_updates" class="font-medium text-gray-700">Order updates</label>
                                    <p class="text-gray-500">Get notified when your orders are processed or completed.</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="email_promotions" name="email_promotions" type="checkbox" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" checked>
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="email_promotions" class="font-medium text-gray-700">Promotions</label>
                                    <p class="text-gray-500">Receive information about deals and new products.</p>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="py-3 px-4 sm:px-6 text-right">
                <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save
                </button>
            </div>
        </form>
    </div>
</div>

<div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg font-medium text-gray-900">Delete Account</h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Permanently delete your account and all data</p>
    </div>

    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
        <div class="max-w-xl text-sm text-gray-500">
            <p>Once you delete your account, all of your data will be permanently removed. This action cannot be undone.</p>
        </div>
        <div class="mt-5">
            <button type="button" class="inline-flex items-center justify-center px-4 py-2 border border-transparent font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm">
                Delete account
            </button>
        </div>
    </div>
</div>
@endsection
