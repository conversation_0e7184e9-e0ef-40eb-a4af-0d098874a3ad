@extends('layouts.browse')

@section('title', 'Browse Online Courses - Digitora')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-r from-indigo-600 to-purple-700 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
            <h1 class="text-4xl font-bold sm:text-5xl lg:text-6xl">
                Master New Skills
            </h1>
            <p class="mt-6 max-w-3xl mx-auto text-xl text-indigo-100">
                Learn from industry experts with our comprehensive online courses designed for Indonesian entrepreneurs and professionals
            </p>
            <div class="mt-8">
                <div class="flex justify-center">
                    <div class="flex bg-white rounded-lg p-1 max-w-md w-full">
                        <input type="text"
                               placeholder="Search courses..."
                               class="flex-1 px-4 py-2 text-gray-900 bg-transparent border-0 focus:outline-none">
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-md font-medium transition-colors">
                            Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Course Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">Filter Courses</h2>
            <span class="text-sm text-gray-500">{{ $courses->total() }} courses found</span>
        </div>
        <form method="GET" action="{{ route('user.browse.courses') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <!-- Search -->
                <div class="lg:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text"
                           id="search"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Search courses..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <!-- Difficulty -->
                <div>
                    <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
                    <select id="difficulty"
                            name="difficulty"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">All Levels</option>
                        <option value="beginner" {{ request('difficulty') === 'beginner' ? 'selected' : '' }}>Beginner</option>
                        <option value="intermediate" {{ request('difficulty') === 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                        <option value="advanced" {{ request('difficulty') === 'advanced' ? 'selected' : '' }}>Advanced</option>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select id="sort"
                            name="sort"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                        <option value="popular" {{ request('sort') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                        <option value="price_low" {{ request('sort') === 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') === 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="rating" {{ request('sort') === 'rating' ? 'selected' : '' }}>Highest Rated</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit"
                            class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        Apply Filters
                    </button>
                </div>
            </div>
            @if(request('search') || request('difficulty') || (request('sort') && request('sort') != 'newest'))
                <div class="flex justify-end">
                    <a href="{{ route('user.browse.courses') }}"
                       class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">
                        Clear all filters
                    </a>
                </div>
            @endif
        </form>
    </div>

    <!-- Course Grid -->
        @if($courses->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($courses as $course)
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:border-indigo-200 transition-all duration-300 group">
                        <!-- Course Thumbnail -->
                        <div class="relative overflow-hidden">
                            <img src="{{ $course->thumbnail_url ?? asset('images/course-placeholder.jpg') }}"
                                 alt="{{ $course->title }}"
                                 class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                            <div class="absolute top-3 left-3">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-white/90 backdrop-blur-sm text-gray-800">
                                    {{ $course->detailedCategory->name ?? 'Course' }}
                                </span>
                            </div>
                            <div class="absolute top-3 right-3">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {{ $course->difficulty_level === 'beginner' ? 'bg-green-100 text-green-800' :
                                       ($course->difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                    {{ ucfirst($course->difficulty_level) }}
                                </span>
                            </div>
                        </div>

                        <!-- Course Content -->
                        <div class="p-5">
                            <!-- Title -->
                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-indigo-600 transition-colors">
                                <a href="{{ route('browse.courses.show', $course) }}">
                                    {{ $course->title }}
                                </a>
                            </h3>

                            <!-- Description -->
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                {{ $course->short_description }}
                            </p>

                            <!-- Instructor -->
                            <div class="flex items-center mb-4">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-medium text-white">
                                            {{ substr($course->seller->name, 0, 1) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ $course->seller->name }}</p>
                                    @if($course->seller->sellerApplication)
                                        <p class="text-xs text-gray-500">{{ $course->seller->sellerApplication->store_name }}</p>
                                    @endif
                                </div>
                            </div>

                            <!-- Course Stats -->
                            <div class="flex items-center text-xs text-gray-500 mb-4 space-x-4">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    {{ $course->sections->count() }} sections
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    {{ $course->curriculumItems->count() }} lessons
                                </span>
                                @if($course->estimated_duration)
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ floor($course->estimated_duration / 60) }}h {{ $course->estimated_duration % 60 }}m
                                    </span>
                                @endif
                            </div>

                            <!-- Price and Action -->
                            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                <div>
                                    @if($course->discount_price)
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xl font-bold text-gray-900">Rp {{ number_format($course->discount_price) }}</span>
                                            <span class="text-sm text-gray-500 line-through">Rp {{ number_format($course->price) }}</span>
                                        </div>
                                        <div class="text-xs text-green-600 font-medium">
                                            Save {{ number_format((($course->price - $course->discount_price) / $course->price) * 100, 0) }}%
                                        </div>
                                    @else
                                        <span class="text-xl font-bold text-gray-900">Rp {{ number_format($course->price) }}</span>
                                    @endif
                                </div>
                                <a href="{{ route('browse.courses.show', $course) }}"
                                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm hover:shadow-md">
                                    View Course
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

        <!-- Pagination -->
        <div class="mt-12">
            {{ $courses->appends(request()->query())->links() }}
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-16">
            <div class="max-w-md mx-auto">
                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No courses found</h3>
                <p class="mt-2 text-gray-500">
                    We couldn't find any courses matching your criteria. Try adjusting your filters or browse all available courses.
                </p>
                <div class="mt-8">
                    <a href="{{ route('user.browse.courses') }}"
                       class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-base font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 transition-colors">
                        Browse All Courses
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>
</div>

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
@endsection
