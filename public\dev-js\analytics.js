/**
 * Analytics charts initialization
 */
document.addEventListener('DOMContentLoaded', function() {
    // Debug info toggle - commented out as requested
    /*
    const toggleDebug = document.getElementById('toggle-debug');
    if (toggleDebug) {
        toggleDebug.addEventListener('click', function() {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
        });
    }
    */

    // Wait for the DOM to be fully loaded
    window.addEventListener('load', function() {
        console.log('Window loaded, initializing charts...');

        try {
            // Revenue Chart
            const revenueCanvas = document.getElementById('revenueChart');
            if (!revenueCanvas) {
                console.error('Revenue chart canvas not found');
                return;
            }

            const revenueCtx = revenueCanvas.getContext('2d');
            const revenueData = JSON.parse(document.getElementById('revenue-data').textContent);
            console.log('Revenue data:', revenueData);

            // Format dates and prepare data for chart
            const revenueLabels = [];
            const revenueValues = [];

            revenueData.forEach(item => {
                try {
                    const date = new Date(item.date);
                    revenueLabels.push(date.toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'short'
                    }));
                    revenueValues.push(parseFloat(item.revenue) || 0);
                } catch (e) {
                    console.error('Error processing revenue data item:', item, e);
                }
            });

            console.log('Revenue labels:', revenueLabels);
            console.log('Revenue values:', revenueValues);

            // Create gradient for revenue chart
            const revenueGradient = revenueCtx.createLinearGradient(0, 0, 0, 400);
            revenueGradient.addColorStop(0, 'rgba(79, 70, 229, 0.4)');
            revenueGradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');

            // Create revenue chart
            const revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: revenueLabels,
                    datasets: [{
                        label: 'Revenue (Rp)',
                        data: revenueValues,
                        borderColor: '#4F46E5',
                        backgroundColor: revenueGradient,
                        borderWidth: 2,
                        pointBackgroundColor: '#4F46E5',
                        pointRadius: 3,
                        pointHoverRadius: 5,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let value = context.raw;
                                    return 'Rp ' + value.toLocaleString('id-ID');
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    if (value >= 1000000) {
                                        return 'Rp ' + (value / 1000000).toFixed(1) + ' jt';
                                    } else if (value >= 1000) {
                                        return 'Rp ' + (value / 1000).toFixed(1) + ' rb';
                                    }
                                    return 'Rp ' + value;
                                }
                            }
                        }
                    }
                }
            });

            // Sales Chart
            const salesCanvas = document.getElementById('salesChart');
            if (!salesCanvas) {
                console.error('Sales chart canvas not found');
                return;
            }

            const salesCtx = salesCanvas.getContext('2d');
            const salesData = JSON.parse(document.getElementById('sales-data').textContent);
            console.log('Sales data:', salesData);

            // Format dates and prepare data for chart
            const salesLabels = [];
            const salesValues = [];

            salesData.forEach(item => {
                try {
                    const date = new Date(item.date);
                    salesLabels.push(date.toLocaleDateString('id-ID', {
                        day: 'numeric',
                        month: 'short'
                    }));
                    salesValues.push(parseInt(item.count) || 0);
                } catch (e) {
                    console.error('Error processing sales data item:', item, e);
                }
            });

            console.log('Sales labels:', salesLabels);
            console.log('Sales values:', salesValues);

            // Create gradient for sales chart
            const salesGradient = salesCtx.createLinearGradient(0, 0, 0, 400);
            salesGradient.addColorStop(0, 'rgba(168, 85, 247, 0.4)');
            salesGradient.addColorStop(1, 'rgba(168, 85, 247, 0.0)');

            // Create sales chart
            const salesChart = new Chart(salesCtx, {
                type: 'bar',
                data: {
                    labels: salesLabels,
                    datasets: [{
                        label: 'Sales Count',
                        data: salesValues,
                        backgroundColor: salesGradient,
                        borderColor: '#A855F7',
                        borderWidth: 1,
                        borderRadius: 4,
                        hoverBackgroundColor: 'rgba(168, 85, 247, 0.6)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                precision: 0
                            }
                        }
                    }
                }
            });
        } catch (error) {
            console.error('Error initializing charts:', error);
        }
    });
});
