<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\CourseSection;
use App\Models\CourseCurriculumItem;
use App\Models\User;
use App\Models\ProductDetailedCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure storage directories exist
        Storage::disk('public')->makeDirectory('courses/thumbnails');
        Storage::disk('public')->makeDirectory('courses/images');
        Storage::disk('public')->makeDirectory('courses/materials');

        // Get all sellers
        $sellers = User::whereHas('activeRoles', function ($query) {
            $query->where('slug', 'seller');
        })->get();

        if ($sellers->isEmpty()) {
            $this->command->info('No sellers found. Please run UserSeeder first.');
            return;
        }

        // Get course-specific detailed categories
        $courseCategories = ProductDetailedCategory::whereHas('subcategory.category', function ($query) {
            $query->where('product_type', 'course');
        })->with(['subcategory.category'])->get();

        if ($courseCategories->isEmpty()) {
            $this->command->info('No course categories found. Please run NewCourseCategorySeeder first.');
            return;
        }

        $this->command->info('Creating courses for ' . $sellers->count() . ' sellers...');

        // Sample course data
        $coursesData = $this->getSampleCoursesData();

        foreach ($sellers as $seller) {
            $this->command->info("Creating courses for seller: {$seller->name}");
            
            // Create 2-4 courses per seller
            $courseCount = rand(2, 4);
            $selectedCourses = collect($coursesData)->random($courseCount);

            foreach ($selectedCourses as $courseData) {
                $this->createCourse($seller, $courseData, $courseCategories);
            }
        }

        $this->command->info('Course seeding completed!');
    }

    /**
     * Create a course with its chapters, subchapters, and materials
     */
    private function createCourse($seller, $courseData, $courseCategories)
    {
        // Select a random category
        $category = $courseCategories->random();

        // Create the course
        $course = Course::create([
            'seller_id' => $seller->id,
            'title' => $courseData['title'],
            'slug' => Course::generateUniqueSlug($courseData['title']),
            'description' => $courseData['description'],
            'short_description' => $courseData['short_description'],
            'category_id' => $category->subcategory->category->id,
            'subcategory_id' => $category->subcategory->id,
            'detailed_category_id' => $category->id,
            'price' => $courseData['price'],
            'discount_price' => $courseData['discount_price'] ?? null,
            'difficulty_level' => $courseData['difficulty_level'],
            'status' => $courseData['status'],
            'what_you_will_learn' => $courseData['what_you_will_learn'],
            'requirements' => $courseData['requirements'],
            'target_audience' => $courseData['target_audience'],
            'estimated_duration' => $courseData['estimated_duration'],
            'published_at' => $courseData['status'] === 'active' ? now() : null,
        ]);

        $this->command->info("  Created course: {$course->title}");

        // Create sections (simplified from chapters)
        foreach ($courseData['chapters'] as $sectionIndex => $sectionData) {
            $section = CourseSection::create([
                'course_id' => $course->id,
                'title' => $sectionData['title'],
                'description' => $sectionData['description'] ?? null,
                'sort_order' => $sectionIndex + 1,
                'is_active' => true,
            ]);

            // Create curriculum items (flattened from subchapters and materials)
            $curriculumItemIndex = 1;
            foreach ($sectionData['subchapters'] as $subchapterData) {
                // Create curriculum items from materials
                foreach ($subchapterData['materials'] as $materialData) {
                    // Map old material types to new Udemy-style types
                    $newType = match($materialData['type']) {
                        'text' => 'lecture',
                        'video' => 'video',
                        'file' => 'document',
                        default => $materialData['type']
                    };

                    CourseCurriculumItem::create([
                        'course_id' => $course->id,
                        'section_id' => $section->id,
                        'title' => $materialData['title'],
                        'description' => $materialData['description'] ?? null,
                        'type' => $newType,
                        'content' => $materialData['content'] ?? null,
                        'file_path' => null,
                        'metadata' => $materialData['metadata'] ?? null,
                        'sort_order' => $curriculumItemIndex,
                        'estimated_duration' => $materialData['estimated_duration'] ?? null,
                        'is_active' => true,
                        'is_preview' => $materialData['is_preview'] ?? false,
                    ]);
                    $curriculumItemIndex++;
                }
            }
        }
    }

    /**
     * Get sample courses data
     */
    private function getSampleCoursesData(): array
    {
        return [
            [
                'title' => 'Complete WhatsApp Bot Development Course',
                'short_description' => 'Learn to build powerful WhatsApp bots for business automation',
                'description' => 'Master the art of WhatsApp bot development from scratch. This comprehensive course covers everything from basic setup to advanced automation features. You\'ll learn to integrate with WhatsApp Business API, create intelligent chatbots, and automate customer service processes.',
                'price' => 299000,
                'discount_price' => 199000,
                'difficulty_level' => 'intermediate',
                'status' => 'active',
                'estimated_duration' => 480,
                'what_you_will_learn' => [
                    'Set up WhatsApp Business API',
                    'Create intelligent chatbots',
                    'Automate customer service',
                    'Integrate with external systems',
                    'Handle multimedia messages'
                ],
                'requirements' => [
                    'Basic programming knowledge',
                    'Understanding of APIs',
                    'WhatsApp Business account'
                ],
                'target_audience' => [
                    'Business owners',
                    'Developers',
                    'Digital marketers',
                    'Entrepreneurs'
                ],
                'chapters' => [
                    [
                        'title' => 'Introduction to WhatsApp Bot Development',
                        'description' => 'Understanding the basics of WhatsApp automation',
                        'subchapters' => [
                            [
                                'title' => 'What is WhatsApp Bot?',
                                'description' => 'Introduction to WhatsApp automation',
                                'estimated_duration' => 15,
                                'is_preview' => true,
                                'materials' => [
                                    [
                                        'title' => 'Welcome Video',
                                        'type' => 'video',
                                        'content' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                                        'estimated_duration' => 10,
                                        'is_preview' => true
                                    ],
                                    [
                                        'title' => 'Course Overview',
                                        'type' => 'text',
                                        'content' => 'This course will teach you everything you need to know about WhatsApp bot development...',
                                        'estimated_duration' => 5
                                    ]
                                ]
                            ],
                            [
                                'title' => 'Setting Up Development Environment',
                                'description' => 'Preparing your development tools',
                                'estimated_duration' => 30,
                                'materials' => [
                                    [
                                        'title' => 'Environment Setup Guide',
                                        'type' => 'video',
                                        'content' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                                        'estimated_duration' => 20
                                    ],
                                    [
                                        'title' => 'Required Tools Checklist',
                                        'type' => 'file',
                                        'content' => 'Download the complete checklist of required development tools',
                                        'estimated_duration' => 5
                                    ],
                                    [
                                        'title' => 'Troubleshooting Common Issues',
                                        'type' => 'text',
                                        'content' => 'Common setup problems and their solutions...',
                                        'estimated_duration' => 5
                                    ]
                                ]
                            ]
                        ]
                    ],
                    [
                        'title' => 'WhatsApp Business API Integration',
                        'description' => 'Learn to integrate with WhatsApp Business API',
                        'subchapters' => [
                            [
                                'title' => 'API Authentication',
                                'description' => 'Setting up API credentials and authentication',
                                'estimated_duration' => 25,
                                'materials' => [
                                    [
                                        'title' => 'API Setup Tutorial',
                                        'type' => 'video',
                                        'content' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                                        'estimated_duration' => 20
                                    ],
                                    [
                                        'title' => 'Authentication Code Examples',
                                        'type' => 'file',
                                        'content' => 'Download sample authentication code',
                                        'estimated_duration' => 5
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'AI Automation for Small Business',
                'short_description' => 'Implement AI solutions to automate your business processes',
                'description' => 'Discover how to leverage artificial intelligence to streamline your business operations. This course covers practical AI implementations for small businesses, including chatbots, automated customer service, and process optimization.',
                'price' => 399000,
                'discount_price' => 299000,
                'difficulty_level' => 'beginner',
                'status' => 'active',
                'estimated_duration' => 360,
                'what_you_will_learn' => [
                    'Understand AI basics for business',
                    'Implement chatbot solutions',
                    'Automate customer service',
                    'Optimize business processes',
                    'Measure AI ROI'
                ],
                'requirements' => [
                    'Basic computer skills',
                    'Business management experience',
                    'Willingness to learn new technology'
                ],
                'target_audience' => [
                    'Small business owners',
                    'Entrepreneurs',
                    'Business managers',
                    'Digital transformation leaders'
                ],
                'chapters' => [
                    [
                        'title' => 'Introduction to AI for Business',
                        'description' => 'Understanding AI applications in business',
                        'subchapters' => [
                            [
                                'title' => 'What is Business AI?',
                                'description' => 'Introduction to AI in business context',
                                'estimated_duration' => 20,
                                'is_preview' => true,
                                'materials' => [
                                    [
                                        'title' => 'AI Business Overview',
                                        'type' => 'video',
                                        'content' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                                        'estimated_duration' => 15,
                                        'is_preview' => true
                                    ],
                                    [
                                        'title' => 'AI Success Stories',
                                        'type' => 'text',
                                        'content' => 'Real-world examples of successful AI implementations in small businesses...',
                                        'estimated_duration' => 5
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'Complete Web Development Bootcamp',
                'short_description' => 'Learn full-stack web development from zero to hero',
                'description' => 'Master modern web development with this comprehensive bootcamp. Learn HTML, CSS, JavaScript, React, Node.js, and database management. Build real-world projects and deploy them to production.',
                'price' => 599000,
                'discount_price' => 399000,
                'difficulty_level' => 'beginner',
                'status' => 'active',
                'estimated_duration' => 720,
                'what_you_will_learn' => [
                    'HTML, CSS, and JavaScript fundamentals',
                    'React.js for frontend development',
                    'Node.js and Express for backend',
                    'Database design and management',
                    'Deployment and hosting'
                ],
                'requirements' => [
                    'Basic computer skills',
                    'No programming experience required',
                    'Dedication to practice'
                ],
                'target_audience' => [
                    'Aspiring web developers',
                    'Career changers',
                    'Students',
                    'Entrepreneurs'
                ],
                'chapters' => [
                    [
                        'title' => 'Frontend Fundamentals',
                        'description' => 'Master HTML, CSS, and JavaScript',
                        'subchapters' => [
                            [
                                'title' => 'HTML Basics',
                                'description' => 'Learn HTML structure and elements',
                                'estimated_duration' => 45,
                                'is_preview' => true,
                                'materials' => [
                                    [
                                        'title' => 'HTML Introduction',
                                        'type' => 'video',
                                        'content' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                                        'estimated_duration' => 30,
                                        'is_preview' => true
                                    ],
                                    [
                                        'title' => 'HTML Cheat Sheet',
                                        'type' => 'file',
                                        'content' => 'Download comprehensive HTML reference',
                                        'estimated_duration' => 5
                                    ],
                                    [
                                        'title' => 'Practice Exercises',
                                        'type' => 'text',
                                        'content' => 'Complete these HTML exercises to reinforce your learning...',
                                        'estimated_duration' => 10
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            [
                'title' => 'Digital Marketing Mastery',
                'short_description' => 'Master digital marketing strategies for business growth',
                'description' => 'Learn comprehensive digital marketing strategies including SEO, social media marketing, content marketing, email marketing, and paid advertising. Perfect for business owners and marketing professionals.',
                'price' => 349000,
                'discount_price' => 249000,
                'difficulty_level' => 'intermediate',
                'status' => 'active',
                'estimated_duration' => 420,
                'what_you_will_learn' => [
                    'SEO optimization techniques',
                    'Social media marketing strategies',
                    'Content marketing best practices',
                    'Email marketing automation',
                    'Paid advertising campaigns'
                ],
                'requirements' => [
                    'Basic marketing knowledge',
                    'Access to social media platforms',
                    'Business or personal brand to promote'
                ],
                'target_audience' => [
                    'Business owners',
                    'Marketing professionals',
                    'Freelancers',
                    'Entrepreneurs'
                ],
                'chapters' => [
                    [
                        'title' => 'Digital Marketing Foundations',
                        'description' => 'Understanding digital marketing landscape',
                        'subchapters' => [
                            [
                                'title' => 'Digital Marketing Overview',
                                'description' => 'Introduction to digital marketing channels',
                                'estimated_duration' => 25,
                                'is_preview' => true,
                                'materials' => [
                                    [
                                        'title' => 'Digital Marketing Landscape',
                                        'type' => 'video',
                                        'content' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                                        'estimated_duration' => 20,
                                        'is_preview' => true
                                    ],
                                    [
                                        'title' => 'Marketing Strategy Template',
                                        'type' => 'file',
                                        'content' => 'Download marketing strategy planning template',
                                        'estimated_duration' => 5
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}
