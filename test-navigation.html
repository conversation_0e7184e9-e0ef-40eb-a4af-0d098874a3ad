<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Mobile Navigation Improvements */
        @media (max-width: 1023px) {
            .store-nav {
                display: none;
            }
        }
        
        /* Test styles */
        .test-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #000;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 9999;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="test-info">
        Screen width: <span id="width"></span>px
    </div>

    <!-- Header similar to store layout -->
    <header class="bg-white shadow-lg sticky top-0 z-50 border-b border-gray-100">
        <!-- Top Bar -->
        <div class="bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 py-3">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="h-12 w-12 bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-xl flex items-center justify-center">
                            <span class="text-xl font-bold text-white">R</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xl font-bold text-white tracking-tight">Test Store</span>
                            <span class="text-sm text-indigo-100 font-medium">Digital Store</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <div class="bg-white">
            <div class="container mx-auto px-4">
                <div class="flex items-center justify-between h-16">
                    <!-- Desktop Navigation -->
                    <nav class="store-nav hidden lg:flex">
                        <a href="#" class="px-4 py-2 text-gray-700 hover:text-indigo-600">Home</a>
                        <a href="#" class="px-4 py-2 text-gray-700 hover:text-indigo-600">Products</a>
                        <a href="#" class="px-4 py-2 text-gray-700 hover:text-indigo-600">Courses</a>
                        <a href="#" class="px-4 py-2 text-gray-700 hover:text-indigo-600">About</a>
                    </nav>

                    <!-- Desktop Search and Cart -->
                    <div class="hidden lg:flex items-center space-x-4">
                        <input type="text" placeholder="Search..." class="px-4 py-2 border rounded-lg">
                        <button class="px-4 py-2 bg-indigo-600 text-white rounded-lg">Cart</button>
                    </div>

                    <!-- Mobile Actions -->
                    <div class="flex items-center lg:hidden space-x-3">
                        <button class="p-2 rounded-lg hover:bg-gray-50">Cart</button>
                        <button id="mobile-menu-toggle" class="p-3 rounded-lg hover:bg-gray-50">
                            <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="lg:hidden hidden bg-white border-t border-gray-200 shadow-lg">
                <div class="px-4 pt-4 pb-3 space-y-2">
                    <a href="#" class="block px-4 py-3 text-gray-700 hover:bg-indigo-50">Home</a>
                    <a href="#" class="block px-4 py-3 text-gray-700 hover:bg-indigo-50">Products</a>
                    <a href="#" class="block px-4 py-3 text-gray-700 hover:bg-indigo-50">Courses</a>
                    <a href="#" class="block px-4 py-3 text-gray-700 hover:bg-indigo-50">About</a>
                </div>
                <div class="px-4 py-3 border-t border-gray-100">
                    <input type="text" placeholder="Search..." class="w-full px-4 py-3 border rounded-lg">
                </div>
            </div>
        </div>
    </header>

    <!-- Content -->
    <main class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-4">Navigation Test</h1>
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Test Instructions:</h2>
            <ol class="list-decimal list-inside space-y-2">
                <li>Resize your browser window to different widths</li>
                <li>Check that desktop navigation shows on screens ≥ 1024px</li>
                <li>Check that mobile menu button shows on screens < 1024px</li>
                <li>Verify there's no gap where both are hidden</li>
                <li>Test mobile menu toggle functionality</li>
            </ol>
            
            <div class="mt-6 p-4 bg-gray-100 rounded">
                <h3 class="font-semibold mb-2">Expected Behavior:</h3>
                <ul class="list-disc list-inside space-y-1 text-sm">
                    <li><strong>≥ 1024px:</strong> Desktop nav visible, mobile button hidden</li>
                    <li><strong>< 1024px:</strong> Desktop nav hidden, mobile button visible</li>
                    <li><strong>No gap:</strong> At exactly 1024px, one should be visible</li>
                </ul>
            </div>
        </div>
    </main>

    <script>
        // Update screen width display
        function updateWidth() {
            document.getElementById('width').textContent = window.innerWidth;
        }
        
        updateWidth();
        window.addEventListener('resize', updateWidth);
        
        // Mobile menu toggle
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });
    </script>
</body>
</html>
