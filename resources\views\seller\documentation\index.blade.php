@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div>
        <h1 class="text-3xl font-bold tracking-tight">Documentation</h1>
        <p class="text-gray-500">Comprehensive guides to help you succeed on Digitora</p>
    </div>

    <!-- Search Bar -->
    <div class="rounded-lg border bg-white p-6 shadow-sm">
        <div class="mx-auto max-w-2xl">
            <form action="{{ route('seller.documentation.index') }}" method="GET">
                <div class="relative">
                    <input type="text" name="search" placeholder="Search documentation..." value="{{ request()->query('search') }}" class="block w-full rounded-md border-gray-300 py-3 pl-4 pr-12 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <button type="submit">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Documentation Sections -->

    @if($sections->isEmpty())
        <p class="text-gray-500">No results found for your search.</p>
    @else
        <div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            @foreach($sections as $section)
            <div class="rounded-lg border bg-white shadow-sm transition-all hover:shadow-md">
                <div class="p-6">
                    <div class="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100 text-indigo-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            @if($section->icon === 'rocket')
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 006.16-12.12A14.98 14.98 0 009.631 8.41m5.96 5.96a14.926 14.926 0 01-5.841 2.58m-.119-8.54a6 6 0 00-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 00-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 01-2.448-2.448 14.9 14.9 0 01.06-.312m-2.24 2.39a4.493 4.493 0 00-1.757 4.306 4.493 4.493 0 004.306-1.758M16.5 9a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z" />
                            @elseif($section->icon === 'package')
                                <path stroke-linecap="round" stroke-linejoin="round" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            @elseif($section->icon === 'shopping-cart')
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            @elseif($section->icon === 'credit-card')
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                            @elseif($section->icon === 'chart-bar')
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            @elseif($section->icon === 'cog')
                                <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            @endif
                        </svg>
                    </div>
                    <h3 class="mb-2 text-lg font-medium">{{ $section->title }}</h3>
                    <p class="mb-4 text-sm text-gray-500">{{ $section->description }}</p>
                    @if($section->topics->isNotEmpty())
                        <ul class="space-y-2">
                            @foreach($section->topics as $topic)
                            <li>
                                <a href="{{ route('seller.documentation.show', $topic->slug) }}" class="flex items-center text-sm text-indigo-600 hover:text-indigo-800 hover:underline">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    {{ $topic->title }}
                                </a>
                            </li>
                            @endforeach
                        </ul>
                    @else
                        <p class="text-sm text-gray-500">No topics available.</p>
                    @endif
                    <div class="mt-4">
                        <a href="{{ route('seller.documentation.index', ['section' => $section->id]) }}" class="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                            View all topics →
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    @endif

    <!-- Getting Started Guide -->
    <div class="rounded-lg border bg-white p-6 shadow-sm">
        <div class="grid gap-6 md:grid-cols-2">
            <div>
                <h2 class="text-xl font-bold">New to Digitora?</h2>
                <p class="mt-2 text-gray-500">Start with our comprehensive guide for new sellers</p>
                <div class="mt-4 space-y-4">
                    <div class="flex items-start">
                        <div class="flex h-6 w-6 items-center justify-center rounded-full bg-indigo-100 text-indigo-600">
                            <span class="text-xs font-medium">1</span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium">Set up your account</h3>
                            <p class="text-xs text-gray-500">Complete your profile and store information</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex h-6 w-6 items-center justify-center rounded-full bg-indigo-100 text-indigo-600">
                            <span class="text-xs font-medium">2</span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium">Create your first product</h3>
                            <p class="text-xs text-gray-500">Upload files and set up your product listing</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex h-6 w-6 items-center justify-center rounded-full bg-indigo-100 text-indigo-600">
                            <span class="text-xs font-medium">3</span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium">Set up payment methods</h3>
                            <p class="text-xs text-gray-500">Configure how you'll receive your earnings</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex h-6 w-6 items-center justify-center rounded-full bg-indigo-100 text-indigo-600">
                            <span class="text-xs font-medium">4</span>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium">Promote your products</h3>
                            <p class="text-xs text-gray-500">Learn strategies to increase your sales</p>
                        </div>
                    </div>
                </div>
                <div class="mt-6">
                    <a href="{{ route('seller.documentation.show', 'account-setup') }}" class="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700">
                        Read Getting Started Guide
                    </a>
                </div>
            </div>
            <div class="hidden md:block">
                <div class="aspect-video rounded-lg bg-gradient-to-br from-indigo-50 to-purple-50 p-6">
                    <div class="flex h-full flex-col items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        <p class="mt-4 text-center text-sm text-gray-500">
                            Our comprehensive documentation will help you make the most of the Digitora platform
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Tutorials -->
    <div>
        <h2 class="mb-6 text-xl font-bold">Video Tutorials</h2>
        <div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            @foreach($videoTutorials as $video)
            <div class="rounded-lg border bg-white shadow-sm">
                <div class="aspect-video rounded-t-lg bg-gray-100">
                    <div class="flex h-full flex-col items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="mt-2 text-center text-sm text-gray-500">Video Preview</p>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="font-medium">{{ $video->title }}</h3>
                    <p class="mt-1 text-sm text-gray-500">{{ $video->description }}</p>
                    <div class="mt-4">
                        <a href="{{ $video->url }}" class="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                            Watch Video ({{ $video->duration }})
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="mt-6 text-center">
            <a href="#" class="text-sm font-medium text-indigo-600 hover:text-indigo-800">
                View All Video Tutorials →
            </a>
        </div>
    </div>
</div>
@endsection