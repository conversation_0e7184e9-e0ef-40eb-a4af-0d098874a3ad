<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('membership_tiers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name'); // Starter, Basic, Pro
            $table->string('slug')->unique(); // starter, basic, pro
            $table->decimal('price', 10, 2)->default(0); // Monthly price in IDR
            $table->integer('daily_ai_prompts'); // Daily AI prompt limit
            $table->boolean('unlimited_products')->default(false); // Unlimited product listings
            $table->boolean('ai_tools_access')->default(false); // AI description, SEO tools
            $table->integer('chatbot_products_limit')->default(0); // How many products can have chatbot
            $table->boolean('sales_analytics')->default(false); // Sales trend analysis
            $table->boolean('priority_support')->default(false); // Priority customer support
            $table->boolean('custom_listing')->default(false); // Video/image customization for Pro
            $table->integer('digital_product_transaction_fee')->default(5); // Digital product transaction fee percentage
            $table->integer('course_transaction_fee')->default(10); // Course transaction fee percentage
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('membership_tiers');
    }
};
