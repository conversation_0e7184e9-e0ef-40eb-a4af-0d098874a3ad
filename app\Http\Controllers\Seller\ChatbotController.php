<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductChatbotData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ChatbotController extends Controller
{
    /**
     * Show the chatbot configuration form for a product.
     */
    public function show(Product $product)
    {
        // Ensure the product belongs to the authenticated seller
        if ($product->seller_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this product.');
        }

        // Get or create chatbot data
        $chatbotData = $product->chatbotData ?: new ProductChatbotData();

        // Check if user can activate chatbot
        $canActivateChatbot = Auth::user()->canActivateChatbot();
        $currentTier = Auth::user()->getCurrentMembershipTier();

        // Get active chatbot count for this user
        $activeChatbotCount = Auth::user()->products()
            ->whereHas('chatbotData', function ($query) {
                $query->where('is_active', true);
            })
            ->count();

        return view('seller.products.chatbot', compact(
            'product',
            'chatbotData',
            'canActivateChatbot',
            'currentTier',
            'activeChatbotCount'
        ));
    }

    /**
     * Store or update chatbot configuration.
     */
    public function store(Request $request, Product $product)
    {
        try {
            // Ensure the product belongs to the authenticated seller
            if ($product->seller_id !== Auth::id()) {
                return response()->json(['error' => 'Unauthorized access to this product.'], 403);
            }

            $isActivating = $request->boolean('is_active');

            // Different validation rules for draft vs activation
            if ($isActivating) {
                // Strict validation for activation
                $validated = $request->validate([
                    'main_function' => 'required|string|max:1000',
                    'key_features' => 'required|string|max:1000',
                    'target_users' => 'required|string|max:1000',
                    'requirements' => 'required|string|max:1000',
                    'usage_instructions' => 'required|string|max:1000',
                    'unique_selling_points' => 'nullable|string|max:1000',
                    'limitations' => 'nullable|string|max:1000',
                    'troubleshooting' => 'nullable|string|max:1000',
                    'tips_optimization' => 'nullable|string|max:1000',
                    'language_format_support' => 'nullable|string|max:1000',
                    'common_questions' => 'nullable|string|max:2000',
                    'contact_info' => 'nullable|string|max:500',
                    'language' => 'required|in:id,en',
                ]);
            } else {
                // Relaxed validation for draft saving
                $validated = $request->validate([
                    'main_function' => 'nullable|string|max:1000',
                    'key_features' => 'nullable|string|max:1000',
                    'target_users' => 'nullable|string|max:1000',
                    'requirements' => 'nullable|string|max:1000',
                    'usage_instructions' => 'nullable|string|max:1000',
                    'unique_selling_points' => 'nullable|string|max:1000',
                    'limitations' => 'nullable|string|max:1000',
                    'troubleshooting' => 'nullable|string|max:1000',
                    'tips_optimization' => 'nullable|string|max:1000',
                    'language_format_support' => 'nullable|string|max:1000',
                    'common_questions' => 'nullable|string|max:2000',
                    'contact_info' => 'nullable|string|max:500',
                    'language' => 'nullable|in:id,en',
                ]);

                // Set default language if not provided
                if (empty($validated['language'])) {
                    $validated['language'] = 'id';
                }
            }

            // Manually handle is_active since FormData sends it as string
            $validated['is_active'] = $isActivating;
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'error' => 'Validation failed',
                'errors' => $e->errors(),
                'message' => 'Please check your input and try again.'
            ], 422);
        }

        // Check if user wants to activate chatbot
        if ($isActivating) {
            if (!Auth::user()->canActivateChatbot()) {
                return response()->json([
                    'error' => 'You have reached your chatbot limit for your current membership tier.',
                    'upgrade_needed' => true
                ], 403);
            }
        }

        // Get or create chatbot data
        $chatbotData = $product->chatbotData ?: new ProductChatbotData(['product_id' => $product->id]);

        // Update the data
        $chatbotData->fill($validated);
        $chatbotData->save();

        // Update product relationship if it's new
        if (!$product->chatbot_data_id) {
            $product->update(['chatbot_data_id' => $chatbotData->id]);
        }

        $message = $isActivating ? 'Chatbot configuration saved and activated successfully!' : 'Draft saved successfully!';

        return response()->json([
            'success' => true,
            'message' => $message,
            'completion_percentage' => $chatbotData->completion_percentage,
            'is_active' => $chatbotData->is_active,
            'has_required_data' => $chatbotData->hasRequiredData(),
        ]);
    }

    /**
     * Toggle chatbot activation status.
     */
    public function toggle(Request $request, Product $product)
    {
        // Ensure the product belongs to the authenticated seller
        if ($product->seller_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized access to this product.'], 403);
        }

        $chatbotData = $product->chatbotData;

        if (!$chatbotData) {
            return response()->json(['error' => 'No chatbot configuration found for this product.'], 404);
        }

        $newStatus = $request->boolean('is_active');

        // Check if user can activate chatbot
        if ($newStatus && !Auth::user()->canActivateChatbot()) {
            return response()->json([
                'error' => 'You have reached your chatbot limit for your current membership tier.',
                'upgrade_needed' => true
            ], 403);
        }

        // Check if required data is filled
        if ($newStatus && !$chatbotData->hasRequiredData()) {
            return response()->json([
                'error' => 'Please fill in all required fields before activating the chatbot.',
                'missing_required' => true
            ], 400);
        }

        $chatbotData->update(['is_active' => $newStatus]);

        return response()->json([
            'success' => true,
            'message' => $newStatus ? 'Chatbot activated successfully!' : 'Chatbot deactivated successfully!',
            'is_active' => $chatbotData->is_active,
        ]);
    }

    /**
     * Delete chatbot configuration.
     */
    public function destroy(Product $product)
    {
        // Ensure the product belongs to the authenticated seller
        if ($product->seller_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized access to this product.'], 403);
        }

        $chatbotData = $product->chatbotData;

        if (!$chatbotData) {
            return response()->json(['error' => 'No chatbot configuration found for this product.'], 404);
        }

        // Remove the relationship and delete the data
        $product->update(['chatbot_data_id' => null]);
        $chatbotData->delete();

        return response()->json([
            'success' => true,
            'message' => 'Chatbot configuration deleted successfully!',
        ]);
    }

    /**
     * Get chatbot status for a product (API endpoint).
     */
    public function status(Product $product)
    {
        // Ensure the product belongs to the authenticated seller
        if ($product->seller_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized access to this product.'], 403);
        }

        $chatbotData = $product->chatbotData;
        $canActivate = Auth::user()->canActivateChatbot();

        return response()->json([
            'has_chatbot' => (bool) $chatbotData,
            'is_active' => $chatbotData ? $chatbotData->is_active : false,
            'completion_percentage' => $chatbotData ? $chatbotData->completion_percentage : 0,
            'has_required_data' => $chatbotData ? $chatbotData->hasRequiredData() : false,
            'can_activate' => $canActivate,
            'current_tier' => Auth::user()->getCurrentMembershipTier()?->name,
        ]);
    }
}
