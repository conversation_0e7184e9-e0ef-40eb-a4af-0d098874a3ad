<?php

namespace App\Http\Controllers;

use App\Models\AiConversation;
use App\Models\AiMessage;
use App\Models\AiUsageLog;
use App\Models\Product;
use App\Models\ProductChatbotData;
use App\Services\GeminiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AiChatController extends Controller
{
    /**
     * Get or create a conversation for the current user/session.
     */
    public function getConversation(Request $request)
    {
        if (Auth::check()) {
            // For authenticated users
            $conversation = AiConversation::where('user_id', Auth::id())
                ->latest()
                ->first();

            if (!$conversation) {
                $conversation = AiConversation::create([
                    'user_id' => Auth::id(),
                    'title' => 'New Conversation',
                ]);
            }
        } else {
            // For non-authenticated users, use session ID
            $sessionId = $request->session()->get('ai_chat_session_id');

            if (!$sessionId) {
                $sessionId = Str::uuid()->toString();
                $request->session()->put('ai_chat_session_id', $sessionId);
            }

            $conversation = AiConversation::where('session_id', $sessionId)
                ->latest()
                ->first();

            if (!$conversation) {
                $conversation = AiConversation::create([
                    'session_id' => $sessionId,
                    'title' => 'New Conversation',
                ]);
            }
        }

        // Load the messages for this conversation
        $messages = $conversation->messages()->orderBy('created_at')->get();

        // If there are no messages, add a welcome message
        if ($messages->isEmpty()) {
            // Create a welcome message using Gemini
            $geminiService = new GeminiService();
            $welcomeMessage = $geminiService->generateResponse("You are an AI assistant for Digitora, a digital marketplace. Introduce yourself briefly and ask how you can help the user today.");

            $welcomeMsg = AiMessage::create([
                'conversation_id' => $conversation->id,
                'sender_type' => 'ai',
                'content' => $welcomeMessage,
            ]);

            $messages = collect([$welcomeMsg]);
        }

        return response()->json([
            'conversation' => $conversation,
            'messages' => $messages,
            'is_authenticated' => Auth::check(),
        ]);
    }

    /**
     * Send a message to the AI and get a response.
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|uuid|exists:ai_conversations,id',
            'message' => 'required|string',
            'page_context' => 'sometimes|array',
        ]);

        $conversation = AiConversation::findOrFail($request->conversation_id);

        // Check if the user is authorized to access this conversation
        if (Auth::check()) {
            if ($conversation->user_id && $conversation->user_id !== Auth::id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Check AI usage limits for authenticated users
            $user = Auth::user();
            if (!$user->canUseAi()) {
                $remainingPrompts = $user->getRemainingAiPrompts();
                $currentTier = $user->getCurrentMembershipTier();
                $isFreeTier = $currentTier && $currentTier->slug === 'starter';

                // Enhanced messaging for free tier users
                if ($isFreeTier) {
                    $message = $remainingPrompts === 0
                        ? 'Anda telah mencapai batas harian DigiAI (5 prompt/hari). Akses terbatas untuk pengguna gratis. Upgrade ke Basic (100 prompt/hari) atau Pro (unlimited) untuk pengalaman tanpa batas!'
                        : "Anda memiliki {$remainingPrompts} prompt tersisa hari ini. Sebagai pengguna gratis, akses DigiAI terbatas dan dapat mengalami throttling saat penggunaan tinggi.";
                } else {
                    $message = $remainingPrompts === 0
                        ? 'Anda telah mencapai batas harian penggunaan AI. Upgrade membership untuk mendapatkan lebih banyak prompt.'
                        : "Anda memiliki {$remainingPrompts} prompt tersisa hari ini.";
                }

                return response()->json([
                    'error' => 'Daily AI usage limit exceeded',
                    'message' => $message,
                    'remaining_prompts' => $remainingPrompts,
                    'current_tier' => $currentTier ? $currentTier->name : 'No membership',
                    'is_free_tier' => $isFreeTier,
                    'upgrade_needed' => true,
                    'upgrade_options' => [
                        'basic' => [
                            'name' => 'Basic',
                            'price' => 'Rp 79.000/bulan',
                            'prompts' => '100 prompt/hari',
                            'features' => '5 chatbot produk, analitik penjualan'
                        ],
                        'pro' => [
                            'name' => 'Pro',
                            'price' => 'Rp 99.000/bulan',
                            'prompts' => 'Unlimited prompts',
                            'features' => 'Unlimited chatbot, listing kustom'
                        ]
                    ]
                ], 429);
            }
        } else {
            $sessionId = $request->session()->get('ai_chat_session_id');
            if ($conversation->session_id !== $sessionId) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        }

        // Save the user's message
        $userMessage = AiMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'user',
            'content' => $request->message,
        ]);

        // Get previous messages for context (last 10 messages)
        $previousMessages = $conversation->messages()
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get()
            ->sortBy('created_at')
            ->toArray();

        // Get page context if provided
        $pageContext = $request->input('page_context', []);

        // Debug: Log the received page context
        Log::info('AI Chat - Received page context:', $pageContext);

        // Check if this is a product page with active chatbot
        $productChatbot = null;
        if (isset($pageContext['product_id'])) {
            $product = Product::with('chatbotData')->find($pageContext['product_id']);
            if ($product && $product->hasActiveChatbot()) {
                $productChatbot = $product->chatbotData;
                Log::info('AI Chat - Using product-specific chatbot for product: ' . $product->name);
            }
        }

        // Get membership data for authenticated users (always available for better AI context)
        $membershipData = null;
        if (Auth::check()) {
            $membershipData = $this->getMembershipData();
            Log::info('AI Chat - Fetching membership data for authenticated user');
        }

        // Generate AI response using Gemini with page context, product chatbot data, and membership data
        $geminiService = new GeminiService();
        $aiResponse = $geminiService->generateResponse($request->message, $previousMessages, $pageContext, $productChatbot, $membershipData);

        // Save the AI's response
        $aiMessage = AiMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'ai',
            'content' => $aiResponse,
        ]);

        // Track AI usage for authenticated users
        if (Auth::check()) {
            AiUsageLog::incrementUsage(
                Auth::id(),
                'chat',
                'AI Chat - Message: ' . substr($request->message, 0, 100)
            );
        }

        return response()->json([
            'user_message' => $userMessage,
            'ai_message' => $aiMessage,
        ]);
    }

    /**
     * Clear the conversation history.
     */
    public function clearConversation(Request $request)
    {
        $request->validate([
            'conversation_id' => 'required|uuid|exists:ai_conversations,id',
        ]);

        $conversation = AiConversation::findOrFail($request->conversation_id);

        // Check if the user is authorized to access this conversation
        if (Auth::check()) {
            if ($conversation->user_id && $conversation->user_id !== Auth::id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        } else {
            $sessionId = $request->session()->get('ai_chat_session_id');
            if ($conversation->session_id !== $sessionId) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        }

        // Delete all messages in the conversation
        $conversation->messages()->delete();

        // Create a welcome message using Gemini
        $geminiService = new GeminiService();
        $welcomeMessage = $geminiService->generateResponse("You are an AI assistant for Digitora, a digital marketplace. Introduce yourself briefly and ask how you can help the user today.");

        AiMessage::create([
            'conversation_id' => $conversation->id,
            'sender_type' => 'ai',
            'content' => $welcomeMessage,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Conversation cleared successfully',
        ]);
    }

    /**
     * Get membership data for AI context
     */
    private function getMembershipData()
    {
        if (!Auth::check()) {
            return null;
        }

        $user = Auth::user();
        $currentTier = $user->getCurrentMembershipTier();
        $todayUsage = \App\Models\AiUsageLog::getTotalTodayUsage($user->id);
        $remainingPrompts = $user->getRemainingAiPrompts();

        // Get all available tiers
        $allTiers = \App\Models\MembershipTier::active()->get();

        return [
            'current_tier' => $currentTier ? [
                'name' => $currentTier->name,
                'slug' => $currentTier->slug,
                'price' => $currentTier->price,
                'daily_ai_prompts' => $currentTier->daily_ai_prompts,
                'max_chatbot_products' => $currentTier->chatbot_products_limit,
                'transaction_fee' => $currentTier->transaction_fee,
                'unlimited_products' => $currentTier->unlimited_products,
                'ai_tools_access' => $currentTier->ai_tools_access,
                'sales_analytics' => $currentTier->sales_analytics,
                'priority_support' => $currentTier->priority_support,
                'custom_listing' => $currentTier->custom_listing,
                'formatted_price' => $currentTier->formatted_price
            ] : null,
            'today_usage' => $todayUsage,
            'remaining_prompts' => $remainingPrompts,
            'available_tiers' => $allTiers->map(function ($tier) {
                return [
                    'name' => $tier->name,
                    'slug' => $tier->slug,
                    'price' => $tier->price,
                    'daily_ai_prompts' => $tier->daily_ai_prompts,
                    'max_chatbot_products' => $tier->chatbot_products_limit,
                    'transaction_fee' => $tier->transaction_fee,
                    'unlimited_products' => $tier->unlimited_products,
                    'ai_tools_access' => $tier->ai_tools_access,
                    'sales_analytics' => $tier->sales_analytics,
                    'priority_support' => $tier->priority_support,
                    'custom_listing' => $tier->custom_listing,
                    'formatted_price' => $tier->formatted_price
                ];
            })->toArray()
        ];
    }
}
