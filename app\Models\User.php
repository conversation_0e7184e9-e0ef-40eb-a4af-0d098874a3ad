<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'google_id',
        'phone',
        'bio',
        'avatar',
        'notification_order',
        'notification_payment',
        'notification_product',
        'notification_marketing',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'notification_order' => 'boolean',
        'notification_payment' => 'boolean',
        'notification_product' => 'boolean',
        'notification_marketing' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            // If no password is set, generate a random secure password
            if (empty($user->password)) {
                $user->password = \Illuminate\Support\Facades\Hash::make(\Illuminate\Support\Str::random(16));
            }
        });

        static::created(function ($user) {
            // Assign default starter membership to new users
            $starterTier = \App\Models\MembershipTier::where('slug', 'starter')->first();
            if ($starterTier) {
                \App\Models\UserMembership::create([
                    'user_id' => $user->id,
                    'membership_tier_id' => $starterTier->id,
                    'started_at' => now(),
                    'status' => 'active',
                ]);
            }
        });
    }

    /**
     * Get the roles for this user.
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_roles')
                    ->withPivot(['assigned_at', 'expires_at', 'is_active'])
                    ->withTimestamps();
    }

    /**
     * Get the active roles for this user.
     */
    public function activeRoles(): BelongsToMany
    {
        return $this->roles()->wherePivot('is_active', true)
                           ->where(function ($query) {
                               $query->whereNull('user_roles.expires_at')
                                     ->orWhere('user_roles.expires_at', '>', now());
                           });
    }

    /**
     * Get the user role assignments.
     */
    public function userRoles(): HasMany
    {
        return $this->hasMany(UserRole::class);
    }

    /**
     * Check if the user has a specific role.
     */
    public function hasRole(string $roleSlug): bool
    {
        return $this->activeRoles()->where('slug', $roleSlug)->exists();
    }

    /**
     * Check if the user has any of the given roles.
     */
    public function hasAnyRole(array $roleSlugs): bool
    {
        return $this->activeRoles()->whereIn('slug', $roleSlugs)->exists();
    }

    /**
     * Check if the user has all of the given roles.
     */
    public function hasAllRoles(array $roleSlugs): bool
    {
        $userRoleSlugs = $this->activeRoles()->pluck('slug')->toArray();
        return empty(array_diff($roleSlugs, $userRoleSlugs));
    }

    /**
     * Check if the user is a seller
     */
    public function isSeller(): bool
    {
        return $this->hasRole('seller');
    }

    /**
     * Check if the user is an admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if the user is a superadmin
     */
    public function isSuperAdmin(): bool
    {
        return $this->hasRole('superadmin');
    }

    /**
     * Check if the user is a regular user (only has user role)
     */
    public function isRegularUser(): bool
    {
        $roles = $this->activeRoles()->pluck('slug')->toArray();
        return count($roles) === 1 && in_array('user', $roles);
    }

    /**
     * Assign a role to the user.
     */
    public function assignRole(string $roleSlug, ?\DateTime $expiresAt = null): void
    {
        $role = Role::findBySlug($roleSlug);
        if (!$role) {
            throw new \InvalidArgumentException("Role '{$roleSlug}' not found.");
        }

        // Check if user already has this role
        if (!$this->hasRole($roleSlug)) {
            UserRole::create([
                'user_id' => $this->id,
                'role_id' => $role->id,
                'assigned_at' => now(),
                'expires_at' => $expiresAt,
                'is_active' => true,
            ]);
        }
    }

    /**
     * Remove a role from the user.
     */
    public function removeRole(string $roleSlug): void
    {
        $role = Role::findBySlug($roleSlug);
        if ($role) {
            $this->userRoles()->where('role_id', $role->id)->delete();
        }
    }

    /**
     * Check if the user has a specific permission.
     */
    public function hasPermission(string $permissionSlug): bool
    {
        return $this->activeRoles()
                    ->whereHas('permissions', function ($query) use ($permissionSlug) {
                        $query->where('slug', $permissionSlug);
                    })
                    ->exists();
    }

    public function sellerApplication()
    {
        return $this->hasOne(SellerApplication::class);
    }

    // Accessor for store_name
    public function getStoreNameAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_name : null;
    }

    // Accessor for store_description
    public function getStoreDescriptionAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_description : null;
    }

    // Accessor for store_category
    public function getStoreCategoryAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_category : null;
    }

    // Accessor for store_logo
    public function getStoreLogoAttribute()
    {
        return $this->sellerApplication ? $this->sellerApplication->store_logo : null;
    }

    // social account
    public function socialAccounts()
    {
        return $this->hasMany(SocialAccount::class);
    }

    //products
    public function products()
    {
        return $this->hasMany(Product::class, 'seller_id', 'id');
    }

    //courses
    public function courses()
    {
        return $this->hasMany(Course::class, 'seller_id', 'id');
    }

    /**
     * Get all memberships for this user.
     */
    public function memberships()
    {
        return $this->hasMany(UserMembership::class);
    }

    /**
     * Get the current active membership.
     */
    public function currentMembership()
    {
        return $this->hasOne(UserMembership::class)->active()->latest();
    }

    /**
     * Get the current membership tier.
     */
    public function getCurrentMembershipTier()
    {
        $membership = $this->currentMembership;
        return $membership ? $membership->membershipTier : null;
    }

    /**
     * Check if user has a specific membership tier.
     */
    public function hasMembershipTier($tierSlug)
    {
        $currentTier = $this->getCurrentMembershipTier();
        return $currentTier && $currentTier->slug === $tierSlug;
    }

    /**
     * Get AI usage logs for this user.
     */
    public function aiUsageLogs()
    {
        return $this->hasMany(AiUsageLog::class);
    }

    /**
     * Get course progress records for this user.
     */
    public function courseProgress()
    {
        return $this->hasMany(UserCourseProgress::class);
    }

    /**
     * Get progress for a specific course.
     */
    public function getProgressForCourse($courseId)
    {
        return $this->courseProgress()->where('course_id', $courseId)->first();
    }

    /**
     * Get all completed courses for this user.
     */
    public function completedCourses()
    {
        return $this->courseProgress()->where('is_completed', true)->with('course');
    }

    /**
     * Get all in-progress courses for this user.
     */
    public function inProgressCourses()
    {
        return $this->courseProgress()->where('is_completed', false)->where('progress_percentage', '>', 0)->with('course');
    }

    /**
     * Check if user can use AI (within daily limit).
     */
    public function canUseAi($usageType = 'chat')
    {
        $currentTier = $this->getCurrentMembershipTier();
        if (!$currentTier) {
            return false;
        }

        return !AiUsageLog::hasExceededDailyLimit($this->id, $currentTier->daily_ai_prompts);
    }

    /**
     * Get remaining AI prompts for today.
     */
    public function getRemainingAiPrompts()
    {
        $currentTier = $this->getCurrentMembershipTier();
        if (!$currentTier) {
            return 0;
        }

        return AiUsageLog::getRemainingPrompts($this->id, $currentTier->daily_ai_prompts);
    }

    /**
     * Check if user can activate chatbot for products.
     */
    public function canActivateChatbot()
    {
        $currentTier = $this->getCurrentMembershipTier();
        if (!$currentTier) {
            return false;
        }

        // If unlimited, return true
        if ($currentTier->chatbot_products_limit === -1) {
            return true;
        }

        // Check if tier allows chatbot
        if ($currentTier->chatbot_products_limit <= 0) {
            return false;
        }

        // Check current usage
        $activeChatbots = $this->products()
            ->whereHas('chatbotData', function ($query) {
                $query->where('is_active', true);
            })
            ->count();

        return $activeChatbots < $currentTier->chatbot_products_limit;
    }
}
