<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Course;
use App\Models\Order;

class TestAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder creates test accounts with course purchases for testing purposes.
     */
    public function run(): void
    {
        $this->command->info('Creating test accounts for course purchase testing...');

        // Create test buyer users if they don't exist
        $testUsers = [
            [
                'email' => '<EMAIL>',
                'name' => 'Test Buyer 1',
                'courses_to_purchase' => ['complete-web-development-bootcamp', 'advanced-react-masterclass']
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'Test Buyer 2',
                'courses_to_purchase' => ['complete-web-development-bootcamp']
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'Test Buyer 3',
                'courses_to_purchase' => ['python-data-science-fundamentals']
            ],
            [
                'email' => '<EMAIL>',
                'name' => 'Test Buyer 4 (No Purchases)',
                'courses_to_purchase' => []
            ]
        ];

        foreach ($testUsers as $userData) {
            $user = User::firstOrCreate([
                'email' => $userData['email']
            ], [
                'id' => (string) Str::uuid(),
                'name' => $userData['name'],
                'password' => Hash::make('test123'),
                'email_verified_at' => now(),
            ]);

            // Create successful orders for specified courses
            foreach ($userData['courses_to_purchase'] as $courseSlug) {
                $course = Course::where('slug', $courseSlug)->first();
                
                if ($course) {
                    // Check if order already exists
                    $existingOrder = Order::where('buyer_id', $user->id)
                        ->where('course_id', $course->id)
                        ->where('status', 'success')
                        ->first();

                    if (!$existingOrder) {
                        $amount = $course->discount_price > 0 ? $course->discount_price : $course->price;
                        
                        Order::create([
                            'id' => (string) Str::uuid(),
                            'order_id' => 'TEST-' . strtoupper(Str::random(8)) . '-' . date('Ymd'),
                            'buyer_id' => $user->id,
                            'seller_id' => $course->seller_id,
                            'course_id' => $course->id,
                            'amount' => $amount,
                            'status' => 'success',
                            'payment_method' => 'test_simulation',
                            'snap_token' => null,
                            'created_at' => now()->subDays(rand(1, 7)),
                            'updated_at' => now()->subDays(rand(1, 7)),
                        ]);

                        $this->command->info("✓ Created purchase: {$user->name} → {$course->title}");
                    } else {
                        $this->command->info("- Purchase already exists: {$user->name} → {$course->title}");
                    }
                } else {
                    $this->command->warn("Course not found: {$courseSlug}");
                }
            }
        }

        $this->command->info('');
        $this->command->info('🎉 Test accounts created successfully!');
        $this->command->info('');
        $this->command->info('📋 TEST ACCOUNT CREDENTIALS:');
        $this->command->info('============================');
        $this->command->info('');
        
        foreach ($testUsers as $userData) {
            $this->command->info("👤 {$userData['name']}");
            $this->command->info("   Email: {$userData['email']}");
            $this->command->info("   Password: test123");
            
            if (!empty($userData['courses_to_purchase'])) {
                $this->command->info("   Purchased Courses:");
                foreach ($userData['courses_to_purchase'] as $courseSlug) {
                    $course = Course::where('slug', $courseSlug)->first();
                    if ($course) {
                        $this->command->info("   - {$course->title}");
                    }
                }
            } else {
                $this->command->info("   Purchased Courses: None (for testing access denied)");
            }
            $this->command->info('');
        }

        $this->command->info('🔗 TESTING URLS:');
        $this->command->info('================');
        $this->command->info('Login Page: http://digitora.test/login');
        $this->command->info('Course Browse: http://digitora.test/browse/courses');
        $this->command->info('');
        
        $courses = Course::whereIn('slug', [
            'complete-web-development-bootcamp', 
            'advanced-react-masterclass', 
            'python-data-science-fundamentals'
        ])->get();

        foreach ($courses as $course) {
            $this->command->info("Course: {$course->title}");
            $this->command->info("  Detail: http://digitora.test/browse/courses/{$course->slug}");
            $this->command->info("  Access: http://digitora.test/browse/courses/{$course->slug}/access");
            $this->command->info('');
        }

        $this->command->info('💡 TESTING INSTRUCTIONS:');
        $this->command->info('=========================');
        $this->command->info('1. Login with any test account above');
        $this->command->info('2. Visit course access URLs to test the new UX');
        $this->command->info('3. Accounts with purchases will see access granted page');
        $this->command->info('4. Accounts without purchases will see access denied page');
        $this->command->info('5. Both pages have 10-second countdown timers');
        $this->command->info('6. Click anywhere to cancel the countdown');
    }
}
