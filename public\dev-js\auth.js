/**
 * Authentication Pages JavaScript
 * Handles password visibility toggle and form animations
 */

document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const passwordFields = document.querySelectorAll('.password-field input');
    const toggleButtons = document.querySelectorAll('.password-toggle');

    toggleButtons.forEach((button, index) => {
        button.addEventListener('click', function() {
            const field = passwordFields[index];
            const type = field.getAttribute('type');

            // Toggle password visibility
            if (type === 'password') {
                field.setAttribute('type', 'text');
                this.querySelector('.eye-icon').classList.add('hidden');
                this.querySelector('.eye-off-icon').classList.remove('hidden');
            } else {
                field.setAttribute('type', 'password');
                this.querySelector('.eye-icon').classList.remove('hidden');
                this.querySelector('.eye-off-icon').classList.add('hidden');
            }
        });
    });

    // Form animations
    const authCard = document.querySelector('.auth-card');
    if (authCard) {
        // Add a subtle entrance animation
        authCard.style.opacity = '0';
        authCard.style.transform = 'translateY(10px)';

        setTimeout(() => {
            authCard.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            authCard.style.opacity = '1';
            authCard.style.transform = 'translateY(0)';
        }, 100);
    }

    // Form validation
    const authForms = document.querySelectorAll('.auth-form');

    authForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');

                    // Add shake animation for invalid fields
                    field.classList.add('shake-animation');
                    setTimeout(() => {
                        field.classList.remove('shake-animation');
                    }, 500);
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            // Password confirmation validation
            const password = form.querySelector('#password');
            const confirmPassword = form.querySelector('#password_confirmation');

            if (password && confirmPassword && password.value !== confirmPassword.value) {
                isValid = false;
                confirmPassword.classList.add('border-red-500');

                // Create or update error message
                let errorMsg = form.querySelector('.password-match-error');
                if (!errorMsg) {
                    errorMsg = document.createElement('p');
                    errorMsg.className = 'text-red-500 text-xs mt-1 password-match-error';
                    confirmPassword.parentNode.appendChild(errorMsg);
                }
                errorMsg.textContent = 'Passwords do not match';
            }

            if (!isValid) {
                e.preventDefault();
            }
        });

        // Real-time validation feedback
        const inputs = form.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                if (this.hasAttribute('required') && this.value.trim()) {
                    this.classList.remove('border-red-500');
                }

                // Clear password match error when typing in either password field
                if (this.id === 'password' || this.id === 'password_confirmation') {
                    const errorMsg = form.querySelector('.password-match-error');
                    if (errorMsg) errorMsg.textContent = '';
                }
            });
        });
    });
});

// Add CSS for shake animation
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    .shake-animation {
        animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
    }

    .hidden {
        display: none;
    }
`;
document.head.appendChild(style);
