<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ProductCategoryController extends Controller
{
    /**
     * Display a listing of the categories.
     */
    public function index()
    {
        $categories = ProductCategory::topLevel()->with('children')->orderBy('sort_order')->get();
        return view('admin.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        $categories = ProductCategory::orderBy('name')->get();
        return view('admin.categories.create', compact('categories'));
    }

    /**
     * Store a newly created category in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:50',
            'parent_id' => 'nullable|exists:product_categories,id',
            'is_active' => 'boolean',
        ]);

        // Generate slug
        $slug = Str::slug($validated['name']);
        $originalSlug = $slug;
        $counter = 1;

        // Ensure slug is unique
        while (ProductCategory::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        // Calculate level
        $level = 1;
        if (!empty($validated['parent_id'])) {
            $parent = ProductCategory::find($validated['parent_id']);
            $level = $parent ? $parent->level + 1 : 1;
        }

        // Create category
        ProductCategory::create([
            'name' => $validated['name'],
            'slug' => $slug,
            'description' => $validated['description'] ?? null,
            'icon' => $validated['icon'] ?? null,
            'parent_id' => $validated['parent_id'] ?? null,
            'level' => $level,
            'is_active' => $validated['is_active'] ?? true,
            'sort_order' => ProductCategory::where('parent_id', $validated['parent_id'])->max('sort_order') + 1,
        ]);

        return redirect()->route('admin.categories.index')->with('success', 'Category created successfully.');
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(ProductCategory $category)
    {
        $categories = ProductCategory::where('id', '!=', $category->id)
            ->whereNotIn('id', $this->getAllChildrenIds($category))
            ->orderBy('name')
            ->get();
            
        return view('admin.categories.edit', compact('category', 'categories'));
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, ProductCategory $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:50',
            'parent_id' => 'nullable|exists:product_categories,id',
            'is_active' => 'boolean',
        ]);

        // Ensure parent is not itself or one of its children
        if (!empty($validated['parent_id'])) {
            if ($validated['parent_id'] == $category->id) {
                return back()->withErrors(['parent_id' => 'A category cannot be its own parent.']);
            }

            $childrenIds = $this->getAllChildrenIds($category);
            if (in_array($validated['parent_id'], $childrenIds)) {
                return back()->withErrors(['parent_id' => 'A category cannot have one of its children as its parent.']);
            }
        }

        // Calculate level
        $level = 1;
        if (!empty($validated['parent_id'])) {
            $parent = ProductCategory::find($validated['parent_id']);
            $level = $parent ? $parent->level + 1 : 1;
        }

        // Update category
        $category->update([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'icon' => $validated['icon'] ?? null,
            'parent_id' => $validated['parent_id'] ?? null,
            'level' => $level,
            'is_active' => $validated['is_active'] ?? true,
        ]);

        // Update slug only if name has changed
        if ($category->wasChanged('name')) {
            $slug = Str::slug($validated['name']);
            $originalSlug = $slug;
            $counter = 1;

            // Ensure slug is unique
            while (ProductCategory::where('slug', $slug)->where('id', '!=', $category->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $category->update(['slug' => $slug]);
        }

        return redirect()->route('admin.categories.index')->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category from storage.
     */
    public function destroy(ProductCategory $category)
    {
        // Delete will cascade to children due to foreign key constraint
        $category->delete();
        return redirect()->route('admin.categories.index')->with('success', 'Category deleted successfully.');
    }

    /**
     * Update the sort order of categories.
     */
    public function updateOrder(Request $request)
    {
        $validated = $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:product_categories,id',
            'categories.*.order' => 'required|integer|min:0',
        ]);

        foreach ($validated['categories'] as $item) {
            ProductCategory::where('id', $item['id'])->update(['sort_order' => $item['order']]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Get all children IDs recursively.
     */
    private function getAllChildrenIds(ProductCategory $category, array &$ids = [])
    {
        foreach ($category->children as $child) {
            $ids[] = $child->id;
            $this->getAllChildrenIds($child, $ids);
        }
        
        return $ids;
    }
}
