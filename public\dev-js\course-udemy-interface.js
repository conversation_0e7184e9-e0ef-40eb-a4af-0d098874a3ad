/**
 * Udemy-Style Course Management Interface
 * Clean, professional design with non-floating elements
 * Integrated add buttons and contextual placement
 */

class UdemyCourseInterface {
    constructor(options) {
        this.courseId = options.courseId;
        this.csrfToken = options.csrfToken;
        this.courseData = options.courseData;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupGlobalFunctions();
        console.log('Udemy Course Interface initialized');
    }

    bindEvents() {
        // Section management
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-action]');
            if (!target) return;

            const action = target.dataset.action;
            const sectionId = target.dataset.sectionId;
            const itemId = target.dataset.itemId;

            switch (action) {
                case 'add-section':
                    this.showAddSectionForm();
                    break;
                case 'edit-section':
                    this.showEditSectionForm(sectionId);
                    break;
                case 'delete-section':
                    this.deleteSection(sectionId);
                    break;
                case 'add-curriculum-item':
                    this.showAddCurriculumItemForm(sectionId);
                    break;
                case 'edit-curriculum-item':
                    this.showEditCurriculumItemForm(itemId);
                    break;
                case 'delete-curriculum-item':
                    this.deleteCurriculumItem(itemId);
                    break;
                case 'cancel-form':
                    this.cancelForm(target);
                    break;
                case 'save-section':
                    this.saveSection(target);
                    break;
                case 'save-curriculum-item':
                    this.saveCurriculumItem(target);
                    break;
            }
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('inline-form')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="checkbox"][data-toggle-type]')) {
                this.handleStatusToggle(e.target);
            }
        });
    }

    showAddSectionForm() {
        const container = document.querySelector('.add-section-container');
        if (!container) return;

        const formHtml = `
            <div class="inline-form-container">
                <form class="inline-form" data-type="section">
                    <div class="form-group">
                        <label class="form-label">Section Title</label>
                        <input type="text" name="title" class="form-input" required placeholder="Enter section title">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description (Optional)</label>
                        <textarea name="description" class="form-textarea" placeholder="Enter section description"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select name="is_active" class="form-select">
                            <option value="1" selected>Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" data-action="cancel-form">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Section</button>
                    </div>
                </form>
            </div>
        `;

        container.innerHTML = formHtml;
        container.querySelector('input[name="title"]').focus();
    }

    showEditSectionForm(sectionId) {
        const sectionContainer = document.querySelector(`[data-section-id="${sectionId}"]`);
        if (!sectionContainer) return;

        // Find section data
        const section = this.findSectionById(sectionId);
        if (!section) return;

        const formHtml = `
            <div class="inline-form-container">
                <form class="inline-form" data-type="section" data-section-id="${sectionId}">
                    <div class="form-group">
                        <label class="form-label">Section Title</label>
                        <input type="text" name="title" class="form-input" required value="${section.title}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description (Optional)</label>
                        <textarea name="description" class="form-textarea">${section.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select name="is_active" class="form-select">
                            <option value="1" ${section.is_active ? 'selected' : ''}>Active</option>
                            <option value="0" ${!section.is_active ? 'selected' : ''}>Inactive</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" data-action="cancel-form">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Section</button>
                    </div>
                </form>
            </div>
        `;

        // Insert form after section header
        const sectionHeader = sectionContainer.querySelector('.section-header');
        sectionHeader.insertAdjacentHTML('afterend', formHtml);
        sectionContainer.querySelector('input[name="title"]').focus();
    }

    showAddCurriculumItemForm(sectionId) {
        const addContainer = document.querySelector(`[data-section-id="${sectionId}"] .add-curriculum-item-container`);
        if (!addContainer) return;

        const formHtml = `
            <div class="inline-form-container">
                <form class="inline-form" data-type="curriculum-item" data-section-id="${sectionId}">
                    <div class="form-group">
                        <label class="form-label">Title</label>
                        <input type="text" name="title" class="form-input" required placeholder="Enter curriculum item title">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Type</label>
                        <select name="type" class="form-select" required>
                            <option value="">Select type</option>
                            <option value="lecture">Lecture</option>
                            <option value="video">Video</option>
                            <option value="pdf">PDF Document</option>
                            <option value="document">Document</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Content/URL</label>
                        <textarea name="content" class="form-textarea" placeholder="Enter content or URL (required for lecture/video types)"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">File (for PDF/Document types)</label>
                        <input type="file" name="file" class="form-input" accept=".pdf,.doc,.docx,.txt,.zip">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description (Optional)</label>
                        <textarea name="description" class="form-textarea" placeholder="Enter description"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Duration (minutes)</label>
                        <input type="number" name="estimated_duration" class="form-input" min="1" placeholder="Enter duration in minutes">
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="is_preview"> Allow preview
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select name="is_active" class="form-select">
                            <option value="1" selected>Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" data-action="cancel-form">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Curriculum Item</button>
                    </div>
                </form>
            </div>
        `;

        addContainer.innerHTML = formHtml;
        addContainer.querySelector('input[name="title"]').focus();
    }

    showEditCurriculumItemForm(itemId) {
        const itemContainer = document.querySelector(`[data-item-id="${itemId}"]`);
        if (!itemContainer) return;

        // Find item data
        const item = this.findCurriculumItemById(itemId);
        if (!item) return;

        const formHtml = `
            <div class="inline-form-container">
                <form class="inline-form" data-type="curriculum-item" data-item-id="${itemId}" data-section-id="${item.section_id}">
                    <div class="form-group">
                        <label class="form-label">Title</label>
                        <input type="text" name="title" class="form-input" required value="${item.title}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Type</label>
                        <select name="type" class="form-select" required>
                            <option value="lecture" ${item.type === 'lecture' ? 'selected' : ''}>Lecture</option>
                            <option value="video" ${item.type === 'video' ? 'selected' : ''}>Video</option>
                            <option value="pdf" ${item.type === 'pdf' ? 'selected' : ''}>PDF Document</option>
                            <option value="document" ${item.type === 'document' ? 'selected' : ''}>Document</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Content/URL</label>
                        <textarea name="content" class="form-textarea">${item.content || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">File (for PDF/Document types)</label>
                        <input type="file" name="file" class="form-input" accept=".pdf,.doc,.docx,.txt,.zip">
                        ${item.file_path ? `<p class="text-sm text-gray-600 mt-1">Current file: ${item.metadata?.original_name || 'File attached'}</p>` : ''}
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description (Optional)</label>
                        <textarea name="description" class="form-textarea">${item.description || ''}</textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Duration (minutes)</label>
                        <input type="number" name="estimated_duration" class="form-input" min="1" value="${item.estimated_duration || ''}">
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="is_preview" ${item.is_preview ? 'checked' : ''}> Allow preview
                        </label>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select name="is_active" class="form-select">
                            <option value="1" ${item.is_active !== false ? 'selected' : ''}>Active</option>
                            <option value="0" ${item.is_active === false ? 'selected' : ''}>Inactive</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" data-action="cancel-form">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Curriculum Item</button>
                    </div>
                </form>
            </div>
        `;

        // Insert form after the item
        itemContainer.insertAdjacentHTML('afterend', formHtml);
        itemContainer.nextElementSibling.querySelector('input[name="title"]').focus();
    }

    cancelForm(button) {
        const formContainer = button.closest('.inline-form-container');
        if (formContainer) {
            // If this is an add section form, restore the add button
            if (formContainer.parentElement.classList.contains('add-section-container')) {
                formContainer.parentElement.innerHTML = `
                    <button class="add-section-btn" data-action="add-section">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Section
                    </button>
                `;
            } else if (formContainer.parentElement.classList.contains('add-curriculum-item-container')) {
                // Restore add curriculum item button
                formContainer.parentElement.innerHTML = `
                    <button class="add-curriculum-item-btn" data-action="add-curriculum-item" data-section-id="${formContainer.querySelector('form').dataset.sectionId}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Curriculum Item
                    </button>
                `;
            } else {
                // Just remove the form
                formContainer.remove();
            }
        }
    }

    async handleFormSubmit(form) {
        const formData = new FormData(form);
        const type = form.dataset.type;
        const sectionId = form.dataset.sectionId;
        const itemId = form.dataset.itemId;

        // Add method spoofing for PUT requests ONLY for updates (when itemId exists for curriculum items or sectionId exists for sections)
        const isUpdate = (type === 'section' && sectionId) || (type === 'curriculum-item' && itemId);
        if (isUpdate) {
            formData.append('_method', 'PUT');
        }

        try {
            let url, method = 'POST'; // Always use POST with method spoofing

            if (type === 'section') {
                if (sectionId) {
                    // Update section
                    url = `/seller/courses/${this.courseId}/sections/${sectionId}`;
                } else {
                    // Create section
                    url = `/seller/courses/${this.courseId}/sections`;
                }
            } else if (type === 'curriculum-item') {
                if (itemId) {
                    // Update curriculum item
                    url = `/seller/courses/${this.courseId}/sections/${sectionId}/curriculum-items/${itemId}`;
                } else {
                    // Create curriculum item
                    url = `/seller/courses/${this.courseId}/sections/${sectionId}/curriculum-items`;
                }
            }

            console.log('Submitting form:', { url, type, sectionId, itemId });

            const response = await fetch(url, {
                method: method,
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Accept': 'application/json',
                },
                body: formData
            });

            console.log('Response status:', response.status);

            if (!response.ok) {
                let errorMessage = 'An error occurred while saving';

                try {
                    const errorData = await response.json();
                    if (errorData.message) {
                        errorMessage = errorData.message;
                    } else if (errorData.errors) {
                        // Handle validation errors
                        const firstError = Object.values(errorData.errors)[0];
                        errorMessage = Array.isArray(firstError) ? firstError[0] : firstError;
                    }
                } catch (parseError) {
                    // If JSON parsing fails, try to get text
                    try {
                        const errorText = await response.text();
                        if (response.status === 405) {
                            errorMessage = 'Invalid request method. Please refresh the page and try again.';
                        } else if (response.status === 404) {
                            errorMessage = 'The requested resource was not found. Please refresh the page.';
                        } else if (response.status === 403) {
                            errorMessage = 'You do not have permission to perform this action.';
                        } else if (response.status === 422) {
                            errorMessage = 'Please check your input and try again.';
                        } else if (response.status >= 500) {
                            errorMessage = 'Server error occurred. Please try again later.';
                        } else {
                            errorMessage = `Error ${response.status}: ${errorText.substring(0, 100)}`;
                        }
                    } catch (textError) {
                        errorMessage = `Error ${response.status}: Unable to process request`;
                    }
                }

                console.error('Response error:', errorMessage);
                this.showNotification(errorMessage, 'error');
                return;
            }

            const result = await response.json();
            console.log('Response result:', result);

            if (result.success) {
                this.showNotification('Saved successfully!', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                this.showNotification(result.message || 'Error saving changes', 'error');
                console.error('Save failed:', result);
            }
        } catch (error) {
            console.error('Error:', error);
            let userMessage = 'Unable to save changes. Please check your connection and try again.';

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                userMessage = 'Network error. Please check your internet connection.';
            } else if (error.message.includes('JSON')) {
                userMessage = 'Server response error. Please try again.';
            }

            this.showNotification(userMessage, 'error');
        }
    }

    async deleteSection(sectionId) {
        if (!confirm('Are you sure you want to delete this section? This will also delete all curriculum items in this section.')) {
            return;
        }

        try {
            const response = await fetch(`/seller/courses/${this.courseId}/sections/${sectionId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Accept': 'application/json',
                }
            });

            if (!response.ok) {
                let errorMessage = 'Failed to delete section';
                if (response.status === 403) {
                    errorMessage = 'You do not have permission to delete this section.';
                } else if (response.status === 404) {
                    errorMessage = 'Section not found. It may have already been deleted.';
                }
                this.showNotification(errorMessage, 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                this.showNotification('Section deleted successfully!', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                this.showNotification(result.message || 'Error deleting section', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification('Unable to delete section. Please try again.', 'error');
        }
    }

    async deleteCurriculumItem(itemId) {
        if (!confirm('Are you sure you want to delete this curriculum item?')) {
            return;
        }

        const item = this.findCurriculumItemById(itemId);
        if (!item) return;

        try {
            const response = await fetch(`/seller/courses/${this.courseId}/sections/${item.section_id}/curriculum-items/${itemId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Accept': 'application/json',
                }
            });

            if (!response.ok) {
                let errorMessage = 'Failed to delete curriculum item';
                if (response.status === 403) {
                    errorMessage = 'You do not have permission to delete this curriculum item.';
                } else if (response.status === 404) {
                    errorMessage = 'Curriculum item not found. It may have already been deleted.';
                }
                this.showNotification(errorMessage, 'error');
                return;
            }

            const result = await response.json();

            if (result.success) {
                this.showNotification('Curriculum item deleted successfully!', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                this.showNotification(result.message || 'Error deleting curriculum item', 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            this.showNotification('Unable to delete curriculum item. Please try again.', 'error');
        }
    }

    findSectionById(sectionId) {
        return this.courseData.sections?.find(section => section.id === sectionId);
    }

    findCurriculumItemById(itemId) {
        for (const section of this.courseData.sections || []) {
            const item = section.curriculum_items?.find(item => item.id === itemId);
            if (item) return item;
        }
        return null;
    }

    showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    setupGlobalFunctions() {
        // Backward compatibility
        window.editSection = (sectionId) => this.showEditSectionForm(sectionId);
        window.deleteSection = (sectionId) => this.deleteSection(sectionId);
        window.editCurriculumItem = (itemId) => this.showEditCurriculumItemForm(itemId);
        window.deleteCurriculumItem = (itemId) => this.deleteCurriculumItem(itemId);
    }

    async handleStatusToggle(toggle) {
        const type = toggle.dataset.toggleType;
        const id = toggle.dataset.id;
        const courseId = toggle.dataset.courseId;
        const isActive = toggle.checked;

        let url;
        switch(type) {
            case 'section':
                url = `/seller/courses/${courseId}/sections/${id}/toggle-status`;
                break;
            case 'curriculum-item':
                url = `/seller/courses/${courseId}/sections/${toggle.dataset.sectionId}/curriculum-items/${id}/toggle-status`;
                break;
            case 'course':
                url = `/seller/courses/${courseId}/toggle-status`;
                break;
            default:
                console.error('Unknown toggle type:', type);
                return;
        }

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': this.csrfToken
                },
                body: JSON.stringify({ status: isActive ? 1 : 0 })
            });

            if (!response.ok) throw new Error('Failed to update status');

            const data = await response.json();
            this.showNotification('Status updated successfully', 'success');

            // Update UI if needed, e.g., status badge
            const statusBadge = toggle.closest('.status-info')?.querySelector('.status-badge') || toggle.closest('.section-status')?.querySelector('.status-badge') || toggle.closest('.item-meta')?.querySelector('.item-status');
            if (statusBadge) {
                statusBadge.className = `status-badge status-${data.status}`;
                statusBadge.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
            }

            // Update publication date if applicable
            if (type === 'course' && data.published_at) {
                const dateElement = toggle.closest('.status-info')?.querySelector('.publication-date');
                if (dateElement) {
                    dateElement.textContent = `Published: ${data.published_at}`;
                }
            }
        } catch (error) {
            console.error('Error updating status:', error);
            toggle.checked = !isActive; // Revert toggle
            this.showNotification('Failed to update status', 'error');
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.courseInterfaceConfig) {
        window.courseInterface = new UdemyCourseInterface(window.courseInterfaceConfig);
    }
});
