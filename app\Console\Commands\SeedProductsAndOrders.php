<?php

namespace App\Console\Commands;

use Database\Seeders\OrderSeeder;
use Database\Seeders\ProductSeeder;
use Illuminate\Console\Command;

class SeedProductsAndOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:products-orders {--refresh : Truncate the products and orders tables before seeding}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed products and orders for testing purposes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('refresh')) {
            $this->info('Refreshing products and orders tables...');
            
            // Truncate orders first (due to foreign key constraints)
            $this->call('db:table:truncate', [
                'table' => 'payments',
            ]);
            
            $this->call('db:table:truncate', [
                'table' => 'orders',
            ]);
            
            $this->call('db:table:truncate', [
                'table' => 'products',
            ]);
            
            $this->info('Tables truncated successfully.');
        }
        
        $this->info('Seeding products...');
        $this->call('db:seed', [
            '--class' => ProductSeeder::class,
        ]);
        
        $this->info('Seeding orders...');
        $this->call('db:seed', [
            '--class' => OrderSeeder::class,
        ]);
        
        $this->info('Products and orders seeded successfully!');
    }
}
