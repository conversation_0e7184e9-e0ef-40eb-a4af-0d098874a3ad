<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'seller_id',
        'name',
        'slug',
        'description',
        'category', // Legacy category field
        'category_id',
        'subcategory_id',
        'detailed_category_id',
        'chatbot_data_id',
        'price',
        'discount_price',
        'status',
        'image',
        'files',
        'is_featured',
        'average_rating',
        'reviews_count',
        'content_type', // New field for simple vs course products
        'product_type', // New field for ebook, digital, course categories
    ];

    protected $casts = [
        'price' => 'integer', // Changed from decimal:2 to integer for Midtrans compatibility
        'discount_price' => 'integer', // Changed from decimal:2 to integer for Midtrans compatibility
    ];

    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    // seller application
    public function sellerApplication()
    {
        return $this->belongsTo(SellerApplication::class, 'seller_id', 'user_id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    public function images()
    {
        return $this->hasMany(ProductImage::class)->orderBy('sort_order');
    }

    public function primaryImage()
    {
        return $this->hasOne(ProductImage::class)->where('is_primary', true);
    }

    /**
     * Get the resources for this product
     */
    public function resources()
    {
        return $this->hasMany(ProductResource::class)->orderBy('sort_order');
    }

    /**
     * Get only active resources
     */
    public function activeResources()
    {
        return $this->hasMany(ProductResource::class)->active()->ordered();
    }

    /**
     * Get preview resources (accessible before purchase)
     */
    public function previewResources()
    {
        return $this->hasMany(ProductResource::class)->preview()->active()->ordered();
    }

    public function getDiscountPercentageAttribute()
    {
        if (!$this->discount_price || $this->price <= 0) {
            return 0;
        }

        return round((($this->price - $this->discount_price) / $this->price) * 100);
    }

    /**
     * Get the category that owns the product.
     */
    public function productCategory()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the subcategory that owns the product.
     */
    public function productSubcategory()
    {
        return $this->belongsTo(ProductSubcategory::class, 'subcategory_id');
    }

    /**
     * Get the detailed category that owns the product.
     */
    public function productDetailedCategory()
    {
        return $this->belongsTo(ProductDetailedCategory::class, 'detailed_category_id');
    }

    /**
     * Get the category name from the legacy category field or the related category.
     */
    public function getCategoryNameAttribute()
    {
        if ($this->productCategory) {
            return $this->productCategory->name;
        }

        return ucfirst($this->category);
    }

    /**
     * Get the subcategory name.
     */
    public function getSubcategoryNameAttribute()
    {
        if ($this->productSubcategory) {
            return $this->productSubcategory->name;
        }

        return null;
    }

    /**
     * Get the detailed category name.
     */
    public function getDetailedCategoryNameAttribute()
    {
        if ($this->productDetailedCategory) {
            return $this->productDetailedCategory->name;
        }

        return null;
    }

    /**
     * Get the full category path for breadcrumbs.
     */
    public function getCategoryPathAttribute()
    {
        if ($this->productDetailedCategory) {
            return $this->productDetailedCategory->path;
        }

        if ($this->productSubcategory) {
            return $this->productSubcategory->path;
        }

        if ($this->productCategory) {
            return $this->productCategory->name;
        }

        return ucfirst($this->category);
    }

    /**
     * Alias for productCategory relationship.
     */
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Alias for productSubcategory relationship.
     */
    public function subcategory()
    {
        return $this->belongsTo(ProductSubcategory::class, 'subcategory_id');
    }

    /**
     * Alias for productDetailedCategory relationship.
     */
    public function detailedCategory()
    {
        return $this->belongsTo(ProductDetailedCategory::class, 'detailed_category_id');
    }

    /**
     * Get the chatbot data for this product.
     */
    public function chatbotData()
    {
        return $this->hasOne(ProductChatbotData::class);
    }

    /**
     * Check if product has active chatbot.
     */
    public function hasActiveChatbot()
    {
        return $this->chatbotData && $this->chatbotData->is_active && $this->chatbotData->hasRequiredData();
    }

    /**
     * Check if product can have chatbot activated (seller has permission).
     */
    public function canActivateChatbot()
    {
        return $this->seller && $this->seller->canActivateChatbot();
    }

    /**
     * Check if this is a course-type product
     */
    public function isCourse(): bool
    {
        return $this->content_type === 'course';
    }

    /**
     * Check if this is a simple product
     */
    public function isSimple(): bool
    {
        return $this->content_type === 'simple';
    }

    /**
     * Get the total number of resources
     */
    public function getResourceCountAttribute(): int
    {
        return $this->resources()->count();
    }

    /**
     * Get the total number of active resources
     */
    public function getActiveResourceCountAttribute(): int
    {
        return $this->activeResources()->count();
    }

    /**
     * Check if product has any resources
     */
    public function hasResources(): bool
    {
        return $this->resources()->exists();
    }

    /**
     * Check if product has legacy files (for backward compatibility)
     */
    public function hasLegacyFiles(): bool
    {
        $files = json_decode($this->files, true);
        return !empty($files);
    }
}
