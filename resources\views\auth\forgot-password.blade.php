<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Digitora</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/digitora-logo.png') }}">
    <link href="{{ asset('css/auth.css') }}" rel="stylesheet">
</head>

<body class=" auth-page min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-white flex flex-col">
    <div class="container mx-auto px-4 py-4">
        <a href="{{ route('login') }}" class="back-link">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            <span>Back to Login</span>
        </a>
    </div>

    <div class="flex-1 flex items-center justify-center px-4 py-12">
        <div class="w-full max-w-md border-0 shadow-lg bg-white rounded-lg">
            <div class="space-y-1 p-6">
                <div class="flex justify-center mb-2">
                    <div
                        class="flex h-10 w-10 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                        <span class="text-xl font-bold">D</span>
                    </div>
                </div>
                <h2 class="text-2xl font-bold text-center">Forgot your password?</h2>
                <p class="text-center text-gray-600">
                    @if (session('status'))
                        Check your email for a reset link
                    @else
                        Enter your email and we'll send you a link to reset your password
                    @endif
                </p>
            </div>

            <!-- Error Messages -->
            @if ($errors->any())
                <div class="px-6 py-3 bg-red-50 border-t border-b border-red-200">
                    @foreach ($errors->all() as $error)
                        <p class="text-red-600 text-sm">{{ $error }}</p>
                    @endforeach
                </div>
            @endif

            <!-- Success Message -->
            @if (session('status'))
                <div class="px-6 py-3 bg-green-50 border-t border-b border-green-200">
                    <p class="text-green-600 text-sm">{{ session('status') }}</p>
                </div>
            @endif

            <div class="p-6">
                @if (session('status'))
                    <div class="text-center space-y-4">
                        <div class="rounded-lg bg-indigo-50 p-4">
                            <p class="text-sm text-gray-600">
                                We've sent a password reset link to <strong>{{ old('email') }}</strong>. Please check
                                your inbox and follow the instructions to reset your password.
                            </p>
                        </div>
                        <p class="text-sm text-gray-600">
                            Didn't receive an email? Check your spam folder or
                            <a href="{{ route('password.request') }}"
                                class="text-indigo-600 hover:text-indigo-700 hover:underline">try again</a>
                        </p>
                    </div>
                @else
                    <form method="POST" action="{{ route('password.email') }}" class="space-y-4">
                        @csrf
                        <div class="space-y-2">
                            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                            <input id="email" name="email" type="email" placeholder="<EMAIL>"
                                value="{{ old('email') }}"
                                class="w-full border border-gray-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-300"
                                required>
                        </div>
                        <button type="submit"
                            class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-md transition-colors duration-200 flex items-center justify-center gap-2 font-medium">
                            Send reset link
                        </button>
                    </form>
                @endif
            </div>

            <div class="flex justify-center p-6">
                <p class="text-sm text-gray-600">
                    Remember your password?
                    <a href="{{ route('login') }}" class="text-indigo-600 hover:text-indigo-700 hover:underline">Back to
                        login</a>
                </p>
            </div>
        </div>
    </div>
</body>

</html>
