<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductChatbotData extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'product_id',
        'main_function',
        'key_features',
        'target_users',
        'requirements',
        'usage_instructions',
        'unique_selling_points',
        'limitations',
        'troubleshooting',
        'tips_optimization',
        'language_format_support',
        'common_questions',
        'contact_info',
        'is_active',
        'language',
        'custom_responses',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'custom_responses' => 'array',
    ];

    /**
     * Get the product that owns this chatbot data.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Check if all required fields are filled.
     */
    public function hasRequiredData()
    {
        $requiredFields = [
            'main_function',
            'key_features',
            'target_users',
            'requirements',
            'usage_instructions',
        ];

        foreach ($requiredFields as $field) {
            if (empty($this->$field)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get completion percentage of chatbot data.
     */
    public function getCompletionPercentageAttribute()
    {
        $allFields = [
            'main_function',
            'key_features',
            'target_users',
            'requirements',
            'usage_instructions',
            'unique_selling_points',
            'limitations',
            'troubleshooting',
            'tips_optimization',
            'language_format_support',
            'common_questions',
            'contact_info',
        ];

        $filledFields = 0;
        foreach ($allFields as $field) {
            if (!empty($this->$field)) {
                $filledFields++;
            }
        }

        return round(($filledFields / count($allFields)) * 100);
    }

    /**
     * Get all chatbot data as a formatted string for AI processing.
     */
    public function getFormattedDataForAi()
    {
        $data = [];

        if ($this->main_function) {
            $data[] = "Fungsi utama: " . $this->main_function;
        }

        if ($this->key_features) {
            $data[] = "Fitur utama: " . $this->key_features;
        }

        if ($this->target_users) {
            $data[] = "Target pengguna: " . $this->target_users;
        }

        if ($this->requirements) {
            $data[] = "Persyaratan: " . $this->requirements;
        }

        if ($this->usage_instructions) {
            $data[] = "Cara penggunaan: " . $this->usage_instructions;
        }

        if ($this->unique_selling_points) {
            $data[] = "Keunggulan: " . $this->unique_selling_points;
        }

        if ($this->limitations) {
            $data[] = "Batasan: " . $this->limitations;
        }

        if ($this->troubleshooting) {
            $data[] = "Troubleshooting: " . $this->troubleshooting;
        }

        if ($this->tips_optimization) {
            $data[] = "Tips optimasi: " . $this->tips_optimization;
        }

        if ($this->language_format_support) {
            $data[] = "Dukungan bahasa/format: " . $this->language_format_support;
        }

        if ($this->common_questions) {
            $data[] = "FAQ: " . $this->common_questions;
        }

        if ($this->contact_info) {
            $data[] = "Kontak seller: " . $this->contact_info;
        }

        return implode("\n\n", $data);
    }

    /**
     * Scope to get active chatbot data only.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
