<?php

use App\Http\Controllers\AiChatController;
use App\Http\Controllers\Auth\SocialiteController;
use App\Models\SellerApplication;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SellerApplicationController;
use App\Http\Controllers\Seller\DashboardController;
use App\Http\Controllers\Seller\ProductController;
use App\Http\Controllers\Seller\OrderController;
use App\Http\Controllers\Seller\AnalyticsController;
use App\Http\Controllers\Seller\DocumentationController;
use App\Http\Controllers\Seller\HelpCenterController;
use App\Http\Controllers\Seller\PaymentController;
use App\Http\Controllers\Seller\SettingsController;
use App\Http\Controllers\PagesController;

Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::get('/auth/google', [SocialiteController::class, 'redirectToGoogle'])->name('auth.google');
Route::get('/auth/google/callback', [SocialiteController::class, 'handleGoogleCallback']);

Route::middleware(['auth'])->group(function () {
    // Route::get('/dashboard', function () {
    //     return view('dashboard');
    // })->name('dashboard');

    // Seller Application Routes
    Route::get('/seller/apply', [SellerApplicationController::class, 'create'])->name('seller.apply');
    Route::post('/seller/apply', [SellerApplicationController::class, 'store'])->name('seller.store');
    Route::get('/seller/success', [SellerApplicationController::class, 'success'])->name('seller.success');
    Route::get('/seller/file/{applicationId}/{type}', [SellerApplicationController::class, 'serveFile'])->name('seller.file');
    Route::get('/seller/reapply', [SellerApplicationController::class, 'reapply'])->name('seller.reapply');
    Route::get('/seller/pending', function () {
        $user = auth()->user();
        $application = \App\Models\SellerApplication::where('user_id', $user->id)->first();

        // If no application exists, redirect to apply page
        if (!$application) {
            return redirect()->route('seller.apply');
        }

        // If application is approved, redirect to seller dashboard
        if ($application->status === 'approved') {
            return redirect()->route('seller.dashboard');
        }

        // If application is rejected, redirect to rejected page
        if ($application->status === 'rejected') {
            return redirect()->route('seller.rejected');
        }

        // If application is still pending, show the pending page
        return view('seller.pending');
    })->name('seller.pending');
    Route::get('/seller/rejected', function () {
        $user = auth()->user();
        $application = SellerApplication::where('user_id', $user->id)->first();

        // If no application exists, redirect to apply page
        if (!$application) {
            return redirect()->route('seller.apply');
        }

        // If application is approved, redirect to seller dashboard
        if ($application->status === 'approved') {
            return redirect()->route('seller.dashboard');
        }

        // If application is still pending, redirect to pending page
        if ($application->status === 'pending') {
            return redirect()->route('seller.pending');
        }

        // If application is rejected, show the rejected page
        return view('seller.rejected', compact('application'));
    })->name('seller.rejected');

    // Seller Dashboard Features (Placeholders)

    Route::get('/seller/store', function () {
        return view('seller.store');
    })->name('seller.view-store');

    // Admin Routes for Seller Applications
    Route::get('/admin/seller-applications', [SellerApplicationController::class, 'index'])->name('admin.seller_applications');
    Route::post('/admin/seller-applications/{application}/update-status', [SellerApplicationController::class, 'updateStatus'])->name('admin.seller_applications.update_status');

    // Admin Routes for Product Categories
    Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
        Route::resource('categories', App\Http\Controllers\Admin\ProductCategoryController::class);
        Route::post('categories/update-order', [App\Http\Controllers\Admin\ProductCategoryController::class, 'updateOrder'])->name('categories.update-order');
    });
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::post('/product/cancel-transaction', [App\Http\Controllers\OrderController::class, 'cancelTransaction'])->name('product.cancel-transaction');
    Route::post('/product/check-transaction', [App\Http\Controllers\OrderController::class, 'checkTransaction'])->name('product.check-transaction');
    Route::post('/product/buy', [App\Http\Controllers\OrderController::class, 'store'])->name('product.buy');
    Route::post('/product/download/{product:id}', [App\Http\Controllers\UserController::class, 'productDownload'])->name('product.download');
    Route::get('/course/{product:id}/access', [App\Http\Controllers\UserController::class, 'accessCourseContent'])->name('user.course-access');
    Route::get('/course/{product:id}/resource/{resourceId}', [App\Http\Controllers\UserController::class, 'downloadResource'])->name('user.download-resource');
    Route::get('/preview/{product:id}/resource/{resourceId}', [App\Http\Controllers\UserController::class, 'previewResource'])->name('user.preview-resource');
});

// Seller Dashboard Routes
Route::middleware(['auth', 'seller'])->prefix('seller')->name('seller.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    Route::post('products/{product}/toggle-status', [ProductController::class, 'toggleStatus'])->name('products.toggle-status');

    // Product Type Selection Route (must come before resource routes)
    Route::get('products/select-type', [ProductController::class, 'selectType'])->name('products.select-type');

    // Course Creation Route (redirects to new course system)
    Route::get('products/create-new-course', [ProductController::class, 'createNewCourse'])->name('products.create-new-course');

    // Product Routes
    Route::resource('products', ProductController::class);

    // Course Routes (Independent Course System)
    Route::resource('courses', App\Http\Controllers\Seller\CourseController::class);
    Route::post('courses/{course}/toggle-status', [App\Http\Controllers\Seller\CourseController::class, 'toggleStatus'])->name('courses.toggle-status');
    Route::post('courses/{course}/publish', [App\Http\Controllers\Seller\CourseController::class, 'publish'])->name('courses.publish');
    Route::post('courses/{course}/unpublish', [App\Http\Controllers\Seller\CourseController::class, 'unpublish'])->name('courses.unpublish');

    // Course Auto-save Routes
    Route::post('courses/auto-save', [App\Http\Controllers\Seller\CourseController::class, 'autoSave'])->name('courses.auto-save');
    Route::delete('courses/auto-save', [App\Http\Controllers\Seller\CourseController::class, 'clearAutoSave'])->name('courses.clear-auto-save');
    Route::post('courses/save-draft', [App\Http\Controllers\Seller\CourseController::class, 'saveDraft'])->name('courses.save-draft');

    // Course Section Management Routes (Udemy-style)
    Route::post('courses/{course}/sections', [App\Http\Controllers\Seller\CourseSectionController::class, 'store'])->name('courses.sections.store');
    Route::put('courses/{course}/sections/{section}', [App\Http\Controllers\Seller\CourseSectionController::class, 'update'])->name('courses.sections.update');
    Route::delete('courses/{course}/sections/{section}', [App\Http\Controllers\Seller\CourseSectionController::class, 'destroy'])->name('courses.sections.destroy');
    Route::post('courses/{course}/sections/reorder', [App\Http\Controllers\Seller\CourseSectionController::class, 'reorder'])->name('courses.sections.reorder');
    Route::post('courses/{course}/sections/{section}/toggle-status', [App\Http\Controllers\Seller\CourseSectionController::class, 'toggleStatus'])->name('courses.sections.toggle-status');

    // Course Curriculum Item Management Routes (Udemy-style)
    Route::post('courses/{course}/sections/{section}/curriculum-items', [App\Http\Controllers\Seller\CourseCurriculumItemController::class, 'store'])->name('courses.curriculum-items.store');
    Route::put('courses/{course}/sections/{section}/curriculum-items/{item}', [App\Http\Controllers\Seller\CourseCurriculumItemController::class, 'update'])->name('courses.curriculum-items.update');
    Route::delete('courses/{course}/sections/{section}/curriculum-items/{item}', [App\Http\Controllers\Seller\CourseCurriculumItemController::class, 'destroy'])->name('courses.curriculum-items.destroy');
    Route::post('courses/{course}/sections/{section}/curriculum-items/reorder', [App\Http\Controllers\Seller\CourseCurriculumItemController::class, 'reorder'])->name('courses.curriculum-items.reorder');
    Route::post('courses/{course}/sections/{section}/curriculum-items/{item}/toggle-status', [App\Http\Controllers\Seller\CourseCurriculumItemController::class, 'toggleStatus'])->name('courses.curriculum-items.toggle-status');

    // Product Chatbot Routes
    Route::get('products/{product}/chatbot', [App\Http\Controllers\Seller\ChatbotController::class, 'show'])->name('products.chatbot');
    Route::post('products/{product}/chatbot', [App\Http\Controllers\Seller\ChatbotController::class, 'store'])->name('products.chatbot.store');
    Route::post('products/{product}/chatbot/toggle', [App\Http\Controllers\Seller\ChatbotController::class, 'toggle'])->name('products.chatbot.toggle');
    Route::delete('products/{product}/chatbot', [App\Http\Controllers\Seller\ChatbotController::class, 'destroy'])->name('products.chatbot.destroy');
    Route::get('products/{product}/chatbot/status', [App\Http\Controllers\Seller\ChatbotController::class, 'status'])->name('products.chatbot.status');

    // Order Routes
    Route::resource('orders', OrderController::class)->only(['index', 'show', 'update']);
    Route::get('orders/{order}/download-invoice', [OrderController::class, 'downloadInvoice'])->name('orders.downloadInvoice');
    Route::get('orders/export/csv', [OrderController::class, 'exportCsv'])->name('orders.export.csv');


    // Analytics Routes
    Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics');
    Route::get('/analytics/export/csv', [AnalyticsController::class, 'exportCsv'])->name('analytics.export.csv');


    // Payments Routes
    Route::resource('payments', PaymentController::class)->only(['index', 'show']);
    Route::get('payments/{payment}/download-receipt', [PaymentController::class, 'downloadReceipt'])->name('payments.downloadReceipt');
    Route::post('payments/update-payment-method', [PaymentController::class, 'updatePaymentMethod'])->name('payments.updatePaymentMethod');

    // Settings Routes
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings');
    Route::post('/settings/profile', [SettingsController::class, 'updateProfile'])->name('settings.updateProfile');
    Route::post('/settings/store', [SettingsController::class, 'updateStore'])->name('settings.updateStore');
    Route::post('/settings/password', [SettingsController::class, 'updatePassword'])->name('settings.updatePassword');
    Route::post('/settings/notifications', [SettingsController::class, 'updateNotifications'])->name('settings.updateNotifications');

    // Seller Membership Routes
    Route::get('/membership', [App\Http\Controllers\Seller\MembershipController::class, 'index'])->name('membership.index');
    Route::post('/membership/upgrade', [App\Http\Controllers\Seller\MembershipController::class, 'upgrade'])->name('membership.upgrade');

    // Help Center Routes
    Route::prefix('help-center')->name('help-center.')->group(function () {
        Route::get('/', [HelpCenterController::class, 'index'])->name('index');
        Route::get('/article/{slug}', [HelpCenterController::class, 'article'])->name('article');
        Route::get('/contact', [HelpCenterController::class, 'contactForm'])->name('contact');
        Route::post('/contact', [HelpCenterController::class, 'submitContact'])->name('submitContact');
    });

    // Documentation Routes
    Route::prefix('documentation')->name('documentation.')->group(function () {
        Route::get('/', [DocumentationController::class, 'index'])->name('index');
        Route::get('/{slug}', [DocumentationController::class, 'show'])->name('show');
        Route::post('/{slug}/feedback', [DocumentationController::class, 'feedback'])->name('feedback');
    });
});

// User Dashboard Routes
Route::middleware(['auth'])->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\UserController::class, 'dashboard'])->name('dashboard');
    Route::get('/purchases', [App\Http\Controllers\UserController::class, 'purchases'])->name('purchases');
    Route::get('/profile', [App\Http\Controllers\UserController::class, 'profile'])->name('profile');
    Route::put('/profile', [App\Http\Controllers\UserController::class, 'updateProfile'])->name('profile.update');
    Route::put('/password', [App\Http\Controllers\UserController::class, 'updatePassword'])->name('password.update');
    Route::get('/settings', [App\Http\Controllers\UserController::class, 'settings'])->name('settings');
    Route::put('/settings', [App\Http\Controllers\UserController::class, 'updateSettings'])->name('settings.update');
    Route::get('/seller-dashboard', [App\Http\Controllers\UserController::class, 'sellerDashboard'])->name('seller-dashboard');
});

// Membership Routes
Route::middleware(['auth'])->prefix('membership')->name('membership.')->group(function () {
    Route::get('/', [App\Http\Controllers\MembershipController::class, 'index'])->name('index');
    Route::post('/upgrade', [App\Http\Controllers\MembershipController::class, 'upgrade'])->name('upgrade');
});

// API Routes for Membership
Route::middleware(['auth'])->prefix('api/membership')->name('api.membership.')->group(function () {
    Route::get('/status', [App\Http\Controllers\MembershipController::class, 'status'])->name('status');
    Route::post('/upgrade', [App\Http\Controllers\MembershipController::class, 'upgrade'])->name('upgrade');
});

// Public Browse Products Route (Legacy)
Route::get('/browse', [PagesController::class, 'browseProducts'])->name('user.browse');

// User Dashboard Browse Routes
Route::middleware(['auth'])->prefix('user')->name('user.browse.')->group(function () {
    Route::get('/browse/products', [App\Http\Controllers\UserController::class, 'browseProducts'])->name('products');
    Route::get('/browse/stores', [App\Http\Controllers\UserController::class, 'browseStores'])->name('stores');
});

// Cart Routes
Route::get('/cart', [App\Http\Controllers\CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add', [App\Http\Controllers\CartController::class, 'addToCart'])->name('cart.add');
Route::post('/cart/add-course', [App\Http\Controllers\CartController::class, 'addCourseToCart'])->name('cart.add-course');
Route::put('/cart/update/{id}', [App\Http\Controllers\CartController::class, 'updateCartItem'])->name('cart.update');
Route::delete('/cart/remove/{id}', [App\Http\Controllers\CartController::class, 'removeCartItem'])->name('cart.remove');
Route::delete('/cart/clear', [App\Http\Controllers\CartController::class, 'clearCart'])->name('cart.clear');
Route::get('/cart/checkout', [App\Http\Controllers\CartController::class, 'checkout'])->name('cart.checkout');
Route::post('/cart/process-payment', [App\Http\Controllers\CartController::class, 'processPayment'])->name('cart.process-payment');
Route::post('/cart/check-transactions', [App\Http\Controllers\CartController::class, 'checkTransactions'])->name('cart.check-transactions');
Route::post('/cart/cancel-transactions', [App\Http\Controllers\CartController::class, 'cancelTransactions'])->name('cart.cancel-transactions');

Route::get('/products', function () {
    return view('products');
})->name('products');

Route::get('/categories', function () {
    return view('categories');
})->name('categories');

Route::get('/category/{slug}', function ($slug) {
    return view('category', ['slug' => $slug]);
})->name('category');

// Unified Browse System Routes (Standalone Pages) - No Authentication Required
Route::name('browse.')->group(function () {
    // Courses
    Route::get('/browse/courses', [App\Http\Controllers\BrowseController::class, 'courses'])->name('courses');
    Route::get('/browse/courses/{course:slug}', [App\Http\Controllers\CourseViewController::class, 'show'])->name('courses.show');

    // Products
    Route::get('/browse/products', [App\Http\Controllers\BrowseController::class, 'products'])->name('products');

    // Stores
    Route::get('/browse/stores', [App\Http\Controllers\BrowseController::class, 'stores'])->name('stores');
});

// Course-specific routes that require authentication for access/downloads
Route::middleware(['auth'])->name('browse.')->group(function () {
    Route::get('/browse/courses/{course:slug}/access', [App\Http\Controllers\CourseViewController::class, 'access'])->name('courses.access');
    Route::get('/browse/courses/{course:slug}/curriculum-item/{item}', [App\Http\Controllers\CourseViewController::class, 'curriculumItem'])->name('courses.curriculum-item');
    Route::get('/browse/courses/{course:slug}/curriculum-item/{item}/download', [App\Http\Controllers\CourseViewController::class, 'downloadCurriculumItem'])->name('courses.curriculum-item.download');
});

// Course access status pages
Route::name('browse.courses.')->group(function () {
    Route::get('/browse/courses/{course:slug}/access-denied', [App\Http\Controllers\CourseViewController::class, 'accessDenied'])->name('access-denied');
    Route::get('/browse/courses/{course:slug}/access-granted', [App\Http\Controllers\CourseViewController::class, 'accessGranted'])->name('access-granted');
});

// Course direct purchase routes
Route::middleware(['auth'])->group(function () {
    Route::get('/course/{course:slug}/buy', [App\Http\Controllers\CourseViewController::class, 'buyNow'])->name('course.buy');
    Route::post('/course/{course:slug}/purchase', [App\Http\Controllers\CourseViewController::class, 'processPurchase'])->name('course.purchase');
});

// Course progress API routes
Route::middleware(['auth'])->prefix('api/course')->name('api.course.')->group(function () {
    Route::post('{course:slug}/progress', [App\Http\Controllers\CourseViewController::class, 'updateProgress'])->name('update-progress');
    Route::get('{course:slug}/progress', [App\Http\Controllers\CourseViewController::class, 'getProgress'])->name('get-progress');
    Route::post('{course:slug}/complete', [App\Http\Controllers\CourseViewController::class, 'markComplete'])->name('mark-complete');
});

// Legacy Browse Routes (Dashboard Integration - keep for backward compatibility)
Route::middleware(['auth'])->prefix('user')->name('user.browse.')->group(function () {
    Route::get('/browse/courses', [App\Http\Controllers\CourseViewController::class, 'index'])->name('courses');
    Route::get('/browse/courses/{course:slug}', [App\Http\Controllers\CourseViewController::class, 'show'])->name('courses.show');
    Route::get('/browse/courses/{course:slug}/access', [App\Http\Controllers\CourseViewController::class, 'access'])->name('courses.access');
    Route::get('/browse/courses/{course:slug}/curriculum-item/{item}', [App\Http\Controllers\CourseViewController::class, 'curriculumItem'])->name('courses.curriculum-item');
    Route::get('/browse/courses/{course:slug}/curriculum-item/{item}/download', [App\Http\Controllers\CourseViewController::class, 'downloadCurriculumItem'])->name('courses.curriculum-item.download');
});

// Removed subchapter API routes - no longer needed with simplified structure

Route::get('/product/{slug}', function ($slug) {
    return view('product', ['slug' => $slug]);
})->name('product');

Route::get('/sell', function () {
    return view('sell');
})->name('sell');

// Route::get('/about', [PagesController::class, 'aboutUs'])->name('about');
Route::get('/about', function () {
    return redirect('/');
})->name('about');

// Commented out routes that are not needed for now
/*
Route::get('/pricing', function () {
    return view('pricing');
})->name('pricing');

Route::get('/success-stories', [PagesController::class, 'successStories'])->name('success-stories');
Route::get('/blog', [PagesController::class, 'blog'])->name('blog');
Route::get('/careers', [PagesController::class, 'careers'])->name('careers');
Route::get('/contact', [PagesController::class, 'contact'])->name('contact');
Route::post('/contact', [PagesController::class, 'submitContact'])->name('contact.submit');
Route::get('/help', [PagesController::class, 'helpCenter'])->name('help');
Route::get('/help/article/{slug}', [PagesController::class, 'helpArticle'])->name('help.article');

// Alternative routes with explicit controller
Route::get('/terms-of-service', [App\Http\Controllers\LegalController::class, 'terms'])->name('terms.alt');
Route::get('/privacy-policy', [App\Http\Controllers\LegalController::class, 'privacy'])->name('privacy.alt');

Route::get('/faq', [PagesController::class, 'faq'])->name('faq');
*/
Route::get('/terms-of-service', [App\Http\Controllers\LegalController::class, 'terms'])->name('terms.alt');
Route::get('/privacy-policy', [App\Http\Controllers\LegalController::class, 'privacy'])->name('privacy.alt');

// Keep these routes active for basic functionality
// Route::get('/pricing', function () { return redirect('/'); })->name('pricing');
// Route::get('/success-stories', function () { return redirect('/'); })->name('success-stories');
// Route::get('/blog', function () { return redirect('/'); })->name('blog');
// Route::get('/careers', function () { return redirect('/'); })->name('careers');
// Route::get('/contact', function () { return redirect('/'); })->name('contact');
// Route::get('/help', function () { return redirect('/'); })->name('help');
// Route::get('/terms-of-service', function () { return redirect('/'); })->name('terms.alt');
// Route::get('/privacy-policy', function () { return redirect('/'); })->name('privacy.alt');
// Route::get('/faq', function () { return redirect('/'); })->name('faq');

// Store Logo Route
Route::get('/seller/products/{product:slug}/preview', [ProductController::class, 'preview'])->name('products.show');
Route::get('/store-logo/{storeSlug}', [App\Http\Controllers\StoreController::class, 'getStoreLogo'])->name('store.logo');

// AI Chat Routes
Route::prefix('ai-chat')->name('ai-chat.')->group(function () {
    Route::get('/conversation', [AiChatController::class, 'getConversation'])->name('conversation');
    Route::post('/send-message', [AiChatController::class, 'sendMessage'])->name('send-message');
    Route::post('/clear-conversation', [AiChatController::class, 'clearConversation'])->name('clear-conversation');
});

// Public Store Routes
Route::get('/{storeNameSlug}', [App\Http\Controllers\StoreController::class, 'show'])->name('store.show');
Route::get('/{storeNameSlug}/all-products', [App\Http\Controllers\StoreController::class, 'allProducts'])->name('store.all-products');
Route::get('/{storeNameSlug}/category/{category}', [App\Http\Controllers\StoreController::class, 'category'])->name('store.category');
Route::get('/{storeNameSlug}/search', [App\Http\Controllers\StoreController::class, 'search'])->name('store.search');
Route::get('/{storeNameSlug}/{product:slug}', [App\Http\Controllers\StoreController::class, 'product'])->name('store.product');

// Route::get('/test-page', function () {
//     return view('test-page');
// })->name('test.page');

// Authentication routes are now handled by Laravel Fortify
