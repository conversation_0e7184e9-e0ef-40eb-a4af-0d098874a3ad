/**
 * Course Learning Interface - Enhanced JavaScript
 * Modern, responsive course viewing experience with sidebar toggle
 */

class CourseLearningInterface {
    constructor() {
        this.sidebar = null;
        this.sidebarToggle = null;
        this.sidebarOverlay = null;
        this.courseLayout = null;
        this.isDesktop = window.innerWidth >= 1025;
        this.sidebarVisible = this.isDesktop;
        
        this.init();
    }

    init() {
        this.setupElements();
        this.bindEvents();
        this.setupResponsiveHandling();
        this.initializeProgress();

        // Ensure initial state is correct
        this.updateToggleButton();

        console.log('Course Learning Interface initialized');
    }

    setupElements() {
        this.sidebar = document.querySelector('.course-sidebar');
        this.courseLayout = document.querySelector('.course-layout');

        // Get existing sidebar toggle button (should already exist in template)
        this.sidebarToggle = document.querySelector('.sidebar-toggle');

        // Create sidebar toggle button if it doesn't exist (fallback)
        if (!this.sidebarToggle) {
            this.createSidebarToggle();
            this.sidebarToggle = document.querySelector('.sidebar-toggle');
        }

        // Create overlay for mobile
        if (!document.querySelector('.sidebar-overlay')) {
            this.createSidebarOverlay();
        }

        this.sidebarOverlay = document.querySelector('.sidebar-overlay');


    }

    createSidebarToggle() {
        const toggle = document.createElement('button');
        toggle.className = 'sidebar-toggle';
        toggle.setAttribute('aria-label', 'Toggle course content sidebar');
        toggle.innerHTML = `
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
        `;
        
        const headerContent = document.querySelector('.course-header-content');
        if (headerContent) {
            headerContent.appendChild(toggle);
        }
    }

    createSidebarOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        document.body.appendChild(overlay);
    }

    bindEvents() {
        // Sidebar toggle
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        }

        // Overlay click to close sidebar on mobile
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => this.hideSidebar());
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.sidebarVisible && !this.isDesktop) {
                this.hideSidebar();
            }
            if (e.key === 's' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.toggleSidebar();
            }
        });

        // Window resize handling
        window.addEventListener('resize', () => this.handleResize());

        // Curriculum item interactions
        this.setupCurriculumItemEvents();
    }

    setupCurriculumItemEvents() {
        const curriculumItems = document.querySelectorAll('.course-curriculum-item');
        
        curriculumItems.forEach(item => {
            // Add hover effects and click tracking
            item.addEventListener('mouseenter', () => {
                this.highlightCurriculumItem(item);
            });
            
            item.addEventListener('mouseleave', () => {
                this.unhighlightCurriculumItem(item);
            });
            
            item.addEventListener('click', (e) => {
                this.trackCurriculumItemClick(item);
                // On mobile, hide sidebar after selection
                if (!this.isDesktop) {
                    setTimeout(() => this.hideSidebar(), 300);
                }
            });
        });
    }

    highlightCurriculumItem(item) {
        if (!item.classList.contains('active')) {
            item.style.transform = 'translateX(8px)';
        }
    }

    unhighlightCurriculumItem(item) {
        if (!item.classList.contains('active')) {
            item.style.transform = 'translateX(4px)';
        }
    }

    trackCurriculumItemClick(item) {
        // Only track progress for actual curriculum items, not overview
        if (!item.classList.contains('course-overview-item')) {
            // Mark as visited/clicked for progress tracking
            item.classList.add('visited');
            this.updateProgress();
        }
    }

    toggleSidebar() {
        if (this.sidebarVisible) {
            this.hideSidebar();
        } else {
            this.showSidebar();
        }
    }

    showSidebar() {
        if (!this.sidebar) return;

        this.sidebarVisible = true;

        if (this.isDesktop) {
            // Desktop: show sidebar by removing hidden class and adjusting layout
            this.sidebar.classList.remove('hidden');
            this.courseLayout?.classList.remove('sidebar-hidden');
        } else {
            // Mobile: show as overlay
            this.sidebar.classList.add('show');
            this.sidebar.classList.remove('hidden');
            this.sidebarOverlay?.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        // Update toggle button
        this.updateToggleButton();

        // Store preference
        localStorage.setItem('courseSidebarVisible', 'true');
    }

    hideSidebar() {
        if (!this.sidebar) return;

        this.sidebarVisible = false;

        if (this.isDesktop) {
            // Desktop: hide sidebar by adding hidden class and adjusting layout
            this.sidebar.classList.add('hidden');
            this.courseLayout?.classList.add('sidebar-hidden');
        } else {
            // Mobile: hide overlay
            this.sidebar.classList.remove('show');
            this.sidebar.classList.add('hidden');
            this.sidebarOverlay?.classList.remove('show');
            document.body.style.overflow = '';
        }

        // Update toggle button
        this.updateToggleButton();

        // Store preference
        localStorage.setItem('courseSidebarVisible', 'false');
    }

    updateToggleButton() {
        if (!this.sidebarToggle) return;

        const icon = this.sidebarToggle.querySelector('svg');
        if (!icon) return;

        if (this.sidebarVisible) {
            // Show sidebar close icon (left arrow)
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>';
            this.sidebarToggle.classList.add('active');
            this.sidebarToggle.setAttribute('aria-label', 'Hide course content sidebar');
        } else {
            // Show sidebar open icon (right arrow)
            icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
            this.sidebarToggle.classList.remove('active');
            this.sidebarToggle.setAttribute('aria-label', 'Show course content sidebar');
        }
    }

    handleResize() {
        const wasDesktop = this.isDesktop;
        this.isDesktop = window.innerWidth >= 1025;
        
        if (wasDesktop !== this.isDesktop) {
            // Device type changed, reset sidebar state
            this.setupResponsiveHandling();
        }
    }

    setupResponsiveHandling() {
        // Load saved preference or use default
        const savedPreference = localStorage.getItem('courseSidebarVisible');

        if (this.isDesktop) {
            // Desktop: sidebar visible by default unless explicitly hidden
            this.sidebarVisible = savedPreference !== 'false';

            // Clean up mobile-specific classes
            this.sidebar?.classList.remove('show');
            this.sidebarOverlay?.classList.remove('show');
            document.body.style.overflow = '';

            // Apply desktop layout
            if (this.sidebarVisible) {
                this.sidebar?.classList.remove('hidden');
                this.courseLayout?.classList.remove('sidebar-hidden');
            } else {
                this.sidebar?.classList.add('hidden');
                this.courseLayout?.classList.add('sidebar-hidden');
            }
        } else {
            // Mobile: sidebar hidden by default
            this.sidebarVisible = false;
            this.sidebar?.classList.remove('show');
            this.sidebar?.classList.add('hidden');
            this.sidebarOverlay?.classList.remove('show');
            document.body.style.overflow = '';
        }

        // Update toggle button state
        this.updateToggleButton();
    }

    initializeProgress() {
        // Load progress from server
        this.loadProgressFromServer();

        // Mark current item as active if URL contains curriculum item
        const currentPath = window.location.pathname;
        if (currentPath.includes('/curriculum-item/')) {
            const curriculumItems = document.querySelectorAll('.course-curriculum-item:not(.course-overview-item)');
            curriculumItems.forEach(item => {
                if (item.href && currentPath.includes(item.href.split('/').pop())) {
                    item.classList.add('active');
                }
            });
        }

        // Initialize progress tracking after marking current item
        this.updateProgress();
    }

    loadProgressFromServer() {
        // Extract course slug from URL
        const pathParts = window.location.pathname.split('/');
        const courseSlugIndex = pathParts.indexOf('courses') + 1;
        if (courseSlugIndex > 0 && courseSlugIndex < pathParts.length) {
            const courseSlug = pathParts[courseSlugIndex];

            fetch(`/api/course/${courseSlug}/progress`)
                .then(response => response.json())
                .then(data => {
                    if (data.completed_items && Array.isArray(data.completed_items)) {
                        // Mark completed items as visited
                        data.completed_items.forEach(itemId => {
                            const item = document.querySelector(`[href*="${itemId}"]`);
                            if (item && item.classList.contains('course-curriculum-item')) {
                                item.classList.add('visited');
                            }
                        });

                        // Update progress display
                        this.updateProgress();
                    }
                })
                .catch(error => {
                    console.log('Could not load progress from server:', error);
                });
        }
    }

    updateProgress() {
        // Only count actual curriculum items, exclude the overview item
        const totalItems = document.querySelectorAll('.course-curriculum-item:not(.course-overview-item)').length;
        const visitedItems = document.querySelectorAll('.course-curriculum-item:not(.course-overview-item).visited, .course-curriculum-item:not(.course-overview-item).active').length;

        if (totalItems > 0) {
            const progressPercentage = Math.round((visitedItems / totalItems) * 100);

            // Update progress bar
            const progressFill = document.querySelector('.course-progress-fill');
            const progressText = document.querySelector('.course-progress-text');

            if (progressFill) {
                progressFill.style.width = `${progressPercentage}%`;
            }

            if (progressText) {
                progressText.textContent = `Progress: ${progressPercentage}%`;
            }

            console.log(`Progress updated: ${visitedItems}/${totalItems} = ${progressPercentage}%`);
        }
    }

    // Public API methods
    getSidebarState() {
        return {
            visible: this.sidebarVisible,
            isDesktop: this.isDesktop
        };
    }

    setSidebarState(visible) {
        if (visible) {
            this.showSidebar();
        } else {
            this.hideSidebar();
        }
    }

    // Debug function to check current state
    debugState() {
        const sidebar = document.querySelector('.course-sidebar');
        const layout = document.querySelector('.course-layout');
        const contentArea = document.querySelector('.course-content-area');

        console.log('=== Course Learning Debug State ===');
        console.log('Sidebar visible:', this.sidebarVisible);
        console.log('Is desktop:', this.isDesktop);
        console.log('Sidebar element:', sidebar);
        console.log('Sidebar classes:', sidebar?.className);
        console.log('Layout element:', layout);
        console.log('Layout classes:', layout?.className);
        console.log('Content area element:', contentArea);
        console.log('Content area classes:', contentArea?.className);
        console.log('Content area computed style:', contentArea ? window.getComputedStyle(contentArea) : null);
        console.log('===================================');

        return {
            sidebarVisible: this.sidebarVisible,
            isDesktop: this.isDesktop,
            elements: { sidebar, layout, contentArea }
        };
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const courseInterface = new CourseLearningInterface();
    window.courseLearningInterface = courseInterface;

    // Make debug function globally accessible
    window.debugCourseInterface = () => courseInterface.debugState();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CourseLearningInterface;
}
