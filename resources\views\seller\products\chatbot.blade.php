@extends('seller.layouts.app')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Product Chatbot Configuration</h1>
                <p class="text-gray-600 mt-1">Configure AI chatbot for: <span class="font-semibold">{{ $product->name }}</span></p>
            </div>
            <a href="{{ route('seller.products.edit', $product) }}"
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                Back to Product
            </a>
        </div>
    </div>

    <!-- Membership Status -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-blue-900">Chatbot Permissions</h3>
                <p class="text-blue-700">Current Tier: {{ $currentTier ? $currentTier->name : 'No membership' }}</p>
                @if($currentTier)
                    <p class="text-sm text-blue-600">
                        Chatbot Limit:
                        @if($currentTier->chatbot_products_limit === -1)
                            Unlimited
                        @elseif($currentTier->chatbot_products_limit === 0)
                            Not Available
                        @else
                            {{ $activeChatbotCount }}/{{ $currentTier->chatbot_products_limit }} used
                        @endif
                    </p>
                @endif
            </div>
            @if(!$canActivateChatbot)
            <div class="text-right">
                <p class="text-red-600 font-semibold">Upgrade Required</p>
                <a href="{{ route('membership.index') }}"
                   class="text-blue-600 hover:text-blue-800 text-sm underline">
                    View Membership Plans
                </a>
            </div>
            @endif
        </div>
    </div>

    <!-- Chatbot Configuration Form -->
    <form id="chatbot-form" class="space-y-6">
        @csrf

        <!-- Status and Progress -->
        <div class="bg-white rounded-lg shadow-lg border border-gray-200">
            <div class="border-b border-gray-100 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Chatbot Status</h3>
                        <p class="text-sm text-gray-600">Current configuration status and activation</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Completion</p>
                            <p class="text-lg font-bold text-blue-600" id="completion-percentage">
                                {{ $chatbotData->completion_percentage ?? 0 }}%
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox"
                                       name="is_active"
                                       id="chatbot-active"
                                       class="sr-only peer"
                                       {{ $chatbotData->is_active ? 'checked' : '' }}
                                       {{ !$canActivateChatbot ? 'disabled' : '' }}>
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                <span class="ml-3 text-sm font-medium text-gray-900" id="toggle-text">
                                    {{ $chatbotData->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Required Fields -->
        <div class="bg-white rounded-lg shadow-lg border border-gray-200">
            <div class="border-b border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900">Required Information</h3>
                <p class="text-sm text-gray-600">These fields are required to activate the chatbot</p>
                <div class="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-900">Tips untuk mengisi form:</p>
                            <ul class="text-xs text-blue-700 mt-1 space-y-1">
                                <li>• Gunakan bahasa yang mudah dipahami customer</li>
                                <li>• Berikan informasi yang spesifik dan detail</li>
                                <li>• Semakin lengkap informasi, semakin baik chatbot membantu customer</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 space-y-6">
                <div>
                    <label for="main_function" class="block text-sm font-medium text-gray-700 mb-2">
                        Apa fungsi utama produk ini? <span class="text-red-500">*</span>
                    </label>
                    <textarea name="main_function"
                              id="main_function"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Jelaskan fungsi utama produk Anda. Contoh: Template ini digunakan untuk membuat laporan keuangan bulanan yang mudah dipahami."
                              maxlength="1000">@if($chatbotData && $chatbotData->main_function){{ $chatbotData->main_function }}@endif</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimal 1000 karakter</p>
                </div>

                <div>
                    <label for="key_features" class="block text-sm font-medium text-gray-700 mb-2">
                        Apa saja fitur utama produk ini? <span class="text-red-500">*</span>
                    </label>
                    <textarea name="key_features"
                              id="key_features"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Sebutkan fitur-fitur unggulan. Contoh: Support Excel 2016+, format otomatis, grafik interaktif, dan panduan lengkap."
                              maxlength="1000">@if($chatbotData && $chatbotData->key_features){{ $chatbotData->key_features }}@endif</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimal 1000 karakter</p>
                </div>

                <div>
                    <label for="target_users" class="block text-sm font-medium text-gray-700 mb-2">
                        Siapa target pengguna produk ini? <span class="text-red-500">*</span>
                    </label>
                    <textarea name="target_users"
                              id="target_users"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Tentukan target pengguna. Contoh: UMKM, akuntan, pemilik bisnis kecil, atau mahasiswa ekonomi."
                              maxlength="1000">@if($chatbotData && $chatbotData->target_users){{ $chatbotData->target_users }}@endif</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimal 1000 karakter</p>
                </div>

                <div>
                    <label for="requirements" class="block text-sm font-medium text-gray-700 mb-2">
                        Apa saja persyaratan untuk menggunakan produk ini? <span class="text-red-500">*</span>
                    </label>
                    <textarea name="requirements"
                              id="requirements"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Jelaskan persyaratan sistem atau software. Contoh: Membutuhkan Microsoft Excel 2016 atau lebih baru, kompatibel dengan Windows dan Mac."
                              maxlength="1000">@if($chatbotData && $chatbotData->requirements){{ $chatbotData->requirements }}@endif</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimal 1000 karakter</p>
                </div>

                <div>
                    <label for="usage_instructions" class="block text-sm font-medium text-gray-700 mb-2">
                        Bagaimana cara menggunakan produk ini? <span class="text-red-500">*</span>
                    </label>
                    <textarea name="usage_instructions"
                              id="usage_instructions"
                              rows="4"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Berikan panduan penggunaan step-by-step. Contoh: 1) Unduh file template, 2) Buka di Excel, 3) Masukkan data keuangan Anda, 4) Gunakan grafik bawaan untuk analisis."
                              maxlength="1000">@if($chatbotData && $chatbotData->usage_instructions){{ $chatbotData->usage_instructions }}@endif</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimal 1000 karakter</p>
                </div>
            </div>
        </div>

        <!-- Optional Fields -->
        <div class="bg-white rounded-lg shadow-lg border border-gray-200">
            <div class="border-b border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900">Optional Information</h3>
                <p class="text-sm text-gray-600">These fields enhance chatbot responses but are not required</p>
                <div class="mt-3 bg-green-50 border border-green-200 rounded-lg p-3">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-green-900">Informasi tambahan ini akan membuat chatbot lebih pintar:</p>
                            <ul class="text-xs text-green-700 mt-1 space-y-1">
                                <li>• Chatbot dapat menjawab pertanyaan yang lebih spesifik</li>
                                <li>• Customer mendapat informasi yang lebih lengkap</li>
                                <li>• Meningkatkan kepercayaan dan konversi penjualan</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 space-y-6">
                <div>
                    <label for="unique_selling_points" class="block text-sm font-medium text-gray-700 mb-2">
                        Apa yang membuat produk ini berbeda dari yang lain?
                    </label>
                    <textarea name="unique_selling_points"
                              id="unique_selling_points"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Jelaskan keunggulan unik produk Anda. Contoh: Template ini dilengkapi dengan formula otomatis yang menghemat waktu hingga 50% dibanding template lain."
                              maxlength="1000">@if($chatbotData && $chatbotData->unique_selling_points){{ $chatbotData->unique_selling_points }}@endif</textarea>
                </div>

                <div>
                    <label for="limitations" class="block text-sm font-medium text-gray-700 mb-2">
                        Apakah ada batasan atau hal yang perlu diperhatikan?
                    </label>
                    <textarea name="limitations"
                              id="limitations"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Sebutkan batasan jika ada. Contoh: Tidak support Excel versi di bawah 2016, atau membutuhkan koneksi internet untuk fitur tertentu."
                              maxlength="1000">@if($chatbotData && $chatbotData->limitations){{ $chatbotData->limitations }}@endif</textarea>
                </div>

                <div>
                    <label for="troubleshooting" class="block text-sm font-medium text-gray-700 mb-2">
                        Bagaimana cara mengatasi masalah umum saat menggunakan produk ini?
                    </label>
                    <textarea name="troubleshooting"
                              id="troubleshooting"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Berikan solusi masalah umum. Contoh: Jika grafik tidak muncul, pastikan Anda mengaktifkan macro di Excel dan refresh data."
                              maxlength="1000">@if($chatbotData && $chatbotData->troubleshooting){{ $chatbotData->troubleshooting }}@endif</textarea>
                </div>

                <div>
                    <label for="tips_optimization" class="block text-sm font-medium text-gray-700 mb-2">
                        Apakah ada tips untuk memaksimalkan penggunaan produk ini?
                    </label>
                    <textarea name="tips_optimization"
                              id="tips_optimization"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Bagikan tips berguna. Contoh: Gunakan fitur filter untuk analisis data yang lebih cepat, atau backup data secara berkala."
                              maxlength="1000">@if($chatbotData && $chatbotData->tips_optimization){{ $chatbotData->tips_optimization }}@endif</textarea>
                </div>

                <div>
                    <label for="language_format_support" class="block text-sm font-medium text-gray-700 mb-2">
                        Apakah produk ini mendukung bahasa atau format tertentu?
                    </label>
                    <textarea name="language_format_support"
                              id="language_format_support"
                              rows="3"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Jelaskan dukungan bahasa/format. Contoh: Tersedia dalam Bahasa Indonesia dan Inggris, support format .xlsx dan .csv."
                              maxlength="1000">@if($chatbotData && $chatbotData->language_format_support){{ $chatbotData->language_format_support }}@endif</textarea>
                </div>

                <div>
                    <label for="common_questions" class="block text-sm font-medium text-gray-700 mb-2">
                        FAQ yang sering ditanyakan
                    </label>
                    <textarea name="common_questions"
                              id="common_questions"
                              rows="4"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Tulis FAQ dalam format Q&A. Contoh: Q: Apakah bisa digunakan di Mac? A: Ya, selama menggunakan Excel 2016 atau lebih baru. Q: Apakah ada tutorial? A: Ya, disertakan video tutorial lengkap."
                              maxlength="2000">@if($chatbotData && $chatbotData->common_questions){{ $chatbotData->common_questions }}@endif</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimal 2000 karakter</p>
                </div>

                <div>
                    <label for="contact_info" class="block text-sm font-medium text-gray-700 mb-2">
                        Info kontak untuk pertanyaan lanjutan
                    </label>
                    <textarea name="contact_info"
                              id="contact_info"
                              rows="2"
                              class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Berikan info kontak Anda. Contoh: Hubungi saya di email: <EMAIL> atau WhatsApp: 08123456789 untuk pertanyaan lebih lanjut."
                              maxlength="500">@if($chatbotData && $chatbotData->contact_info){{ $chatbotData->contact_info }}@endif</textarea>
                    <p class="text-xs text-gray-500 mt-1">Maksimal 500 karakter</p>
                </div>
            </div>
        </div>

        <!-- Settings -->
        <div class="bg-white rounded-lg shadow-lg border border-gray-200">
            <div class="border-b border-gray-100 p-6">
                <h3 class="text-lg font-semibold text-gray-900">Chatbot Settings</h3>
                <p class="text-sm text-gray-600">Configure chatbot behavior and language</p>
            </div>
            <div class="p-6">
                <div>
                    <label for="language" class="block text-sm font-medium text-gray-700 mb-2">
                        Bahasa Chatbot <span class="text-red-500">*</span>
                    </label>
                    <select name="language"
                            id="language"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="id" {{ ($chatbotData->language ?? 'id') === 'id' ? 'selected' : '' }}>
                            Bahasa Indonesia
                        </option>
                        <option value="en" {{ ($chatbotData->language ?? 'id') === 'en' ? 'selected' : '' }}>
                            English
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between items-center">
            <button type="button"
                    id="delete-chatbot"
                    class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition duration-200"
                    {{ !$chatbotData->exists ? 'style=display:none' : '' }}>
                Delete Configuration
            </button>

            <div class="space-x-4">
                <button type="button"
                        id="save-draft"
                        class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition duration-200">
                    Save Draft
                </button>
                <button type="submit"
                        id="save-and-activate"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition duration-200"
                        {{ !$canActivateChatbot ? 'disabled' : '' }}>
                    Save & Activate
                </button>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Initializing chatbot form...');

    const form = document.getElementById('chatbot-form');
    const activeToggle = document.getElementById('chatbot-active');
    const completionPercentage = document.getElementById('completion-percentage');
    const saveAndActivateBtn = document.getElementById('save-and-activate');
    const saveDraftBtn = document.getElementById('save-draft');
    const deleteChatbotBtn = document.getElementById('delete-chatbot');

    // Debug: Check if all elements are found
    console.log('📋 Form elements check:');
    console.log('  form:', form ? '✅ Found' : '❌ Not found');
    console.log('  activeToggle:', activeToggle ? '✅ Found' : '❌ Not found');
    console.log('  completionPercentage:', completionPercentage ? '✅ Found' : '❌ Not found');
    console.log('  saveAndActivateBtn:', saveAndActivateBtn ? '✅ Found' : '❌ Not found');
    console.log('  saveDraftBtn:', saveDraftBtn ? '✅ Found' : '❌ Not found');
    console.log('  deleteChatbotBtn:', deleteChatbotBtn ? '✅ Found' : '❌ Not found');

    if (!form) {
        console.error('❌ Form not found! Cannot initialize chatbot functionality.');
        return;
    }

    // Auto-save functionality
    let autoSaveTimeout;
    const textareas = form.querySelectorAll('textarea');

    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                saveDraft();
            }, 2000); // Auto-save after 2 seconds of inactivity
        });
    });

    // Save draft function
    function saveDraft(shouldRedirect = false) {
        console.log('🔄 Starting save draft...');

        const formData = new FormData(form);
        formData.set('is_active', false); // Always save as draft

        // Debug: Log form data
        console.log('📝 Form data:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            console.error('❌ CSRF token not found!');
            showNotification('CSRF token missing', 'error');
            return;
        }

        console.log('🔑 CSRF token found:', csrfToken.getAttribute('content').substring(0, 10) + '...');

        fetch(`/seller/products/{{ $product->id }}/chatbot`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: formData
        })
        .then(response => {
            console.log('📡 Response status:', response.status);
            console.log('📡 Response headers:', response.headers.get('content-type'));

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Server returned non-JSON response (possibly a redirect or error page)');
            }

            return response.json();
        })
        .then(data => {
            console.log('✅ Response data:', data);
            if (data.success) {
                completionPercentage.textContent = data.completion_percentage + '%';

                if (shouldRedirect) {
                    showNotification('Draft saved successfully!', 'success');
                    setTimeout(() => {
                        window.location.href = '{{ route("seller.products.edit", $product) }}';
                    }, 1500);
                } else {
                    // No notification for auto-save to avoid spam
                    console.log('✅ Auto-save successful');
                }
            } else {
                console.error('❌ Save failed:', data);
                let errorMessage = data.error || 'Save failed';

                // Show validation errors if available
                if (data.errors) {
                    console.error('Validation errors:', data.errors);
                    const errorList = Object.values(data.errors).flat();
                    errorMessage = errorList.join(', ');
                }

                showNotification(errorMessage, 'error');
            }
        })
        .catch(error => {
            console.error('❌ Auto-save error:', error);
            showNotification('Error saving draft: ' + error.message, 'error');
        });
    }

    // Save draft button
    saveDraftBtn.addEventListener('click', function() {
        saveDraft(true); // Pass true to indicate manual save (should redirect)
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('🚀 Starting form submission (Save & Activate)...');

        const formData = new FormData(form);
        formData.set('is_active', true); // Activate when submitting

        // Debug: Log form data
        console.log('📝 Form data for activation:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            console.error('❌ CSRF token not found!');
            showNotification('CSRF token missing', 'error');
            return;
        }

        fetch(`/seller/products/{{ $product->id }}/chatbot`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            body: formData
        })
        .then(response => {
            console.log('📡 Activation response status:', response.status);
            console.log('📡 Activation response headers:', response.headers.get('content-type'));

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Server returned non-JSON response (possibly a redirect or error page)');
            }

            return response.json();
        })
        .then(data => {
            console.log('✅ Activation response data:', data);
            if (data.success) {
                showNotification(data.message, 'success');
                completionPercentage.textContent = data.completion_percentage + '%';
                activeToggle.checked = data.is_active;
                deleteChatbotBtn.style.display = 'block';

                // Redirect to product edit page after successful activation
                setTimeout(() => {
                    window.location.href = '{{ route("seller.products.edit", $product) }}';
                }, 1500);
            } else {
                console.error('❌ Activation failed:', data);
                let errorMessage = data.error || 'An error occurred';

                // Show validation errors if available
                if (data.errors) {
                    console.error('Validation errors:', data.errors);
                    const errorList = Object.values(data.errors).flat();
                    errorMessage = errorList.join(', ');
                }

                showNotification(errorMessage, 'error');
                if (data.upgrade_needed) {
                    setTimeout(() => {
                        window.location.href = '/membership';
                    }, 2000);
                }
            }
        })
        .catch(error => {
            console.error('❌ Activation error:', error);
            showNotification('Error during activation: ' + error.message, 'error');
        });
    });

    // Function to update toggle text
    function updateToggleText() {
        const toggleText = document.getElementById('toggle-text');
        if (activeToggle.checked) {
            toggleText.textContent = 'Active';
            toggleText.className = 'ml-3 text-sm font-medium text-green-600';
        } else {
            toggleText.textContent = 'Inactive';
            toggleText.className = 'ml-3 text-sm font-medium text-gray-500';
        }
    }

    // Toggle activation
    activeToggle.addEventListener('change', function() {
        const isActive = this.checked;
        updateToggleText(); // Update text immediately

        fetch(`/seller/products/{{ $product->id }}/chatbot/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ is_active: isActive })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
            } else {
                this.checked = !isActive; // Revert toggle
                updateToggleText(); // Update text after revert
                showNotification(data.error || 'An error occurred', 'error');
                if (data.upgrade_needed) {
                    setTimeout(() => {
                        window.location.href = '/membership';
                    }, 2000);
                }
            }
        })
        .catch(error => {
            this.checked = !isActive; // Revert toggle
            updateToggleText(); // Update text after revert
            console.error('Error:', error);
            showNotification('An error occurred', 'error');
        });
    });

    // Delete configuration
    deleteChatbotBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to delete this chatbot configuration? This action cannot be undone.')) {
            fetch(`/seller/products/{{ $product->id }}/chatbot`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '{{ route("seller.products.edit", $product) }}';
                    }, 1500);
                } else {
                    showNotification(data.error || 'An error occurred', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred', 'error');
            });
        }
    });

    // Notification function
    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
});
</script>
@endsection
