/* Modern LMS Design System */
:root {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --success-color: #10b981;
    --success-hover: #059669;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.udemy-course-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 2rem !important;
    background: var(--gray-50) !important;
    min-height: 100vh !important;
}

/* Course Header */
.course-header {
    background: white !important;
    border-radius: var(--border-radius) !important;
    border: 1px solid var(--gray-200) !important;
    box-shadow: var(--shadow-sm) !important;
    margin-bottom: 2rem !important;
    overflow: hidden !important;
}

.course-header-content {
    padding: 2rem !important;
    display: flex !important;
    align-items: flex-start !important;
    justify-content: space-between !important;
    gap: 2rem !important;
}

.course-header-info {
    flex: 1 !important;
}

.course-title {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: var(--gray-900) !important;
    margin: 0 0 0.5rem 0 !important;
    line-height: 1.2 !important;
}

.course-subtitle {
    font-size: 1rem !important;
    color: var(--gray-600) !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

.course-header-actions {
    display: flex !important;
    gap: 1rem !important;
    align-items: center !important;
    flex-shrink: 0 !important;
}

/* Modern Button System */
.btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: var(--border-radius) !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    text-decoration: none !important;
    transition: all 0.15s ease !important;
    border: 1px solid transparent !important;
    cursor: pointer !important;
    white-space: nowrap !important;
}

.btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md) !important;
}

.btn-outline {
    background: transparent !important;
    color: var(--primary-color) !important;
    border: 1px solid var(--primary-color) !important;
}

.btn-outline:hover {
    background: var(--primary-color) !important;
    color: white !important;
}

.btn-secondary {
    background: white !important;
    color: var(--gray-700) !important;
    border: 1px solid var(--gray-300) !important;
}

.btn-secondary:hover {
    background: var(--gray-50) !important;
    border-color: var(--gray-400) !important;
}

.btn-success {
    background: var(--success-color) !important;
    color: white !important;
    border-color: var(--success-color) !important;
}

.btn-success:hover {
    background: var(--success-hover) !important;
    border-color: var(--success-hover) !important;
}

.btn-danger {
    background: var(--danger-color) !important;
    color: white !important;
    border-color: var(--danger-color) !important;
}

.btn-danger:hover {
    background: var(--danger-hover) !important;
    border-color: var(--danger-hover) !important;
}

.btn-sm {
    padding: 0.5rem 1rem !important;
    font-size: 0.75rem !important;
}

.btn-xs {
    padding: 0.25rem 0.75rem !important;
    font-size: 0.625rem !important;
}

/* Status Badge System */
.status-badge, .item-status {
    display: inline-flex !important;
    align-items: center !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 20px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.025em !important;
}

.status-active {
    background: #d1fae5 !important;
    color: #065f46 !important;
    border: 1px solid #a7f3d0 !important;
}

.status-inactive {
    background: #fee2e2 !important;
    color: #991b1b !important;
    border: 1px solid #fca5a5 !important;
}

.status-draft {
    background: #fef3c7 !important;
    color: #92400e !important;
    border: 1px solid #fde68a !important;
}

.section-status {
     margin-top: 1rem !important;
     display: flex !important;
     align-items: center !important;
     gap: 0.75rem !important;
 }
 
 .item-status {
     margin-left: 0.5rem !important;
 }
 
 /* Curriculum Section */
 .curriculum-section {
     background: white !important;
     border-radius: var(--border-radius) !important;
     border: 1px solid var(--gray-200) !important;
     box-shadow: var(--shadow-sm) !important;
     overflow: hidden !important;
 }
 
 .curriculum-header {
     padding: 2rem !important;
     border-bottom: 1px solid var(--gray-200) !important;
     background: var(--gray-50) !important;
     display: flex !important;
     justify-content: space-between !important;
     align-items: center !important;
 }
 
 .curriculum-title {
     font-size: 1.5rem !important;
     font-weight: 600 !important;
     color: var(--gray-900) !important;
     margin: 0 !important;
 }
 
 .curriculum-stats {
     font-size: 0.875rem !important;
     color: var(--gray-600) !important;
     font-weight: 500 !important;
 }
 
 .curriculum-content {
     padding: 0 !important;
 }
 
 /* Section Styling */
 .udemy-section {
     border-bottom: 1px solid var(--gray-200) !important;
 }
 
 .udemy-section:last-child {
     border-bottom: none !important;
 }
 
 .section-header {
     padding: 2rem !important;
     background: var(--gray-50) !important;
     border-bottom: 1px solid var(--gray-200) !important;
     display: flex !important;
     justify-content: space-between !important;
     align-items: flex-start !important;
 }
 
 .section-info {
     flex: 1 !important;
 }
 
 .section-number {
     font-size: 0.875rem !important;
     font-weight: 600 !important;
     color: var(--primary-color) !important;
     text-transform: uppercase !important;
     letter-spacing: 0.025em !important;
     margin-bottom: 0.5rem !important;
 }
 
 .section-title {
     font-size: 1.25rem !important;
     font-weight: 600 !important;
     color: var(--gray-900) !important;
     margin: 0 0 0.5rem 0 !important;
     line-height: 1.3 !important;
 }
 
 .section-description {
     color: var(--gray-600) !important;
     line-height: 1.5 !important;
     margin: 0 0 1rem 0 !important;
     font-size: 0.875rem !important;
 }
 
 .section-actions {
     display: flex !important;
     gap: 0.5rem !important;
     flex-shrink: 0 !important;
 }
 
 .action-btn {
     display: flex !important;
     align-items: center !important;
     justify-content: center !important;
     width: 2.5rem !important;
     height: 2.5rem !important;
     border-radius: 8px !important;
     border: 1px solid var(--gray-300) !important;
     background: white !important;
     color: var(--gray-600) !important;
     cursor: pointer !important;
     transition: all 0.15s ease !important;
 }
 
 .action-btn:hover {
     background: var(--gray-50) !important;
     border-color: var(--gray-400) !important;
     color: var(--gray-700) !important;
 }
 
 .edit-btn:hover {
     background: var(--primary-color) !important;
     border-color: var(--primary-color) !important;
     color: white !important;
 }
 
 .delete-btn:hover {
     background: var(--danger-color) !important;
     border-color: var(--danger-color) !important;
     color: white !important;
 }
 
 /* Curriculum Items */
 .curriculum-items {
     background: white !important;
 }
 
 .curriculum-item {
     padding: 1.5rem 2rem !important;
     border-bottom: 1px solid var(--gray-100) !important;
     display: flex !important;
     justify-content: space-between !important;
     align-items: center !important;
     transition: background-color 0.15s ease !important;
 }
 
 .curriculum-item:hover {
     background: var(--gray-50) !important;
 }
 
 .curriculum-item:last-child {
     border-bottom: none !important;
 }
 
 .item-content {
     display: flex !important;
     align-items: center !important;
     gap: 1rem !important;
     flex: 1 !important;
 }
 
 .item-icon {
     display: flex !important;
     align-items: center !important;
     justify-content: center !important;
     width: 2.5rem !important;
     height: 2.5rem !important;
     border-radius: 8px !important;
     background: var(--gray-100) !important;
     color: var(--gray-600) !important;
     flex-shrink: 0 !important;
 }
 
 .item-type-lecture {
     background: #dbeafe !important;
     color: #1d4ed8 !important;
 }
 
 .item-type-video {
     background: #fef3c7 !important;
     color: #d97706 !important;
 }
 
 .item-type-pdf {
     background: #fee2e2 !important;
     color: #dc2626 !important;
 }
 
 .item-details {
     flex: 1 !important;
 }
 
 .item-title {
     font-size: 1rem !important;
     font-weight: 600 !important;
     color: var(--gray-900) !important;
     margin: 0 0 0.5rem 0 !important;
     line-height: 1.3 !important;
 }
 
 .item-meta {
     display: flex !important;
     align-items: center !important;
     gap: 0.75rem !important;
     flex-wrap: wrap !important;
 }
 
 .item-type, .item-duration, .item-preview {
     font-size: 0.75rem !important;
     font-weight: 500 !important;
     color: var(--gray-600) !important;
     background: var(--gray-100) !important;
     padding: 0.25rem 0.5rem !important;
     border-radius: 4px !important;
 }
 
 .item-preview {
     background: #dbeafe !important;
     color: #1d4ed8 !important;
 }
 
 .item-actions {
     display: flex !important;
     gap: 0.5rem !important;
     flex-shrink: 0 !important;
 }
 
 /* Add Buttons */
 .add-section-container, .add-curriculum-item-container {
     padding: 2rem !important;
     text-align: center !important;
     border-top: 1px solid var(--gray-200) !important;
 }
 
 .add-section-btn, .add-curriculum-item-btn {
     display: inline-flex !important;
     align-items: center !important;
     gap: 0.5rem !important;
     padding: 1rem 2rem !important;
     border: 2px dashed var(--gray-300) !important;
     border-radius: var(--border-radius) !important;
     background: transparent !important;
     color: var(--gray-600) !important;
     font-weight: 600 !important;
     cursor: pointer !important;
     transition: all 0.15s ease !important;
 }
 
 .add-section-btn:hover, .add-curriculum-item-btn:hover {
     border-color: var(--primary-color) !important;
     color: var(--primary-color) !important;
     background: var(--gray-50) !important;
 }
 
 /* Empty State */
 .empty-state {
     padding: 4rem 2rem !important;
     text-align: center !important;
 }
 
 .empty-state-content {
     max-width: 400px !important;
     margin: 0 auto !important;
 }
 
 .empty-state-icon {
     width: 4rem !important;
     height: 4rem !important;
     color: var(--gray-400) !important;
     margin: 0 auto 1.5rem auto !important;
 }
 
 .empty-state-title {
     font-size: 1.25rem !important;
     font-weight: 600 !important;
     color: var(--gray-900) !important;
     margin: 0 0 0.5rem 0 !important;
 }
 
 .empty-state-description {
     color: var(--gray-600) !important;
     line-height: 1.5 !important;
     margin: 0 !important;
 }

/* Course Overview Card */
.course-overview-card {
    background: white !important;
    border-radius: var(--border-radius) !important;
    border: 1px solid var(--gray-200) !important;
    box-shadow: var(--shadow-sm) !important;
    margin-bottom: 2rem !important;
    overflow: hidden !important;
}

.course-overview-content {
    padding: 2rem !important;
    display: flex !important;
    gap: 2rem !important;
    align-items: flex-start !important;
}

.course-thumbnail {
    flex-shrink: 0 !important;
    width: 200px !important;
    height: 120px !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    background: var(--gray-100) !important;
}

.thumbnail-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

.course-details {
    flex: 1 !important;
}

.course-overview-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: var(--gray-900) !important;
    margin: 0 0 1rem 0 !important;
}

.course-overview-description {
    color: var(--gray-700) !important;
    line-height: 1.6 !important;
    margin-bottom: 1.5rem !important;
    font-size: 1rem !important;
}

.course-meta-stats {
    display: flex !important;
    gap: 1rem !important;
    flex-wrap: wrap !important;
}

.meta-badge {
    background: var(--gray-100) !important;
    color: var(--gray-700) !important;
    padding: 0.5rem 1rem !important;
    border-radius: 20px !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border: 1px solid var(--gray-200) !important;
}

/* Toggle Switch Styles */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--success-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--success-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Small switch */
.switch-sm {
  width: 40px;
  height: 24px;
}

.switch-sm .slider:before {
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
}

.switch-sm input:checked + .slider:before {
  transform: translateX(16px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}