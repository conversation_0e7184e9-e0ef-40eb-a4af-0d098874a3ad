@extends('seller.layouts.app')

@push('scripts')
    <script>
        // Pass course data to JavaScript for loading existing data
        const courseData = @json($courseData ?? []);
    </script>
    <script src="{{ asset(js_path() . '/course-structure-combined.js') . '?v=' . strtotime('now') }}" defer></script>
@endpush

@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center gap-4">
            <a href="{{ route('seller.products.create-course-step', ['step' => 1]) }}"
                class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
                <span class="sr-only">Back</span>
            </a>
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Create New Course</h1>
                <p class="text-gray-600">Step 2 of 2: Course Structure & Content</p>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="bg-white rounded-lg border shadow-sm p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full text-sm font-medium">
                        ✓
                    </div>
                    <span class="text-sm font-medium text-green-600">Basic Information</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">
                        2
                    </div>
                    <span class="text-sm font-medium text-blue-600">Course Structure & Content</span>
                </div>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 100%"></div>
            </div>
        </div>

        <!-- Course Info Summary -->
        @if(isset($courseData['step1']))
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-lg font-medium text-blue-900">{{ $courseData['step1']['name'] }}</h3>
            <p class="text-sm text-blue-700 mt-1">{{ Str::limit($courseData['step1']['description'], 100) }}</p>
            <p class="text-sm text-blue-600 mt-2">Price: Rp {{ number_format($courseData['step1']['price'], 0, ',', '.') }}</p>
        </div>
        @endif

        <!-- Form -->
        <form action="{{ route('seller.products.save-course-step', ['step' => 2]) }}" method="POST" enctype="multipart/form-data" id="course-structure-form">
            @csrf
            
            <div class="space-y-6">
                <!-- Course Structure & Content -->
                <div class="rounded-xl border bg-white shadow-lg">
                    <div class="border-b border-gray-100 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">Course Structure & Content</h3>
                                <p class="text-sm text-gray-600">Organize your course into chapters and lessons, then add content to each lesson</p>
                            </div>
                            <button type="button" id="add-chapter-btn"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add Chapter
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="chapters-container" class="space-y-6">
                            <!-- Chapters will be added here dynamically -->
                        </div>
                        
                        <!-- Empty State -->
                        <div id="empty-state" class="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No chapters yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating your first chapter</p>
                            <div class="mt-6">
                                <button type="button" onclick="document.getElementById('add-chapter-btn').click()"
                                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add Chapter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center justify-between">
                    <a href="{{ route('seller.products.create-course-step', ['step' => 1]) }}"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Back to Step 1
                    </a>
                    
                    <div class="flex items-center space-x-3">
                        <button type="button" id="save-draft-btn"
                            class="inline-flex items-center px-4 py-2 border border-amber-300 shadow-sm text-sm font-medium rounded-md text-amber-700 bg-amber-50 hover:bg-amber-100">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Save as Draft
                        </button>
                        <button type="button" id="cancel-btn"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" id="create-course-btn"
                            class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Create Course
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Chapter Template (Hidden) -->
    <template id="chapter-template">
        <div class="chapter-item border border-gray-200 rounded-lg bg-white" data-chapter-index="">
            <div class="border-b border-gray-200 p-4 bg-gray-50">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-gray-500 chapter-number">1</span>
                        <span class="text-sm font-medium text-gray-700">Chapter <span class="chapter-display-number">1</span></span>
                    </div>
                    <div class="flex items-center gap-1">
                        <button type="button" class="move-chapter-up text-gray-400 hover:text-gray-600 text-sm" disabled>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                            </svg>
                        </button>
                        <button type="button" class="move-chapter-down text-gray-400 hover:text-gray-600 text-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <button type="button" class="remove-chapter text-red-500 hover:text-red-700 text-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                        <input type="text" name="chapters[0][title]" 
                               class="chapter-title w-full rounded border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                               placeholder="Chapter title" required>
                    </div>
                    <div>
                        <input type="text" name="chapters[0][description]" 
                               class="chapter-description w-full rounded border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                               placeholder="Chapter description">
                    </div>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="text-sm font-medium text-gray-700">Lessons</h4>
                    <button type="button" class="add-lesson-btn text-sm text-blue-600 hover:text-blue-700 font-medium">
                        + Add Lesson
                    </button>
                </div>
                <div class="lessons-container space-y-3">
                    <!-- Lessons will be added here -->
                </div>
            </div>
        </div>
    </template>

    <!-- Lesson Template (Hidden) -->
    <template id="lesson-template">
        <div class="lesson-item border border-gray-200 rounded-lg bg-gray-50" data-lesson-index="">
            <div class="border-b border-gray-200 p-3 bg-gray-100">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center gap-2">
                        <span class="text-xs text-gray-500 lesson-number">1.</span>
                        <span class="text-sm font-medium text-gray-700">Lesson <span class="lesson-display-number">1</span></span>
                    </div>
                    <div class="flex items-center gap-1">
                        <button type="button" class="move-lesson-up text-gray-400 hover:text-gray-600 text-xs" disabled>
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                            </svg>
                        </button>
                        <button type="button" class="move-lesson-down text-gray-400 hover:text-gray-600 text-xs">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <button type="button" class="remove-lesson text-red-500 hover:text-red-700 text-xs">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div>
                        <input type="text" name="chapters[0][sub_chapters][0][title]" 
                               class="lesson-title w-full rounded border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                               placeholder="Lesson title" required>
                    </div>
                    <div>
                        <input type="text" name="chapters[0][sub_chapters][0][description]" 
                               class="lesson-description w-full rounded border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                               placeholder="Lesson description">
                    </div>
                </div>
            </div>
            <div class="p-3">
                <div class="flex items-center justify-between mb-3">
                    <h5 class="text-sm font-medium text-gray-600">Lesson Content</h5>
                    <button type="button" class="add-resource-btn text-sm text-blue-600 hover:text-blue-700 font-medium">
                        + Add Resource
                    </button>
                </div>
                <div class="resources-container space-y-3">
                    <!-- Resources will be added here -->
                    <div class="empty-resources text-center py-4 text-gray-500 border-2 border-dashed border-gray-200 rounded">
                        <svg class="mx-auto h-6 w-6 text-gray-400 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <p class="text-xs">No resources added yet</p>
                        <button type="button" class="add-resource-btn text-xs text-blue-600 hover:text-blue-700 font-medium mt-1">
                            Add your first resource
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- Resource Template (Hidden) -->
    <template id="resource-template">
        <div class="resource-item border border-gray-200 rounded p-3 bg-white" data-resource-index="">
            <div class="flex items-center justify-between mb-3">
                <h6 class="text-sm font-medium text-gray-700">Resource <span class="resource-number">1</span></h6>
                <button type="button" class="remove-resource text-red-500 hover:text-red-700 text-sm">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Resource Title *</label>
                    <input type="text" name="content[0][resources][0][title]"
                           class="resource-title w-full rounded border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                           placeholder="e.g., Introduction Video" required>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Resource Type *</label>
                    <select name="content[0][resources][0][type]"
                            class="resource-type w-full rounded border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm" required>
                        <option value="">Select type...</option>
                        <option value="video">Video Link</option>
                        <option value="text">Text Content</option>
                        <option value="file">File Upload</option>
                        <option value="pdf">PDF Document</option>
                        <option value="link">External Link</option>
                        <option value="audio">Audio File</option>
                        <option value="image">Image</option>
                    </select>
                </div>
            </div>

            <div class="mb-3">
                <label class="block text-xs font-medium text-gray-700 mb-1">Description</label>
                <input type="text" name="content[0][resources][0][description]"
                       class="resource-description w-full rounded border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
                       placeholder="Brief description of this resource">
            </div>

            <div class="resource-content-area">
                <!-- Dynamic content based on resource type will be inserted here -->
            </div>

            <div class="flex items-center gap-4 mt-3">
                <label class="flex items-center">
                    <input type="checkbox" name="content[0][resources][0][is_preview]" value="1"
                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <span class="ml-2 text-xs text-gray-600">Preview (accessible before purchase)</span>
                </label>
            </div>

            <!-- Hidden fields for indexing -->
            <input type="hidden" name="content[0][chapter_index]" class="chapter-index-input" value="0">
            <input type="hidden" name="content[0][sub_chapter_index]" class="sub-chapter-index-input" value="0">
        </div>
    </template>

    <script>
        // Global variables for course step 2
        let isFormDirty = false;
        let courseForm = null;

        // Global function for saving course as draft
        function saveCourseAsDraft(showNotification = true) {
            // First save the current form data to session
            if (courseForm) {
                const formData = new FormData(courseForm);

                // Submit the form data to save to session first
                fetch(courseForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                }).then(() => {
                    // Then save as draft
                    return fetch('{{ route('seller.products.save-course-as-draft') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });
                }).then(response => response.json())
                .then(data => {
                    if (data.success) {
                        isFormDirty = false;
                        if (showNotification) {
                            alert('Course saved as draft successfully!');
                            window.location.href = data.redirect_url;
                        }
                    } else {
                        if (showNotification) {
                            alert('Failed to save draft: ' + data.message);
                        }
                    }
                }).catch(error => {
                    console.error('Error saving draft:', error);
                    if (showNotification) {
                        alert('Error saving draft. Please try again.');
                    }
                });
            }
        }

        // Global function for handling cancel with confirmation
        function handleCancel() {
            if (isFormDirty) {
                const userChoice = confirm(
                    'You have unsaved changes. What would you like to do?\n\n' +
                    'Click "OK" to save as draft and exit\n' +
                    'Click "Cancel" to discard changes and exit'
                );

                if (userChoice) {
                    // User chose to save as draft
                    saveCourseAsDraft();
                } else {
                    // User chose to discard changes
                    if (confirm('Are you sure you want to discard all changes? This cannot be undone.')) {
                        isFormDirty = false;
                        window.location.href = '{{ route('seller.products.select-type') }}';
                    }
                }
            } else {
                // No changes, just redirect
                window.location.href = '{{ route('seller.products.select-type') }}';
            }
        }

        // Auto-save and draft functionality for step 2
        document.addEventListener('DOMContentLoaded', function() {
            const saveDraftBtn = document.getElementById('save-draft-btn');
            const cancelBtn = document.getElementById('cancel-btn');
            courseForm = document.getElementById('course-structure-form');

            // Track form changes
            if (courseForm) {
                courseForm.addEventListener('input', function() {
                    isFormDirty = true;
                });
            }

            // Save as draft button
            if (saveDraftBtn) {
                saveDraftBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    saveCourseAsDraft();
                });
            }

            // Cancel button with confirmation
            if (cancelBtn) {
                cancelBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleCancel();
                });
            }

            // Auto-save on page unload if form is dirty
            window.addEventListener('beforeunload', function(e) {
                if (isFormDirty && courseForm) {
                    // Try to save as draft before leaving
                    saveCourseAsDraft(false); // Don't show notifications on auto-save
                }
            });
        });
    </script>
@endsection
