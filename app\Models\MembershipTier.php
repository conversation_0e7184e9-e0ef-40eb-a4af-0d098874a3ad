<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MembershipTier extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'slug',
        'price',
        'daily_ai_prompts',
        'unlimited_products',
        'ai_tools_access',
        'chatbot_products_limit',
        'sales_analytics',
        'priority_support',
        'custom_listing',
        'digital_product_transaction_fee',
        'course_transaction_fee',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'integer',
        'daily_ai_prompts' => 'integer',
        'unlimited_products' => 'boolean',
        'ai_tools_access' => 'boolean',
        'chatbot_products_limit' => 'integer',
        'sales_analytics' => 'boolean',
        'priority_support' => 'boolean',
        'custom_listing' => 'boolean',
        'digital_product_transaction_fee' => 'integer',
        'course_transaction_fee' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get all user memberships for this tier.
     */
    public function userMemberships()
    {
        return $this->hasMany(UserMembership::class);
    }

    /**
     * Check if this tier has unlimited AI prompts.
     * Note: No tier has unlimited prompts anymore, Pro tier has 200 daily prompts.
     */
    public function hasUnlimitedAiPrompts()
    {
        return false; // No tier has unlimited prompts anymore
    }

    /**
     * Check if this tier has unlimited chatbot products.
     */
    public function hasUnlimitedChatbotProducts()
    {
        return $this->chatbot_products_limit === -1;
    }

    /**
     * Get formatted price for display.
     */
    public function getFormattedPriceAttribute()
    {
        if ($this->price == 0) {
            return 'Gratis';
        }

        return 'Rp ' . number_format($this->price, 0, ',', '.') . '/Bulan';
    }

    /**
     * Scope to get active tiers only.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->orderBy('sort_order');
    }
}
