@extends('layouts.main')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-white flex items-center justify-center py-6 px-4">
        <div class="max-w-md w-full bg-white border-0 shadow-lg rounded-xl mx-auto">
            <div class="text-center p-6">
                <div class="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-red-100">
                    <svg class="h-12 w-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Application Rejected</h2>
            </div>
            <div class="px-6 pb-6 space-y-4 text-center">
                <p class="text-gray-600">
                    We're sorry, but your seller application has been rejected. You can reapply by submitting a new application.
                </p>
                <div class="rounded-lg bg-indigo-50 p-4 text-left">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Tips for Reapplying</h3>
                    <ul class="space-y-2 text-sm text-gray-600 list-disc list-inside">
                        <li>Ensure all information is accurate and complete</li>
                        <li>Upload clear and legible identification documents</li>
                        <li>Provide a detailed and professional store description</li>
                    </ul>
                </div>
            </div>
            <div class="p-6 flex flex-col space-y-4">
                <a href="{{ route('seller.reapply') }}" class="block w-full bg-indigo-600 text-white px-4 py-3 rounded-md hover:bg-indigo-700 transition-colors duration-200 text-center font-medium" style="background-color: #4f46e5;">
                    Reapply Now
                </a>
                <a href="{{ route('dashboard') }}" class="block w-full border border-gray-300 text-gray-700 px-4 py-3 rounded-md hover:bg-gray-50 transition-colors duration-200 text-center font-medium">
                    Return to Homepage
                </a>
            </div>
        </div>
    </div>
@endsection