@extends('layouts.main')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl font-extrabold text-gray-900 mb-4 animate-fade-in">Frequently Asked Questions</h1>
            <p class="text-gray-600 text-lg max-w-2xl mx-auto animate-fade-in animation-delay-200">
                Find answers to the most common questions about Digit<PERSON>.
            </p>
        </div>

        <!-- Search Section -->
        <div class="max-w-3xl mx-auto mb-12">
            <form action="{{ route('faq') }}" method="GET" class="relative">
                <div class="flex items-center">
                    <input type="text" name="search" value="{{ request('search') }}" placeholder="Search for questions..."
                        class="block w-full rounded-l-full border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500 py-3 px-6">
                    <button type="submit"
                        class="bg-purple-600 text-white px-6 py-3 rounded-r-full font-medium hover:bg-purple-700 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>

        <!-- FAQ Categories -->
        <div class="max-w-5xl mx-auto mb-8">
            <div class="flex flex-wrap justify-center gap-4 mb-8">
                <a href="{{ route('faq') }}" class="px-5 py-2 rounded-full {{ !request('category') ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                    All Questions
                </a>
                <a href="{{ route('faq', ['category' => 'general']) }}" class="px-5 py-2 rounded-full {{ request('category') == 'general' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                    General
                </a>
                <a href="{{ route('faq', ['category' => 'sellers']) }}" class="px-5 py-2 rounded-full {{ request('category') == 'sellers' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                    For Sellers
                </a>
                <a href="{{ route('faq', ['category' => 'buyers']) }}" class="px-5 py-2 rounded-full {{ request('category') == 'buyers' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                    For Buyers
                </a>
                <a href="{{ route('faq', ['category' => 'payments']) }}" class="px-5 py-2 rounded-full {{ request('category') == 'payments' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                    Payments
                </a>
                <a href="{{ route('faq', ['category' => 'technical']) }}" class="px-5 py-2 rounded-full {{ request('category') == 'technical' ? 'bg-purple-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                    Technical
                </a>
            </div>
        </div>

        <!-- FAQ Accordion -->
        <div class="max-w-3xl mx-auto">
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="divide-y divide-gray-200">
                    @forelse($faqs as $index => $faq)
                        <div x-data="{ open: false }" class="border-b border-gray-200 last:border-b-0">
                            <button @click="open = !open" class="flex justify-between items-center w-full px-6 py-5 text-left">
                                <span class="text-lg font-semibold text-gray-900">{{ $faq->question }}</span>
                                <svg x-show="!open" class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                                <svg x-show="open" class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                </svg>
                            </button>
                            <div x-show="open" class="px-6 pb-5" style="display: none;">
                                <p class="text-gray-600">{{ $faq->answer }}</p>
                            </div>
                        </div>
                    @empty
                        <div class="p-8 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-lg font-medium text-gray-900">No FAQs found</h3>
                            <p class="mt-1 text-gray-500">Try a different search term or category.</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Still Have Questions -->
            <div class="mt-12 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-8 text-center text-white">
                <h2 class="text-2xl font-bold mb-4">Still Have Questions?</h2>
                <p class="mb-6 max-w-2xl mx-auto">Can't find what you're looking for? Our support team is here to help.</p>
                <div class="flex flex-col sm:flex-row justify-center sm:space-x-4 space-y-4 sm:space-y-0">
                    <a href="{{ route('help') }}" class="inline-block bg-white text-purple-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
                        Browse Help Center
                    </a>
                    <a href="{{ route('contact') }}" class="inline-block border-2 border-white text-white px-6 py-3 rounded-full font-medium hover:bg-white hover:text-purple-600 transition-colors duration-300">
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
@endpush
@endsection
