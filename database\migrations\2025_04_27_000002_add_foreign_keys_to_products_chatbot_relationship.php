<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraint from products to product_chatbot_data
        Schema::table('products', function (Blueprint $table) {
            $table->foreign('chatbot_data_id')
                  ->references('id')
                  ->on('product_chatbot_data')
                  ->onUpdate('cascade')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if the table and foreign key exist before trying to drop
        if (Schema::hasTable('products')) {
            Schema::table('products', function (Blueprint $table) {
                // <PERSON><PERSON> will handle checking if the foreign key exists
                $table->dropForeign(['chatbot_data_id']);
            });
        }
    }
};
