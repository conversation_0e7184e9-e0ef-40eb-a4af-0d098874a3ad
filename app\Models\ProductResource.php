<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ProductResource extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'product_id',
        'title',
        'description',
        'type',
        'content',
        'metadata',
        'sort_order',
        'is_active',
        'is_preview',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_active' => 'boolean',
        'is_preview' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Resource type constants
    const TYPE_FILE = 'file';
    const TYPE_LINK = 'link';
    const TYPE_PDF = 'pdf';
    const TYPE_TEXT = 'text';
    const TYPE_VIDEO = 'video';
    const TYPE_AUDIO = 'audio';
    const TYPE_DOCUMENT = 'document';
    const TYPE_IMAGE = 'image';

    /**
     * Get all available resource types
     */
    public static function getResourceTypes(): array
    {
        return [
            self::TYPE_FILE => 'File Upload',
            self::TYPE_LINK => 'External Link',
            self::TYPE_PDF => 'PDF Document',
            self::TYPE_TEXT => 'Text Content',
            self::TYPE_VIDEO => 'Video Link',
            self::TYPE_AUDIO => 'Audio File',
            self::TYPE_DOCUMENT => 'Document',
            self::TYPE_IMAGE => 'Image',
        ];
    }

    /**
     * Get the product that owns this resource
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Scope to get only active resources
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get resources ordered by sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope to get preview resources
     */
    public function scopePreview($query)
    {
        return $query->where('is_preview', true);
    }

    /**
     * Check if this resource is a file type
     */
    public function isFileType(): bool
    {
        return in_array($this->type, [self::TYPE_FILE, self::TYPE_PDF, self::TYPE_AUDIO, self::TYPE_DOCUMENT, self::TYPE_IMAGE]);
    }

    /**
     * Check if this resource is a link type
     */
    public function isLinkType(): bool
    {
        return in_array($this->type, [self::TYPE_LINK, self::TYPE_VIDEO]);
    }

    /**
     * Get the file size in human readable format
     */
    public function getFileSizeAttribute(): ?string
    {
        if (!$this->isFileType() || !isset($this->metadata['size'])) {
            return null;
        }

        $bytes = $this->metadata['size'];
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the download URL for file resources
     */
    public function getDownloadUrlAttribute(): ?string
    {
        if (!$this->isFileType() || !$this->content) {
            return null;
        }

        return Storage::url($this->content);
    }

    /**
     * Get the display content based on resource type
     */
    public function getDisplayContentAttribute(): string
    {
        switch ($this->type) {
            case self::TYPE_TEXT:
                return $this->content ?? '';
            case self::TYPE_LINK:
            case self::TYPE_VIDEO:
                return $this->content ?? '';
            case self::TYPE_FILE:
            case self::TYPE_PDF:
            case self::TYPE_AUDIO:
            case self::TYPE_DOCUMENT:
            case self::TYPE_IMAGE:
                return $this->content ? basename($this->content) : '';
            default:
                return $this->content ?? '';
        }
    }

    /**
     * Check if the resource file exists (for file types)
     */
    public function fileExists(): bool
    {
        if (!$this->isFileType() || !$this->content) {
            return false;
        }

        return Storage::exists($this->content);
    }

    /**
     * Delete the associated file when the resource is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($resource) {
            if ($resource->isFileType() && $resource->content && Storage::exists($resource->content)) {
                Storage::delete($resource->content);
            }
        });
    }
}
