<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-QWR2LGRD93');
    </script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Digitora') }} - User Dashboard</title>
    <link rel="icon" type="image/x-icon" href="{{ asset('images/digitora-logo.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- AI Chat CSS -->
    <link rel="stylesheet" href="{{ asset('css/ai-chat.css') }}">
    <link href="{{ asset('css/dashboard-user.css') }}" rel="stylesheet">

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    {{-- SWEET ALERT 2 --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>

<body class="font-sans antialiased">
    <div class="flex min-h-screen bg-gray-50">
        <!-- Sidebar for desktop -->
        <aside class="hidden w-64 flex-col border-r bg-white lg:flex">
            <div class="flex h-16 items-center border-b px-4">
                <a href="{{ route('home') }}" class="flex items-center gap-2">
                    <div
                        class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                        <span class="text-lg font-bold">D</span>
                    </div>
                    <span class="text-xl font-bold">Digitora</span>
                </a>
            </div>
            <nav class="flex-1 overflow-auto py-4">
                <div class="px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Main</h2>
                    <div class="space-y-1">
                        <a href="{{ route('user.dashboard') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.dashboard') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            Dashboard
                        </a>
                        <a href="{{ route('user.purchases') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.purchases') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.purchases') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            My Purchases
                        </a>
                        <a href="{{ route('cart.index') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('cart.*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('cart.*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            Shopping Cart
                        </a>
                    </div>
                </div>

                <div class="mt-8 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Account</h2>
                    <div class="space-y-1">
                        <a href="{{ route('user.profile') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.profile') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.profile') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Profile
                        </a>
                        <a href="{{ route('user.settings') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.settings') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.settings') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            Settings
                        </a>
                        @auth
                        <a href="{{ route('membership.index') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('membership.*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('membership.*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 2L2 7l10 5 10-5-10-5z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M2 17l10 5 10-5" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M2 12l10 5 10-5" />
                            </svg>
                            Membership
                            @php
                                $currentTier = Auth::user()->getCurrentMembershipTier();
                            @endphp
                            @if($currentTier)
                                <span class="ml-auto text-xs px-2 py-0.5 rounded-full {{ $currentTier->slug === 'free' ? 'bg-gray-100 text-gray-600' : ($currentTier->slug === 'basic' ? 'bg-blue-100 text-blue-600' : 'bg-purple-100 text-purple-600') }}">
                                    {{ $currentTier->name }}
                                </span>
                            @endif
                        </a>
                        @endauth
                    </div>
                </div>

                <div class="mt-8 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Browse</h2>
                    <div class="space-y-1">
                        <a href="{{ route('user.browse.products') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.browse.products*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.browse.products*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            Products
                        </a>
                        <a href="{{ route('user.browse.courses') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.browse.courses*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.browse.courses*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            Courses
                        </a>
                        <a href="{{ route('user.browse.stores') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.browse.stores*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.browse.stores*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                            Stores
                        </a>
                    </div>
                </div>

                <div class="mt-8 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Explore (Standalone)</h2>
                    <div class="space-y-1">
                        <a href="{{ route('browse.courses') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('browse.courses*') ? 'bg-purple-50 text-purple-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('browse.courses*') ? 'text-purple-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            All Courses
                        </a>
                        <a href="{{ route('browse.products') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('browse.products*') ? 'bg-purple-50 text-purple-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('browse.products*') ? 'text-purple-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            All Products
                        </a>
                        <a href="{{ route('browse.stores') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('browse.stores*') ? 'bg-purple-50 text-purple-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('browse.stores*') ? 'text-purple-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                            All Stores
                        </a>
                    </div>
                </div>

                <div class="mt-8 px-3">
                    <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Seller</h2>
                    <div class="space-y-1">
                        <a href="{{ route('user.seller-dashboard') }}"
                            class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.seller-dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                class="mr-3 h-5 w-5 {{ request()->routeIs('user.seller-dashboard') ? 'text-indigo-500' : 'text-gray-400' }}"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            @if (Auth::check() && Auth::user()->isSeller())
                                Seller Dashboard
                            @else
                                Become a Seller
                            @endif
                        </a>
                    </div>
                </div>
            </nav>
            <div class="border-t p-4">
                <div class="flex items-center gap-3">
                    @auth
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-600 text-white">
                            {{ substr(Auth::user()->name, 0, 1) }}
                        </div>
                        <div>
                            <p class="text-sm font-medium">{{ Auth::user()->name }}</p>
                            <p class="text-xs text-gray-500">User Account</p>
                        </div>
                    @else
                        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium">Guest</p>
                            <p class="text-xs text-gray-500">Not logged in</p>
                        </div>
                    @endauth
                </div>
                <div class="mt-4">
                    @auth
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit"
                                class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Logout
                            </button>
                        </form>
                    @else
                        <div class="space-y-2">
                            <a href="{{ route('login') }}"
                                class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                </svg>
                                Login
                            </a>
                            <a href="{{ route('register') }}"
                                class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                </svg>
                                Register
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
        </aside>

        <!-- Main content -->
        <div class="flex flex-1 flex-col">
            <!-- Header -->
            <header class="flex h-16 items-center justify-between border-b bg-white px-4 sm:px-6">
                <div class="flex items-center">
                    <button x-data @click="$dispatch('toggle-sidebar')"
                        class="mr-2 rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="h-6 w-6">
                            <line x1="4" x2="20" y1="12" y2="12"></line>
                            <line x1="4" x2="20" y1="6" y2="6"></line>
                            <line x1="4" x2="20" y1="18" y2="18"></line>
                        </svg>
                        <span class="sr-only">Toggle Menu</span>
                    </button>
                    <div class="lg:hidden">
                        <a href="{{ route('home') }}" class="flex items-center gap-2">
                            <div
                                class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                                <span class="text-lg font-bold">D</span>
                            </div>
                            <span class="text-xl font-bold">Digitora</span>
                        </a>
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <a href="{{ route('cart.index') }}"
                        class="relative rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span class="sr-only">View cart</span>
                        @if (isset($cartItemCount) && $cartItemCount > 0)
                            <span
                                class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full">{{ $cartItemCount }}</span>
                        @endif
                    </a>
                    @auth
                        <div x-data="{ open: false }" class="relative">
                            <button @click="open = !open"
                                class="flex items-center gap-2 rounded-full text-gray-500 hover:text-gray-600">
                                <div
                                    class="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 text-white">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                                <span class="sr-only">Open user menu</span>
                            </button>
                            <div x-show="open" @click.away="open = false"
                                class="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                                role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button"
                                tabindex="-1">
                                <a href="{{ route('user.dashboard') }}"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    role="menuitem">Dashboard</a>
                                <a href="{{ route('user.profile') }}"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    role="menuitem">Profile</a>
                                <a href="{{ route('user.settings') }}"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    role="menuitem">Settings</a>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit"
                                        class="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                        role="menuitem">Logout</button>
                                </form>
                            </div>
                        </div>
                    @else
                        <div class="flex items-center space-x-3">
                            <a href="{{ route('login') }}"
                                class="text-sm font-medium text-indigo-600 hover:text-indigo-500">Login</a>
                            <a href="{{ route('register') }}"
                                class="rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-medium text-white hover:bg-indigo-500">Register</a>
                        </div>
                    @endauth
                </div>
            </header>

            <!-- Mobile sidebar -->
            <div x-data="{ open: false }" @toggle-sidebar.window="open = !open" x-show="open"
                class="fixed inset-0 z-50 lg:hidden" x-cloak>
                <div x-show="open" @click="open = false" class="fixed inset-0 bg-gray-600 bg-opacity-75"></div>
                <div class="fixed inset-y-0 left-0 flex w-64 flex-col bg-white">
                    <div class="flex h-16 items-center justify-between border-b px-4">
                        <a href="{{ route('home') }}" class="flex items-center gap-2">
                            <div
                                class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                                <span class="text-lg font-bold">D</span>
                            </div>
                            <span class="text-xl font-bold">Digitora</span>
                        </a>
                        <button @click="open = false"
                            class="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                            <span class="sr-only">Close Menu</span>
                        </button>
                    </div>
                    <nav class="flex-1 overflow-auto py-4">
                        <div class="px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Main
                            </h2>
                            <div class="space-y-1">
                                <a href="{{ route('user.dashboard') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.dashboard') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                                    </svg>
                                    Dashboard
                                </a>
                                <a href="{{ route('user.purchases') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.purchases') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.purchases') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                    </svg>
                                    My Purchases
                                </a>
                                <a href="{{ route('cart.index') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('cart.*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('cart.*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    Shopping Cart
                                </a>
                            </div>
                        </div>

                        <div class="mt-8 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Account
                            </h2>
                            <div class="space-y-1">
                                <a href="{{ route('user.profile') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.profile') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.profile') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Profile
                                </a>
                                <a href="{{ route('user.settings') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.settings') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.settings') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Settings
                                </a>
                                @auth
                                <a href="{{ route('membership.index') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('membership.*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('membership.*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 2L2 7l10 5 10-5-10-5z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2 17l10 5 10-5" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2 12l10 5 10-5" />
                                    </svg>
                                    Membership
                                    @php
                                        $currentTier = Auth::user()->getCurrentMembershipTier();
                                    @endphp
                                    @if($currentTier)
                                        <span class="ml-auto text-xs px-2 py-0.5 rounded-full {{ $currentTier->slug === 'free' ? 'bg-gray-100 text-gray-600' : ($currentTier->slug === 'basic' ? 'bg-blue-100 text-blue-600' : 'bg-purple-100 text-purple-600') }}">
                                            {{ $currentTier->name }}
                                        </span>
                                    @endif
                                </a>
                                @endauth
                            </div>
                        </div>

                        <div class="mt-8 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Browse
                            </h2>
                            <div class="space-y-1">
                                <a href="{{ route('user.browse.products') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.browse.products*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.browse.products*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 0 01-2-2v-2z" />
                                    </svg>
                                    Products
                                </a>
                                <a href="{{ route('user.browse.courses') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.browse.courses*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.browse.courses*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                    Courses
                                </a>
                                <a href="{{ route('user.browse.stores') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.browse.stores*') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.browse.stores*') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    Stores
                                </a>
                            </div>
                        </div>

                        <div class="mt-8 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Explore (Standalone)
                            </h2>
                            <div class="space-y-1">
                                <a href="{{ route('browse.courses') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('browse.courses*') ? 'bg-purple-50 text-purple-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('browse.courses*') ? 'text-purple-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                    All Courses
                                </a>
                                <a href="{{ route('browse.products') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('browse.products*') ? 'bg-purple-50 text-purple-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('browse.products*') ? 'text-purple-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 0 01-2-2v-2z" />
                                    </svg>
                                    All Products
                                </a>
                                <a href="{{ route('browse.stores') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('browse.stores*') ? 'bg-purple-50 text-purple-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('browse.stores*') ? 'text-purple-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                    </svg>
                                    All Stores
                                </a>
                            </div>
                        </div>

                        <div class="mt-8 px-3">
                            <h2 class="mb-2 px-4 text-xs font-semibold uppercase tracking-wider text-gray-500">Seller
                            </h2>
                            <div class="space-y-1">
                                <a href="{{ route('user.seller-dashboard') }}"
                                    class="flex items-center rounded-md px-4 py-2 text-sm font-medium {{ request()->routeIs('user.seller-dashboard') ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        class="mr-3 h-5 w-5 {{ request()->routeIs('user.seller-dashboard') ? 'text-indigo-500' : 'text-gray-400' }}"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                    @if (Auth::check() && Auth::user()->isSeller())
                                        Seller Dashboard
                                    @else
                                        Become a Seller
                                    @endif
                                </a>
                            </div>
                        </div>
                    </nav>
                    <div class="border-t p-4">
                        <div class="flex items-center gap-3">
                            @auth
                                <div
                                    class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-600 text-white">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                                <div>
                                    <p class="text-sm font-medium">{{ Auth::user()->name }}</p>
                                    <p class="text-xs text-gray-500">User Account</p>
                                </div>
                            @else
                                <div
                                    class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200 text-gray-600">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium">Guest</p>
                                    <p class="text-xs text-gray-500">Not logged in</p>
                                </div>
                            @endauth
                        </div>
                        <div class="mt-4">
                            @auth
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit"
                                        class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                        </svg>
                                        Logout
                                    </button>
                                </form>
                            @else
                                <div class="space-y-2">
                                    <a href="{{ route('login') }}"
                                        class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                        </svg>
                                        Login
                                    </a>
                                    <a href="{{ route('register') }}"
                                        class="flex w-full items-center rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5 text-gray-400"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                        </svg>
                                        Register
                                    </a>
                                </div>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-auto p-4 sm:p-6">
                @if (session('success'))
                    <div class="mb-4 rounded-md bg-green-50 p-4 text-sm text-green-700">
                        {{ session('success') }}
                    </div>
                @endif

                @if (session('error'))
                    <div class="mb-4 rounded-md bg-red-50 p-4 text-sm text-red-700">
                        {{ session('error') }}
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    <!-- AI Chat Component -->
    @include('components.ai-chat')

    <!-- AI Chat JS -->
    <script src="{{ asset(js_path() . '/ai-chat.js') }}" defer></script>
</body>

</html>
