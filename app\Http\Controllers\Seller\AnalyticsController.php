<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Order;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

use Illuminate\Support\Facades\Response;

class AnalyticsController extends Controller
{
    public function index(Request $request)
    {
        $timeRange = $request->get('range', '30d');

        // Set date range based on selected time range
        $startDate = null;
        $endDate = Carbon::now();

        switch ($timeRange) {
            case '7d':
                $startDate = Carbon::now()->subDays(7);
                break;
            case '30d':
                $startDate = Carbon::now()->subDays(30);
                break;
            case '90d':
                $startDate = Carbon::now()->subDays(90);
                break;
            case '1y':
                $startDate = Carbon::now()->subYear();
                break;
            default:
                $startDate = Carbon::now()->subDays(30);
        }

        // Get revenue data for chart - only for successful orders (products and courses)
        $sellerId = Auth::id();
        $feeService = new \App\Services\FeeCalculationService();

        // Get orders grouped by date
        $ordersByDate = DB::table('orders')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                'orders.id',
                'orders.amount',
                'orders.product_id',
                'orders.course_id',
                'orders.seller_id'
            )
            ->where('orders.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->orderBy('date')
            ->get();

        // Calculate net revenue by date
        $revenueData = collect();
        $groupedOrders = $ordersByDate->groupBy('date');

        foreach ($groupedOrders as $date => $dayOrders) {
            $orders = \App\Models\Order::whereIn('id', $dayOrders->pluck('id'))->get();
            $dayRevenueData = $feeService->calculateTotalSellerRevenue($orders);

            $revenueData->push((object)[
                'date' => $date,
                'revenue' => $dayRevenueData['total_net_amount'] // Use net amount instead of gross
            ]);
        }

        // Get sales data for chart - only for successful orders (products and courses)
        $salesData = DB::table('orders')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('orders.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date')
            ->get();

        // Get top products and courses - only count successful orders
        $topProductsQuery = DB::table('products')
            ->select(
                'products.name as item_name',
                DB::raw("'product' as item_type"),
                DB::raw('COUNT(orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('products.id', '=', 'orders.product_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('products.seller_id', $sellerId)
            ->groupBy('products.id', 'products.name');

        $topCoursesQuery = DB::table('courses')
            ->select(
                'courses.title as item_name',
                DB::raw("'course' as item_type"),
                DB::raw('COUNT(orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('courses.id', '=', 'orders.course_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('courses.seller_id', $sellerId)
            ->groupBy('courses.id', 'courses.title');

        $topProducts = $topProductsQuery->union($topCoursesQuery)
            ->orderByDesc('orders_count')
            ->limit(5)
            ->get();

        // Get category distribution for products and courses - only count successful orders
        $productCategoryData = DB::table('products')
            ->select(
                'category',
                DB::raw('COUNT(DISTINCT orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('products.id', '=', 'orders.product_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('products.seller_id', $sellerId)
            ->groupBy('category');

        $courseCategoryData = DB::table('courses')
            ->join('product_categories', 'courses.category_id', '=', 'product_categories.id')
            ->select(
                'product_categories.name as category',
                DB::raw('COUNT(DISTINCT orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('courses.id', '=', 'orders.course_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('courses.seller_id', $sellerId)
            ->groupBy('product_categories.id', 'product_categories.name');

        $categoryData = $productCategoryData->union($courseCategoryData)
            ->orderByDesc('orders_count')
            ->get();

        // Get revenue by category over time - only for successful orders (products and courses)
        $productCategoryRevenueData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                'products.category',
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'), 'products.category');

        $courseCategoryRevenueData = DB::table('orders')
            ->join('courses', 'orders.course_id', '=', 'courses.id')
            ->join('product_categories', 'courses.category_id', '=', 'product_categories.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                'product_categories.name as category',
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('courses.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'), 'product_categories.name');

        $categoryRevenueData = $productCategoryRevenueData->union($courseCategoryRevenueData)
            ->orderBy('date')
            ->get();

        // Get summary stats - only for successful orders (products and courses)
        $totalRevenue = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $totalSales = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $averageOrderValue = $totalSales > 0 ? $totalRevenue / $totalSales : 0;

        // Get previous period data for comparison - only for successful orders (products and courses)
        $previousStartDate = (clone $startDate)->subDays($startDate->diffInDays($endDate));
        $previousEndDate = (clone $endDate)->subDays($startDate->diffInDays($endDate));

        $previousRevenue = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->sum('amount');

        $previousSales = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();

        $revenueChange = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : ($totalRevenue > 0 ? 100 : 0);
        $salesChange = $previousSales > 0 ? (($totalSales - $previousSales) / $previousSales) * 100 : ($totalSales > 0 ? 100 : 0);

        // Ensure we have at least one data point for charts
        if ($revenueData->isEmpty()) {
            $revenueData = collect([
                ['date' => now()->format('Y-m-d'), 'revenue' => 0]
            ]);
        }

        if ($salesData->isEmpty()) {
            $salesData = collect([
                ['date' => now()->format('Y-m-d'), 'count' => 0]
            ]);
        }

        return view('seller.analytics.index', compact(
            'timeRange',
            'revenueData',
            'salesData',
            'topProducts',
            'totalRevenue',
            'totalSales',
            'averageOrderValue',
            'revenueChange',
            'salesChange',
            'categoryData',
            'categoryRevenueData'
        ));
    }

    /**
     * Export analytics data as CSV
     */
    public function exportCsv(Request $request)
    {
        $timeRange = $request->get('range', '30d');
        $data = $this->getAnalyticsData($timeRange);

        // Create CSV content
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="analytics_' . $timeRange . '_' . date('Y-m-d') . '.csv"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Add headers
            fputcsv($file, ['Analytics Report', 'Period: ' . $data['timeRange'], 'Generated: ' . date('Y-m-d H:i:s')]);
            fputcsv($file, []);

            // Summary section
            fputcsv($file, ['Summary']);
            fputcsv($file, ['Total Revenue', 'Rp ' . number_format($data['totalRevenue'], 0, ',', '.')]);
            fputcsv($file, ['Total Sales', $data['totalSales']]);
            fputcsv($file, ['Average Order Value', 'Rp ' . number_format($data['averageOrderValue'], 0, ',', '.')]);
            fputcsv($file, []);

            // Revenue by date
            fputcsv($file, ['Revenue by Date']);
            fputcsv($file, ['Date', 'Revenue (Rp)']);
            foreach ($data['revenueData'] as $item) {
                fputcsv($file, [
                    Carbon::parse($item->date)->format('Y-m-d'),
                    number_format($item->revenue, 0, ',', '.')
                ]);
            }
            fputcsv($file, []);

            // Sales by date
            fputcsv($file, ['Sales by Date']);
            fputcsv($file, ['Date', 'Number of Sales']);
            foreach ($data['salesData'] as $item) {
                fputcsv($file, [
                    Carbon::parse($item->date)->format('Y-m-d'),
                    $item->count
                ]);
            }
            fputcsv($file, []);

            // Category data
            fputcsv($file, ['Category Analysis']);
            fputcsv($file, ['Category', 'Sales Count', 'Revenue (Rp)']);
            foreach ($data['categoryData'] as $item) {
                fputcsv($file, [
                    ucfirst($item->category ?? 'Uncategorized'),
                    $item->orders_count,
                    number_format($item->orders_sum_amount, 0, ',', '.')
                ]);
            }
            fputcsv($file, []);

            // Top products and courses
            fputcsv($file, ['Top Products & Courses']);
            fputcsv($file, ['Item Name', 'Type', 'Sales Count', 'Revenue (Rp)']);
            foreach ($data['topProducts'] as $item) {
                fputcsv($file, [
                    $item->item_name,
                    ucfirst($item->item_type),
                    $item->orders_count,
                    number_format($item->orders_sum_amount, 0, ',', '.')
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }



    /**
     * Get analytics data for the given time range
     */
    private function getAnalyticsData($timeRange)
    {
        // Set date range based on selected time range
        $startDate = null;
        $endDate = Carbon::now();

        switch ($timeRange) {
            case '7d':
                $startDate = Carbon::now()->subDays(7);
                break;
            case '30d':
                $startDate = Carbon::now()->subDays(30);
                break;
            case '90d':
                $startDate = Carbon::now()->subDays(90);
                break;
            case '1y':
                $startDate = Carbon::now()->subYear();
                break;
            default:
                $startDate = Carbon::now()->subDays(30);
        }

        // Get revenue data for chart - only for successful orders (products and courses)
        $sellerId = Auth::id();
        $revenueData = DB::table('orders')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('orders.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date')
            ->get();

        // Get sales data for chart - only for successful orders (products and courses)
        $salesData = DB::table('orders')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where('orders.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'))
            ->orderBy('date')
            ->get();

        // Get top products and courses - only count successful orders
        $topProductsQuery = DB::table('products')
            ->select(
                'products.name as item_name',
                DB::raw("'product' as item_type"),
                DB::raw('COUNT(orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('products.id', '=', 'orders.product_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('products.seller_id', $sellerId)
            ->groupBy('products.id', 'products.name');

        $topCoursesQuery = DB::table('courses')
            ->select(
                'courses.title as item_name',
                DB::raw("'course' as item_type"),
                DB::raw('COUNT(orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('courses.id', '=', 'orders.course_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('courses.seller_id', $sellerId)
            ->groupBy('courses.id', 'courses.title');

        $topProducts = $topProductsQuery->union($topCoursesQuery)
            ->orderByDesc('orders_count')
            ->limit(5)
            ->get();

        // Get category distribution for products and courses - only count successful orders
        $productCategoryData = DB::table('products')
            ->select(
                'category',
                DB::raw('COUNT(DISTINCT orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('products.id', '=', 'orders.product_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('products.seller_id', $sellerId)
            ->groupBy('category');

        $courseCategoryData = DB::table('courses')
            ->join('product_categories', 'courses.category_id', '=', 'product_categories.id')
            ->select(
                'product_categories.name as category',
                DB::raw('COUNT(DISTINCT orders.id) as orders_count'),
                DB::raw('SUM(orders.amount) as orders_sum_amount')
            )
            ->leftJoin('orders', function ($join) use ($startDate, $endDate) {
                $join->on('courses.id', '=', 'orders.course_id')
                    ->where('orders.status', '=', 'success')
                    ->whereBetween('orders.created_at', [$startDate, $endDate]);
            })
            ->where('courses.seller_id', $sellerId)
            ->groupBy('product_categories.id', 'product_categories.name');

        $categoryData = $productCategoryData->union($courseCategoryData)
            ->orderByDesc('orders_count')
            ->get();

        // Get revenue by category over time - only for successful orders (products and courses)
        $productCategoryRevenueData = DB::table('orders')
            ->join('products', 'orders.product_id', '=', 'products.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                'products.category',
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('products.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'), 'products.category');

        $courseCategoryRevenueData = DB::table('orders')
            ->join('courses', 'orders.course_id', '=', 'courses.id')
            ->join('product_categories', 'courses.category_id', '=', 'product_categories.id')
            ->select(
                DB::raw('DATE(orders.created_at) as date'),
                'product_categories.name as category',
                DB::raw('SUM(orders.amount) as revenue')
            )
            ->where('courses.seller_id', '=', $sellerId)
            ->where('orders.status', '=', 'success')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(orders.created_at)'), 'product_categories.name');

        $categoryRevenueData = $productCategoryRevenueData->union($courseCategoryRevenueData)
            ->orderBy('date')
            ->get();

        // Get summary stats - only for successful orders (products and courses)
        $totalRevenue = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');

        $totalSales = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $averageOrderValue = $totalSales > 0 ? $totalRevenue / $totalSales : 0;

        // Get previous period data for comparison - only for successful orders
        $previousStartDate = (clone $startDate)->subDays($startDate->diffInDays($endDate));
        $previousEndDate = (clone $endDate)->subDays($startDate->diffInDays($endDate));

        $previousRevenue = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->sum('amount');

        $previousSales = Order::where('seller_id', $sellerId)
            ->where('status', '=', 'success')
            ->whereBetween('created_at', [$previousStartDate, $previousEndDate])
            ->count();

        $revenueChange = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : ($totalRevenue > 0 ? 100 : 0);
        $salesChange = $previousSales > 0 ? (($totalSales - $previousSales) / $previousSales) * 100 : ($totalSales > 0 ? 100 : 0);

        // Ensure we have at least one data point for charts
        if ($revenueData->isEmpty()) {
            $revenueData = collect([
                (object)['date' => now()->format('Y-m-d'), 'revenue' => 0]
            ]);
        }

        if ($salesData->isEmpty()) {
            $salesData = collect([
                (object)['date' => now()->format('Y-m-d'), 'count' => 0]
            ]);
        }

        return [
            'timeRange' => $timeRange,
            'revenueData' => $revenueData,
            'salesData' => $salesData,
            'topProducts' => $topProducts,
            'totalRevenue' => $totalRevenue,
            'totalSales' => $totalSales,
            'averageOrderValue' => $averageOrderValue,
            'revenueChange' => $revenueChange,
            'salesChange' => $salesChange,
            'categoryData' => $categoryData,
            'categoryRevenueData' => $categoryRevenueData
        ];
    }
}
