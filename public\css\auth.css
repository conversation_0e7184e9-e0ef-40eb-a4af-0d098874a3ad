/* Auth Pages Styles - Scoped with .auth-page class */

/* Import Tailwind CSS */
@import 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';

/* Base styles for auth pages */
.auth-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #333;
}

/* Background gradient */
.auth-page .auth-bg {
    background-image: linear-gradient(to bottom right, #eef2ff, #faf5ff, #ffffff);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Back link styles */
.auth-page .back-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #6366f1;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
    text-decoration: none;
}

.auth-page .back-link:hover {
    color: #4f46e5;
}

.auth-page .back-link svg {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

/* Card styles */
.auth-page .auth-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 28rem;
    margin: 0 auto;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.auth-page .auth-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

/* Logo styles */
.auth-page .logo-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.auth-page .logo {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 0.5rem;
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

/* Form styles */
.auth-page .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.auth-page .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.auth-page label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.auth-page input[type="text"],
.auth-page input[type="email"],
.auth-page input[type="password"] {
    width: 100%;
    padding: 0.625rem 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease, box-shadow 0.15s ease;
    background-color: white;
}

.auth-page input[type="text"]:focus,
.auth-page input[type="email"]:focus,
.auth-page input[type="password"]:focus {
    outline: none;
    border-color: #a5b4fc;
    box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.25);
}

.auth-page .password-field {
    position: relative;
}

.auth-page .password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    cursor: pointer;
    color: #9ca3af;
}

.auth-page .checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auth-page input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    border-radius: 0.25rem;
    border: 1px solid #d1d5db;
    accent-color: #6366f1;
}

/* Button styles */
.auth-page .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.625rem 1rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 0.875rem;
}

.auth-page .btn-primary {
    background-color: #6366f1;
    color: white;
    border: none;
}

.auth-page .btn-primary:hover {
    background-color: #4f46e5;
}

.auth-page .btn-outline {
    background-color: white;
    border: 1px solid #e5e7eb;
    color: #374151;
}

.auth-page .btn-outline:hover {
    background-color: #f9fafb;
}

/* Divider styles */
.auth-page .divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    color: #9ca3af;
    font-size: 0.75rem;
    text-transform: uppercase;
}

.auth-page .divider::before,
.auth-page .divider::after {
    content: "";
    flex: 1;
    border-top: 1px solid #e5e7eb;
}

.auth-page .divider span {
    padding: 0 0.75rem;
}

/* Social login buttons */
.auth-page .social-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.auth-page .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.625rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    background-color: white;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.auth-page .social-btn:hover {
    background-color: #f9fafb;
}

.auth-page .social-btn svg {
    width: 1.25rem;
    height: 1.25rem;
}

/* Footer link styles */
.auth-page .auth-footer {
    text-align: center;
    padding: 1.25rem;
    border-top: 1px solid #f3f4f6;
    font-size: 0.875rem;
    color: #6b7280;
}

.auth-page .auth-footer a {
    color: #6366f1;
    text-decoration: none;
    font-weight: 500;
}

.auth-page .auth-footer a:hover {
    text-decoration: underline;
    color: #4f46e5;
}

/* Error and success messages */
.auth-page .alert {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.auth-page .alert-error {
    background-color: #fee2e2;
    border: 1px solid #fecaca;
    color: #b91c1c;
}

.auth-page .alert-success {
    background-color: #d1fae5;
    border: 1px solid #a7f3d0;
    color: #047857;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.auth-page .auth-card {
    animation: fadeIn 0.5s ease-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .auth-page .auth-card {
        max-width: 100%;
        margin: 0 1rem;
    }

    .auth-page .social-buttons {
        grid-template-columns: 1fr;
    }
}
