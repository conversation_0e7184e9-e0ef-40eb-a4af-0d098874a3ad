@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div class="flex flex-col justify-between gap-4 md:flex-row md:items-center">
        <div>
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">Payments</h1>
            <p class="text-gray-600">Manage your earnings and payment methods</p>
        </div>
        <div class="flex items-center gap-3">
            <div x-data="{ open: false }" class="relative">
                <button @click="open = !open" class="inline-flex items-center gap-1 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7 10 12 15 17 10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Export
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 rounded-lg bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 z-10">
                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <span>Export as CSV</span>
                    </a>

                </div>
            </div>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-3">
        <div class="stat-card">
            <div class="flex items-center justify-between pb-2">
                <h3>Net Earnings</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-green-500">
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
            </div>
            <div class="value">Rp {{ number_format($totalEarnings ?? 0, 0, ',', '.') }}</div>
            <p>Your earnings after all fees</p>
            @if(!$meetsMinimumPayout)
            <div class="mt-2 text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                Minimum payout: Rp {{ number_format($minimumThreshold, 0, ',', '.') }}
            </div>
            @endif
        </div>
        <div class="stat-card">
            <div class="flex items-center justify-between pb-2">
                <h3>Pending Payments</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-amber-500">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
            </div>
            <div class="value">Rp {{ number_format($pendingPayments ?? 0, 0, ',', '.') }}</div>
            <p>To be paid on next payout</p>
        </div>
        <div class="stat-card">
            <div class="flex items-center justify-between pb-2">
                <h3>Next Payout</h3>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 text-indigo-600">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
            </div>
            <div class="value text-xl">{{ $nextPayoutDate ?? 'N/A' }}</div>
            <p>Estimated sencillo</p>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-3">
        <div class="md:col-span-2">
            <div class="rounded-lg border bg-white shadow-sm">
                <div class="flex flex-col gap-4 border-b p-6 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h3 class="text-lg font-medium">Payment History</h3>
                        <p class="text-sm text-gray-500">Track your earnings and payouts</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <select onchange="window.location.href='{{ route('seller.payments.index', [], false) ?? '#' }}?status=' + this.value" class="rounded-lg border border-gray-200 py-2 pl-3 pr-10 text-sm transition-colors">
                            <option value="all" {{ ($status ?? 'all') === 'all' ? 'selected' : '' }}>All Payments</option>
                            <option value="pending" {{ ($status ?? 'all') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="paid" {{ ($status ?? 'all') === 'paid' ? 'selected' : '' }}>Paid</option>
                            <option value="failed" {{ ($status ?? 'all') === 'failed' ? 'selected' : '' }}>Failed</option>
                        </select>
                    </div>
                </div>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Transaction ID</th>
                                <th>Order</th>
                                <th class="text-right">Amount</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($payments ?? collect() as $payment)
                            <tr>
                                <td class="font-medium">{{ $payment->transaction_id ?? 'N/A' }}</td>
                                <td>#{{ $payment->order->order_number ?? 'N/A' }}</td>
                                <td class="text-right">Rp {{ number_format($payment->amount ?? 0, 0, ',', '.') }}</td>
                                <td class="whitespace-nowrap">{{ $payment->created_at->format('M d, Y') ?? 'N/A' }}</td>
                                <td>
                                    <span class="status-label {{ ($payment->status ?? 'pending') === 'paid' ? 'paid' : (($payment->status ?? 'pending') === 'failed' ? 'failed' : 'pending') }}">
                                        {{ ucfirst($payment->status ?? 'pending') }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('seller.payments.show', $payment, false) ?? '#' }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center text-gray-500">No payments found</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <div class="border-t px-6 py-4">
                    {{ ($payments ?? collect())->links() }}
                </div>
            </div>
        </div>
        <div>
            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Payment Method</h3>
                    <p class="text-sm text-gray-500">Update your payment details</p>
                </div>
                <div class="p-6">
                    @if(Auth::check())
                    <form action="{{ route('seller.payments.updatePaymentMethod', [], false) ?? '#' }}" method="POST">
                        @csrf
                        <div class="space-y-4">
                            <div>
                                <label for="payment_method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                                <select id="payment_method" name="payment_method" class="mt-1 block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                    <option value="bank_transfer" {{ (Auth::user()->payment_method ?? '') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                                    <option value="paypal" {{ (Auth::user()->payment_method ?? '') === 'paypal' ? 'selected' : '' }}>PayPal</option>
                                    <option value="stripe" {{ (Auth::user()->payment_method ?? '') === 'stripe' ? 'selected' : '' }}>Stripe</option>
                                </select>
                            </div>
                            <div>
                                <label for="account_details" class="block text-sm font-medium text-gray-700">Account Details</label>
                                <textarea id="account_details" name="account_details" rows="4" class="mt-1 block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">{{ Auth::user()->payment_details ?? '' }}</textarea>
                                <p class="mt-1 text-xs text-gray-500">Enter your account details for the selected payment method.</p>
                            </div>
                            <div>
                                <button type="submit" class="inline-flex w-full justify-center rounded-lg border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors">Update Payment Method</button>
                            </div>
                        </div>
                    </form>
                    @else
                    <p class="text-sm text-red-500">Please log in to update your payment method.</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection