/**
 * Store page functionality
 * Handles tab switching, animations, and store-specific interactions
 */

// Professional Tab switching functionality
function switchStoreTab(tabName) {
    return switchStoreTabEnhanced(tabName);
}

// Make switchStoreTab function globally available
window.switchStoreTab = switchStoreTab;

// Legacy support for old function name
window.switchTab = switchStoreTab;

// Initialize store page functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Store page initializing...');

    // Check for saved tab preference or default to products
    const savedTab = localStorage.getItem('storeActiveTab') || 'products';
    console.log('Initializing with tab:', savedTab);

    // Small delay to ensure DOM is fully ready
    setTimeout(() => {
        // Initialize the tab system
        initializeTabSystem(savedTab);

        // Initialize scroll animations
        initializeScrollAnimations();

        // Initialize mobile menu functionality
        initializeMobileMenu();

        console.log('Store page initialization complete');
    }, 100);
});

// Initialize tab system with proper error handling
function initializeTabSystem(defaultTab = 'products') {
    try {
        // Ensure all tab contents are hidden initially
        document.querySelectorAll('.store-tab-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Remove active class from all buttons
        document.querySelectorAll('.store-tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Add click event listeners to tab buttons
        document.querySelectorAll('.store-tab-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tabId = this.id;
                const tabName = tabId.replace('-tab', '');
                console.log('Tab button clicked:', tabName);
                switchStoreTab(tabName);
            });
        });

        // Fallback: Ensure at least one tab is visible
        const visibleTabs = document.querySelectorAll('.store-tab-content:not(.hidden)');
        if (visibleTabs.length === 0) {
            console.log('No visible tabs found, forcing products tab to show');
            const productsContent = document.getElementById('products-content');
            if (productsContent) {
                productsContent.classList.remove('hidden');
            }
        }

        // Activate the default tab
        switchStoreTab(defaultTab);

        console.log('Tab system initialized successfully with tab:', defaultTab);
    } catch (error) {
        console.error('Error initializing tab system:', error);
        // Fallback to products tab
        switchStoreTab('products');
    }
}

// Enhanced tab switching with loading states and animations
function switchStoreTabEnhanced(tabName) {
    console.log('Enhanced switching to tab:', tabName);

    // Add loading state to current content
    const currentContent = document.querySelector('.store-tab-content:not(.hidden)');
    if (currentContent) {
        currentContent.classList.add('loading');
    }

    // Hide all tab contents with smooth transition
    document.querySelectorAll('.store-tab-content').forEach(content => {
        content.classList.add('hidden');
        content.classList.remove('loading');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.store-tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content with delay for smooth transition
    const targetContent = document.getElementById(tabName + '-content');
    if (targetContent) {
        // Small delay to ensure smooth transition
        setTimeout(() => {
            targetContent.classList.remove('hidden');
            // Trigger scroll animation for newly visible elements
            triggerScrollAnimations(targetContent);
        }, 150);
        console.log('Showing content for:', tabName);
    } else {
        console.error('Target content not found for tab:', tabName);
        return false;
    }

    // Add active class to selected tab button
    const activeButton = document.getElementById(tabName + '-tab');
    if (activeButton) {
        activeButton.classList.add('active');
        console.log('Activated button for:', tabName);
    } else {
        console.error('Target button not found for tab:', tabName);
        return false;
    }

    // Store current tab in localStorage for persistence
    localStorage.setItem('storeActiveTab', tabName);

    return true;
}

// Trigger scroll animations for elements within a container
function triggerScrollAnimations(container) {
    const animateElements = container.querySelectorAll('.animate-on-scroll');
    animateElements.forEach(element => {
        element.classList.add('visible');
    });
}

// Scroll animations
function initializeScrollAnimations() {
    const animateElements = document.querySelectorAll('.animate-on-scroll');

    function checkScroll() {
        animateElements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementTop < windowHeight * 0.9) {
                element.classList.add('visible');
            }
        });
    }

    // Initial check
    checkScroll();

    // Check on scroll
    window.addEventListener('scroll', checkScroll);
}

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileMenuButton = document.querySelector('[data-mobile-menu-toggle]');
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = mobileMenuButton?.querySelector('.menu-icon');
    const closeIcon = mobileMenuButton?.querySelector('.close-icon');

    if (mobileMenuButton && mobileMenu) {
        // Simple toggle function
        const toggleMenu = function(e) {
            e.preventDefault();

            const isHidden = mobileMenu.classList.contains('hidden');

            if (isHidden) {
                // Show menu
                mobileMenu.classList.remove('hidden');
                menuIcon?.classList.add('hidden');
                closeIcon?.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            } else {
                // Hide menu
                mobileMenu.classList.add('hidden');
                menuIcon?.classList.remove('hidden');
                closeIcon?.classList.add('hidden');
                document.body.style.overflow = '';
            }
        };

        // Add click event
        mobileMenuButton.addEventListener('click', toggleMenu);

        // Close menu when clicking on links
        const mobileMenuLinks = mobileMenu.querySelectorAll('a');
        mobileMenuLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
                menuIcon?.classList.remove('hidden');
                closeIcon?.classList.add('hidden');
                document.body.style.overflow = '';
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    menuIcon?.classList.remove('hidden');
                    closeIcon?.classList.add('hidden');
                    document.body.style.overflow = '';
                }
            }
        });
    }
}

// Smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Function to open AI chat with product-specific message (legacy)
window.openProductChat = function(productName) {
    const message = `Halo! Saya tertarik dengan produk "${productName}". Bisakah Anda membantu saya dengan informasi lebih lanjut?`;

    // Use the improved global function if available
    if (typeof window.openAiChatWithMessage === 'function') {
        window.openAiChatWithMessage(message);
    } else if (typeof window.openAiChat === 'function') {
        // Fallback to old method
        window.openAiChat();
        setTimeout(() => {
            if (typeof window.sendAiMessage === 'function') {
                window.sendAiMessage(message);
            }
        }, 500);
    } else {
        // Show user-friendly message
        alert('AI Chat is not available at the moment. Please try again later.');
    }
};

// Function to open AI chat with product-specific context from store listing
window.openProductChatFromStore = function(productName, storeSlug, productSlug) {
    const message = `Halo! Saya tertarik dengan produk "${productName}". Bisakah Anda membantu saya dengan informasi lebih lanjut?`;

    // Create a temporary context to simulate being on the product page
    const originalPath = window.location.pathname;
    const productPath = `/${storeSlug}/${productSlug}`;

    // Temporarily change the pathname for context detection
    const originalPushState = history.pushState;
    history.pushState(null, null, productPath);

    // Use the improved global function if available
    if (typeof window.openAiChatWithMessage === 'function') {
        window.openAiChatWithMessage(message);
    } else if (typeof window.openAiChat === 'function') {
        // Fallback to old method
        window.openAiChat();
        setTimeout(() => {
            if (typeof window.sendAiMessage === 'function') {
                window.sendAiMessage(message);
            }
        }, 500);
    } else {
        // Show user-friendly message
        alert('AI Chat is not available at the moment. Please try again later.');
    }

    // Restore the original path after a short delay
    setTimeout(() => {
        history.pushState(null, null, originalPath);
    }, 1000);
};
