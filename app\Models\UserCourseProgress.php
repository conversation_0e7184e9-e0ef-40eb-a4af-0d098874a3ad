<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserCourseProgress extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'user_course_progress';

    protected $fillable = [
        'user_id',
        'course_id',
        'current_curriculum_item_id',
        'progress_percentage',
        'is_completed',
        'completed_at',
        'last_accessed_at',
        'completed_items',
    ];

    protected $casts = [
        'progress_percentage' => 'decimal:2',
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'completed_items' => 'array',
    ];

    /**
     * Get the user that owns the progress record
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course that this progress belongs to
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the current curriculum item
     */
    public function currentCurriculumItem()
    {
        return $this->belongsTo(CourseCurriculumItem::class, 'current_curriculum_item_id');
    }

    /**
     * Mark a curriculum item as completed
     */
    public function markItemCompleted($curriculumItemId)
    {
        $completedItems = $this->completed_items ?? [];
        
        if (!in_array($curriculumItemId, $completedItems)) {
            $completedItems[] = $curriculumItemId;
            $this->completed_items = $completedItems;
        }

        // Update current curriculum item
        $this->current_curriculum_item_id = $curriculumItemId;
        $this->last_accessed_at = now();

        // Calculate progress percentage
        $this->updateProgressPercentage();

        $this->save();
    }

    /**
     * Update progress percentage based on completed items
     */
    public function updateProgressPercentage()
    {
        $totalItems = $this->course->activeCurriculumItems()->count();
        $completedCount = count($this->completed_items ?? []);

        if ($totalItems > 0) {
            $percentage = ($completedCount / $totalItems) * 100;
            $this->progress_percentage = round($percentage, 2);

            // Mark as completed if all items are done
            if ($percentage >= 100) {
                $this->is_completed = true;
                $this->completed_at = now();
            }
        }
    }

    /**
     * Check if a specific curriculum item is completed
     */
    public function isItemCompleted($curriculumItemId)
    {
        return in_array($curriculumItemId, $this->completed_items ?? []);
    }

    /**
     * Get or create progress record for a user and course
     */
    public static function getOrCreate($userId, $courseId)
    {
        return static::firstOrCreate(
            ['user_id' => $userId, 'course_id' => $courseId],
            [
                'progress_percentage' => 0.00,
                'is_completed' => false,
                'completed_items' => [],
                'last_accessed_at' => now(),
            ]
        );
    }

    /**
     * Reset progress for a course
     */
    public function resetProgress()
    {
        $this->update([
            'progress_percentage' => 0.00,
            'is_completed' => false,
            'completed_at' => null,
            'completed_items' => [],
            'current_curriculum_item_id' => null,
            'last_accessed_at' => now(),
        ]);
    }
}
