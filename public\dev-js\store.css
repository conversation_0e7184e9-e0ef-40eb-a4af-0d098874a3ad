/**
 * Store page specific styles
 * Professional e-commerce design for seller branding
 */

/* Store-specific CSS variables */
:root {
    --store-primary: #6366f1;
    --store-secondary: #a855f7;
    --store-accent: #f59e0b;
    --store-success: #10b981;
    --store-warning: #f59e0b;
    --store-error: #ef4444;
    --store-gray-50: #f9fafb;
    --store-gray-100: #f3f4f6;
    --store-gray-200: #e5e7eb;
    --store-gray-300: #d1d5db;
    --store-gray-400: #9ca3af;
    --store-gray-500: #6b7280;
    --store-gray-600: #4b5563;
    --store-gray-700: #374151;
    --store-gray-800: #1f2937;
    --store-gray-900: #111827;
    --store-transition: all 0.3s ease;
    --store-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --store-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --store-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --store-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --store-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Store header enhancements */
.store-header {
    background: linear-gradient(135deg, var(--store-primary) 0%, var(--store-secondary) 100%);
    position: relative;
    overflow: hidden;
    z-index: 50; /* Ensure header is above content but below mobile menu */
}

.store-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Professional store branding */
.store-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: var(--store-transition);
}

.store-brand:hover {
    transform: translateY(-1px);
}

.store-logo {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    object-fit: cover;
    box-shadow: var(--store-shadow);
    transition: var(--store-transition);
}

.store-logo:hover {
    box-shadow: var(--store-shadow-md);
}

.store-logo-placeholder {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, var(--store-primary) 0%, var(--store-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1rem;
    box-shadow: var(--store-shadow);
    transition: var(--store-transition);
}

.store-logo-placeholder:hover {
    box-shadow: var(--store-shadow-md);
    transform: scale(1.05);
}

/* Enhanced navigation */
.store-nav {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.store-nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    color: var(--store-gray-700);
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    border-radius: 0.375rem;
    transition: var(--store-transition);
}

.store-nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--store-primary), var(--store-secondary));
    transform: translateX(-50%);
    transition: width 0.3s ease;
}

.store-nav-link:hover {
    color: var(--store-primary);
    background-color: var(--store-gray-50);
}

.store-nav-link:hover::after {
    width: 80%;
}

.store-nav-link.active {
    color: var(--store-primary);
    background-color: var(--store-gray-50);
}

.store-nav-link.active::after {
    width: 80%;
}

/* Hero section enhancements */
.store-hero {
    background: linear-gradient(135deg, var(--store-primary) 0%, var(--store-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.store-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.store-hero-content {
    position: relative;
    z-index: 10;
}

/* Professional product cards */
.store-product-card {
    background: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--store-shadow);
    transition: var(--store-transition);
    border: 1px solid var(--store-gray-100);
}

.store-product-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--store-shadow-xl);
    border-color: var(--store-primary);
}

.store-product-image {
    aspect-ratio: 4 / 3;
    overflow: hidden;
    background: var(--store-gray-100);
    position: relative;
}

.store-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.store-product-card:hover .store-product-image img {
    transform: scale(1.1);
}

/* Enhanced buttons */
.store-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: var(--store-transition);
    position: relative;
    overflow: hidden;
}

.store-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.store-btn:hover::before {
    left: 100%;
}

.store-btn-primary {
    background: linear-gradient(135deg, var(--store-primary) 0%, var(--store-secondary) 100%);
    color: white;
    box-shadow: var(--store-shadow);
}

.store-btn-primary:hover {
    box-shadow: var(--store-shadow-lg);
    transform: translateY(-1px);
}

.store-btn-secondary {
    background: white;
    color: var(--store-primary);
    border: 1px solid var(--store-gray-200);
    box-shadow: var(--store-shadow-sm);
}

.store-btn-secondary:hover {
    background: var(--store-gray-50);
    border-color: var(--store-primary);
    box-shadow: var(--store-shadow);
}

/* Professional Tab System */
.store-tabs {
    background: white;
    border-radius: 0.75rem;
    padding: 0.375rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--store-gray-200);
    position: relative;
    overflow: hidden;
}

.store-tabs::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
    pointer-events: none;
}

.store-tab-button {
    position: relative;
    padding: 0.875rem 1.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    background: transparent;
    cursor: pointer;
    color: var(--store-gray-600);
    z-index: 1;
    overflow: hidden;
}

.store-tab-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--store-primary) 0%, var(--store-secondary) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.store-tab-button.active {
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px -2px rgba(99, 102, 241, 0.3);
}

.store-tab-button.active::before {
    opacity: 1;
}

.store-tab-button:not(.active):hover {
    color: var(--store-primary);
    background: var(--store-gray-50);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
}

.store-tab-button svg {
    transition: transform 0.3s ease;
}

.store-tab-button:hover svg {
    transform: scale(1.1);
}

/* Tab Content */
.store-tab-content {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.store-tab-content.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
    position: absolute;
    left: -9999px;
}

.store-tab-content:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
    position: relative;
    left: auto;
}

/* Footer enhancements */
.store-footer {
    background: var(--store-gray-50);
    border-top: 1px solid var(--store-gray-200);
}

.store-footer-link {
    color: var(--store-gray-600);
    text-decoration: none;
    transition: var(--store-transition);
    position: relative;
}

.store-footer-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--store-primary);
    transition: width 0.3s ease;
}

.store-footer-link:hover {
    color: var(--store-primary);
}

.store-footer-link:hover::after {
    width: 100%;
}

/* Simple mobile menu fix */
#mobile-menu {
    z-index: 1000;
    background: white;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

#mobile-menu a {
    display: block;
    position: relative;
    z-index: 1001;
}

/* Enhanced Responsive Design */
@media (min-width: 1024px) and (max-width: 1279px) {
    .store-nav {
        gap: 0.75rem;
    }

    .store-nav-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
    }
}

@media (max-width: 1024px) {
    .store-tabs {
        padding: 0.25rem;
    }

    .store-tab-button {
        padding: 0.75rem 1.25rem;
        font-size: 0.8125rem;
    }
}

@media (max-width: 768px) {
    .store-nav {
        gap: 1rem;
    }

    .store-nav-link {
        padding: 0.375rem 0.75rem;
        font-size: 0.8125rem;
    }

    .store-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
    }

    /* Mobile-optimized tabs */
    .store-tabs {
        width: 100%;
        max-width: 100%;
        margin: 0 1rem;
        padding: 0.25rem;
        border-radius: 0.5rem;
    }

    .store-tab-button {
        flex: 1;
        padding: 0.75rem 1rem;
        font-size: 0.8125rem;
        text-align: center;
    }

    .store-tab-button svg {
        width: 1rem;
        height: 1rem;
    }
}

@media (max-width: 640px) {
    /* Ensure mobile menu items don't overflow */
    .store-nav-link {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Improve mobile header spacing */
    .store-header {
        padding: 0.5rem 0;
    }

    .store-brand {
        flex-shrink: 1;
        min-width: 0;
    }

    .store-brand span {
        font-size: 1rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Touch-friendly mobile menu button */
    [data-mobile-menu-toggle] {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        touch-action: manipulation;
        user-select: none;
        -webkit-user-select: none;
    }

    /* Mobile menu improvements */
    #mobile-menu {
        max-height: calc(100vh - 120px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* Animation classes */
.store-fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.7s ease, transform 0.7s ease;
}

.store-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Professional loading states */
.store-loading {
    position: relative;
    overflow: hidden;
}

.store-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: store-shimmer 1.5s infinite;
}

@keyframes store-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Tab Loading Animation */
@keyframes tabFadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.store-tab-content:not(.hidden) {
    animation: tabFadeIn 0.4s ease-out;
}

/* Enhanced Focus States for Accessibility */
.store-tab-button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.store-tab-button:focus:not(:focus-visible) {
    box-shadow: none;
}

/* Improved Tab Badge Styling */
.store-tab-button .badge {
    transition: all 0.3s ease;
    font-weight: 700;
    letter-spacing: 0.025em;
}

.store-tab-button.active .badge {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    transform: scale(1.05);
}

.store-tab-button:not(.active) .badge {
    background: var(--store-gray-100);
    color: var(--store-gray-600);
}

/* Smooth Hover Effects */
.store-tab-button:hover .badge {
    transform: scale(1.05);
}

/* Loading State for Tab Content */
.store-tab-content.loading {
    position: relative;
    pointer-events: none;
}

.store-tab-content.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}


