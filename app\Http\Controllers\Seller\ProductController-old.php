<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Models\Product;
use App\Models\SellerApplication;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::where('seller_id', auth()->id());

        if ($search = $request->input('search')) {
            $query->where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%");
        }

        if ($status = $request->input('status')) {
            if ($status !== 'all') {
                $query->where('status', $status);
            }
        }

        // Add withCount for orders with 'success' status to show correct sales numbers
        $query->withCount(['orders' => function ($query) {
            $query->where('status', 'success');
        }]);

        $products = $query->paginate(10);
        return view('seller.products.index', compact('products'));
    }

    public function selectType()
    {
        return view('seller.products.select-type');
    }

    public function create(Request $request)
    {
        // Get the product type from the request
        $productType = $request->get('type', 'digital'); // default to digital
        $contentType = $request->get('content_type', 'simple'); // default to simple

        // Validate the product type
        if (!in_array($productType, ['ebook', 'digital', 'course'])) {
            return redirect()->route('seller.products.select-type');
        }

        // Validate the content type
        if (!in_array($contentType, ['simple', 'course'])) {
            $contentType = 'simple';
        }

        // If it's a course type, redirect to the new course creation (which clears session)
        if ($productType === 'course' && $contentType === 'course') {
            return redirect()->route('seller.products.create-new-course');
        }

        // Get all categories with their subcategories and detailed categories
        $categoryTree = \App\Models\ProductCategory::active()
            ->with(['activeSubcategories', 'activeSubcategories.activeDetailedCategories'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // For backward compatibility, generate legacy category groups from the database
        $categoryGroups = [];
        $legacyCategoryMapping = [];
        $legacySubcategoryMapping = [];

        foreach ($categoryTree as $category) {
            $categoryGroups[$category->name] = [];

            foreach ($category->activeSubcategories as $subcategory) {
                try {
                    // Try to use the legacy_code if available, otherwise use the slug or create one from the name
                    // This is wrapped in a try-catch in case the legacy_code column doesn't exist yet
                    $legacyCode = $subcategory->legacy_code;
                    if (empty($legacyCode)) {
                        $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                    }
                } catch (\Exception $e) {
                    // If there's an error (like the column doesn't exist), use the slug or create one from the name
                    $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                }

                // Add to the category groups
                $categoryGroups[$category->name][$legacyCode] = $subcategory->name;

                // Build the mappings for JavaScript
                $legacyCategoryMapping[$legacyCode] = $category->name;
                $legacySubcategoryMapping[$legacyCode] = $subcategory->name;
            }
        }

        return view('seller.products.create', compact('categoryGroups', 'categoryTree', 'legacyCategoryMapping', 'legacySubcategoryMapping', 'productType', 'contentType'));
    }

    /**
     * Start a new course creation (clears any existing session data)
     */
    public function createNewCourse()
    {
        // Clear any existing course creation session data
        session()->forget('course_creation_data');
        session()->forget('draft_course_id');

        // Redirect to step 1
        return redirect()->route('seller.products.create-course-step', ['step' => 1]);
    }

    /**
     * Multi-step course creation workflow
     */
    public function createCourseStep(Request $request, $step = 1)
    {
        // Validate step (now only 2 steps)
        if (!in_array($step, [1, 2])) {
            return redirect()->route('seller.products.create-course-step', ['step' => 1]);
        }

        // Get course data from session if it exists
        $courseData = session('course_creation_data', []);

        // Check if we're starting fresh (no session data) or continuing an existing draft
        $isNewCourse = empty($courseData);
        $isDraftContinuation = !empty($courseData);

        // Get course-specific categories with their subcategories and detailed categories
        $categoryTree = \App\Models\ProductCategory::active()
            ->forProductType('course')
            ->with(['activeSubcategories', 'activeSubcategories.activeDetailedCategories'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // Build category groups for the legacy selector
        $categoryGroups = [];
        $legacyCategoryMapping = [];
        $legacySubcategoryMapping = [];

        foreach ($categoryTree as $category) {
            foreach ($category->activeSubcategories as $subcategory) {
                // For course categories, use the legacy_code directly since they're properly set in the seeder
                $legacyCode = $subcategory->legacy_code;

                // If no legacy_code is set, create one from the subcategory name
                if (empty($legacyCode)) {
                    $legacyCode = \Illuminate\Support\Str::slug($subcategory->name, '_');
                }

                $categoryGroups[$category->name][$legacyCode] = $subcategory->name;
                $legacyCategoryMapping[$legacyCode] = $category->name;
                $legacySubcategoryMapping[$legacyCode] = $subcategory->name;
            }
        }

        // Use combined view for step 2
        $viewName = $step == 2 ? 'seller.products.create-course-step2-combined' : 'seller.products.create-course-step' . $step;

        return view($viewName, compact(
            'step',
            'courseData',
            'categoryGroups',
            'categoryTree',
            'legacyCategoryMapping',
            'legacySubcategoryMapping',
            'isNewCourse',
            'isDraftContinuation'
        ));
    }

    /**
     * Save course step data and proceed to next step
     */
    public function saveCourseStep(Request $request, $step)
    {
        $courseData = session('course_creation_data', []);

        switch ($step) {
            case 1:
                // Validate basic course information
                $validated = $request->validate([
                    'name' => 'required|string|max:255',
                    'description' => 'required|string',
                    'category' => 'required|string',
                    'category_id' => 'nullable|exists:product_categories,id',
                    'subcategory_id' => 'nullable|exists:product_subcategories,id',
                    'detailed_category_id' => 'nullable|exists:product_detailed_categories,id',
                    'price' => 'required|numeric|min:5000',
                    'discount_price' => 'nullable|numeric|min:5000|lt:price',
                    'has_discount' => 'nullable|boolean',
                ]);

                // Handle image uploads
                $images = [];
                $userId = auth()->id();
                $userCourseDir = "course/{$userId}/course-images";

                if ($request->hasFile('images')) {
                    // Ensure directory exists
                    Storage::disk('public')->makeDirectory($userCourseDir);

                    foreach ($request->file('images') as $image) {
                        if ($image->getSize() <= 2 * 1024 * 1024) { // 2MB limit
                            $path = $image->store($userCourseDir, 'public');
                            $images[] = [
                                'path' => $path,
                                'url' => asset('storage/' . $path), // Add URL for preview
                                'name' => $image->getClientOriginalName(),
                                'original_name' => $image->getClientOriginalName(),
                                'size' => $image->getSize(),
                                'isExisting' => false
                            ];
                        }
                    }
                }

                // Preserve existing images if they exist and no new images uploaded
                if (empty($images) && isset($courseData['step1']['images'])) {
                    $images = $courseData['step1']['images'];
                }

                $courseData['step1'] = array_merge($validated, ['images' => $images]);
                break;

            case 2:
                // Validate combined course structure and content
                $validated = $request->validate([
                    'chapters' => 'required|array|min:1',
                    'chapters.*.title' => 'required|string|max:255',
                    'chapters.*.description' => 'nullable|string',
                    'chapters.*.sub_chapters' => 'required|array|min:1',
                    'chapters.*.sub_chapters.*.title' => 'required|string|max:255',
                    'chapters.*.sub_chapters.*.description' => 'nullable|string',
                    'content' => 'nullable|array',
                    'content.*.chapter_index' => 'nullable|integer',
                    'content.*.sub_chapter_index' => 'nullable|integer',
                    'content.*.resources' => 'nullable|array',
                    'content.*.resources.*.title' => 'nullable|string|max:255',
                    'content.*.resources.*.type' => 'nullable|string|in:video,text,file,pdf,link,audio,image',
                    'content.*.resources.*.description' => 'nullable|string',
                    'content.*.resources.*.content' => 'nullable|string',
                    'content.*.resources.*.is_preview' => 'nullable|boolean',
                ]);

                $courseData['step2'] = [
                    'chapters' => $validated['chapters']
                ];

                // Store content data if provided
                if (!empty($validated['content'])) {
                    $courseData['step3'] = [
                        'content' => $validated['content']
                    ];
                }
                break;
        }

        // Save to session
        session(['course_creation_data' => $courseData]);

        // Determine next step (now only 2 steps)
        $nextStep = $step + 1;
        if ($nextStep > 2) {
            // Final step - create the course
            return $this->createCourseFromSteps();
        }

        return redirect()->route('seller.products.create-course-step', ['step' => $nextStep])
            ->with('success', 'Step ' . $step . ' saved successfully!');
    }

    /**
     * Auto-save images for course creation
     */
    public function autoSaveImages(Request $request)
    {
        try {
            $validated = $request->validate([
                'images' => 'array', // Allow empty array
                'images.*.name' => 'required|string',
                'images.*.size' => 'required|integer',
                'images.*.type' => 'required|string',
                'images.*.data' => 'nullable|string', // Allow null for existing images
                'images.*.index' => 'required|integer',
                'images.*.isExisting' => 'nullable|boolean',
                'images.*.path' => 'nullable|string',
                'images.*.url' => 'nullable|string',
                'step' => 'required|integer|in:1'
            ]);

            // Get current course data
            $courseData = session('course_creation_data', []);
            $userId = auth()->id();

            // Create user-specific directory for course images
            $userCourseDir = "course/{$userId}/course-images";

            // Process and save images
            $savedImages = [];
            if (!empty($validated['images'])) {
                foreach ($validated['images'] as $imageData) {
                    // Handle existing images that are being preserved
                    if (isset($imageData['isExisting']) && $imageData['isExisting']) {
                        // Verify the existing image file still exists
                        if (isset($imageData['path']) && Storage::disk('public')->exists($imageData['path'])) {
                            // For existing images, preserve the existing data
                            $savedImages[] = [
                                'path' => $imageData['path'],
                                'url' => $imageData['url'],
                                'name' => $imageData['name'],
                                'original_name' => $imageData['name'],
                                'size' => $imageData['size'],
                                'index' => $imageData['index'],
                                'isExisting' => true
                            ];
                        } else {
                            // If existing image file is missing, skip it
                            continue;
                        }
                    } else {
                        // For new images, process and save them
                        try {
                            $base64Data = $imageData['data'];
                            if (strpos($base64Data, ',') !== false) {
                                $base64Data = explode(',', $base64Data)[1];
                            }

                            $imageContent = base64_decode($base64Data);

                            // Validate decoded content
                            if ($imageContent === false || empty($imageContent)) {
                                continue; // Skip invalid image data
                            }

                            // Generate unique filename with user-specific path
                            $extension = $this->getExtensionFromMimeType($imageData['type']);
                            $filename = 'course_' . time() . '_' . uniqid() . '.' . $extension;
                            $path = $userCourseDir . '/' . $filename;

                            // Ensure directory exists
                            Storage::disk('public')->makeDirectory($userCourseDir);

                            // Save to storage
                            if (!Storage::disk('public')->put($path, $imageContent)) {
                                continue; // Skip if save failed
                            }

                        $savedImages[] = [
                            'path' => $path,
                            'url' => asset('storage/' . $path),
                            'name' => $imageData['name'],
                            'original_name' => $imageData['name'],
                            'size' => $imageData['size'],
                            'index' => $imageData['index'],
                            'isExisting' => false
                        ];
                        } catch (\Exception $e) {
                            // Log error and continue with next image
                            \Log::error('Failed to process course image: ' . $e->getMessage());
                            continue;
                        }
                    }
                }
            }

            // Update session data
            if (!isset($courseData['step1'])) {
                $courseData['step1'] = [];
            }
            $courseData['step1']['images'] = $savedImages;
            session(['course_creation_data' => $courseData]);

            return response()->json([
                'success' => true,
                'message' => 'Images auto-saved successfully',
                'images' => $savedImages
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Auto-save failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file extension from MIME type
     */
    private function getExtensionFromMimeType($mimeType)
    {
        $mimeToExt = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];

        return $mimeToExt[$mimeType] ?? 'jpg';
    }

    /**
     * Auto-save course data for step 2
     */
    public function autoSaveCourseData(Request $request)
    {
        try {
            $validated = $request->validate([
                'step' => 'required|integer|in:2',
                'data' => 'required|array',
                'data.chapters' => 'required|array',
                'data.chapters.*.title' => 'required|string|max:255',
                'data.chapters.*.description' => 'nullable|string',
                'data.chapters.*.sub_chapters' => 'required|array',
                'data.chapters.*.sub_chapters.*.title' => 'required|string|max:255',
                'data.chapters.*.sub_chapters.*.description' => 'nullable|string',
                'data.content' => 'nullable|array',
                'data.content.*.chapter_index' => 'nullable|integer',
                'data.content.*.sub_chapter_index' => 'nullable|integer',
                'data.content.*.resources' => 'nullable|array',
                'data.content.*.resources.*.title' => 'nullable|string|max:255',
                'data.content.*.resources.*.type' => 'nullable|string',
                'data.content.*.resources.*.description' => 'nullable|string',
                'data.content.*.resources.*.content' => 'nullable|string',
                'data.content.*.resources.*.url' => 'nullable|string',
                'data.content.*.resources.*.is_preview' => 'nullable|boolean',
            ]);

            // Get current course data
            $courseData = session('course_creation_data', []);

            // Update step 2 data
            $courseData['step2'] = [
                'chapters' => $validated['data']['chapters']
            ];

            // Update step 3 data (content)
            $courseData['step3'] = [
                'content' => $validated['data']['content'] ?? []
            ];

            // Save to session
            session(['course_creation_data' => $courseData]);

            return response()->json([
                'success' => true,
                'message' => 'Course data auto-saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Auto-save failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save course as draft
     */
    public function saveCourseAsDraft()
    {
        try {
            $courseData = session('course_creation_data', []);

            if (empty($courseData['step1'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'No course data found to save as draft'
                ], 400);
            }

            $step1Data = $courseData['step1'];

            // Check if we're continuing an existing draft
            $draftCourseId = session('draft_course_id');
            $product = null;

            if ($draftCourseId) {
                // Try to find the existing draft product
                $product = Product::where('id', $draftCourseId)
                    ->where('seller_id', auth()->id())
                    ->where('status', 'draft')
                    ->where('content_type', 'course')
                    ->first();
            }

            // Prepare product data
            $productData = [
                'name' => $step1Data['name'] ?? 'Untitled Course',
                'description' => $step1Data['description'] ?? 'Course description',
                'category' => $step1Data['category'] ?? 'Other',
                'category_id' => $step1Data['category_id'] ?? null,
                'subcategory_id' => $step1Data['subcategory_id'] ?? null,
                'detailed_category_id' => $step1Data['detailed_category_id'] ?? null,
                'price' => $step1Data['price'] ?? 5000,
                'discount_price' => $step1Data['discount_price'] ?? null,
                'status' => 'draft',
                'content_type' => 'course',
                'product_type' => 'course',
            ];

            // Handle primary image if exists
            if (!empty($step1Data['images'])) {
                $firstImage = $step1Data['images'][0];
                $productData['image'] = $firstImage['path'];
            }

            if ($product) {
                // Update existing draft product
                $product->update($productData);
            } else {
                // Create new draft product
                $productData['seller_id'] = auth()->id();
                $productData['files'] = json_encode([]);

                // Generate unique slug
                $baseSlug = \Illuminate\Support\Str::slug($productData['name']);
                $slug = $baseSlug;
                $counter = 1;

                while (Product::where('slug', $slug)->exists()) {
                    $slug = "{$baseSlug}-{$counter}";
                    $counter++;
                }
                $productData['slug'] = $slug;

                $product = Product::create($productData);

                // Store the new draft ID in session for future updates
                session(['draft_course_id' => $product->id]);
            }

            // Store the course session data in the product for later continuation
            $product->update([
                'files' => json_encode([
                    'course_session_data' => $courseData,
                    'is_draft_continuation' => true
                ])
            ]);

            // Clear course creation session data but keep draft_course_id
            session()->forget('course_creation_data');

            return response()->json([
                'success' => true,
                'message' => 'Course saved as draft successfully',
                'product_id' => $product->id,
                'redirect_url' => route('seller.products.index')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save course as draft: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Continue editing a draft course
     */
    public function continueDraftCourse(Product $product)
    {
        // Verify this is a draft course belonging to the current user
        if ($product->seller_id !== auth()->id() || $product->status !== 'draft' || $product->content_type !== 'course') {
            abort(404);
        }

        // Load the course session data from the product
        $files = json_decode($product->files, true);
        $courseData = $files['course_session_data'] ?? [];

        if (!empty($courseData)) {
            // Restore the session data
            session(['course_creation_data' => $courseData]);

            // Store the draft product ID in session so we can update it instead of creating new
            session(['draft_course_id' => $product->id]);

            // Determine which step to redirect to
            $step = 1;
            if (!empty($courseData['step1'])) {
                $step = 2; // If step1 is complete, go to step2
            }

            return redirect()->route('seller.products.create-course-step', ['step' => $step])
                ->with('success', 'Continuing draft course...');
        }

        // If no session data, start from step 1
        return redirect()->route('seller.products.create-course-step', ['step' => 1])
            ->with('info', 'Starting course creation from the beginning...');
    }

    /**
     * Create the final course product from all steps
     */
    private function createCourseFromSteps()
    {
        $courseData = session('course_creation_data', []);

        if (empty($courseData['step1']) || empty($courseData['step2'])) {
            return redirect()->route('seller.products.create-course-step', ['step' => 1])
                ->with('error', 'Please complete all steps before creating the course.');
        }

        $step1Data = $courseData['step1'];
        $step2Data = $courseData['step2'];
        $step3Data = $courseData['step3'] ?? ['content' => []]; // Content is optional

        // Check if we're updating an existing draft
        $draftCourseId = session('draft_course_id');
        $product = null;

        if ($draftCourseId) {
            // Try to find the existing draft product
            $product = Product::where('id', $draftCourseId)
                ->where('seller_id', auth()->id())
                ->where('status', 'draft')
                ->where('content_type', 'course')
                ->first();
        }

        // Prepare product data
        $productData = [
            'name' => $step1Data['name'],
            'description' => $step1Data['description'],
            'category' => $step1Data['category'],
            'category_id' => $step1Data['category_id'],
            'subcategory_id' => $step1Data['subcategory_id'],
            'detailed_category_id' => $step1Data['detailed_category_id'],
            'price' => $step1Data['price'],
            'discount_price' => $step1Data['discount_price'] ?? null,
            'status' => 'active',
            'content_type' => 'course',
            'product_type' => 'course',
            'files' => json_encode([]), // Courses don't use the files field
        ];

        if ($product) {
            // Update existing draft product
            // Generate unique slug, excluding the current product
            $baseSlug = \Illuminate\Support\Str::slug($step1Data['name']);
            $slug = $baseSlug;
            $counter = 1;

            while (Product::where('slug', $slug)->where('id', '!=', $product->id)->exists()) {
                $slug = "{$baseSlug}-{$counter}";
                $counter++;
            }
            $productData['slug'] = $slug;

            $product->update($productData);
        } else {
            // Create new product
            $productData['seller_id'] = auth()->id();

            // Generate unique slug
            $baseSlug = \Illuminate\Support\Str::slug($step1Data['name']);
            $slug = $baseSlug;
            $counter = 1;

            while (Product::where('slug', $slug)->exists()) {
                $slug = "{$baseSlug}-{$counter}";
                $counter++;
            }
            $productData['slug'] = $slug;

            $product = Product::create($productData);
        }

        // Handle primary image
        if (!empty($step1Data['images'])) {
            $firstImage = $step1Data['images'][0];
            // Move from course draft to permanent location
            $tempPath = $firstImage['path'];

            // Handle both old temp path and new user-specific path
            if (strpos($tempPath, 'temp/course-images') !== false) {
                $permanentPath = str_replace('temp/course-images', 'products/images', $tempPath);
            } else {
                // New user-specific path structure
                $filename = basename($tempPath);
                $permanentPath = "products/images/{$filename}";
            }

            // Ensure products/images directory exists
            Storage::disk('public')->makeDirectory('products/images');

            // Move the file if it exists (and avoid moving to same location)
            if (Storage::disk('public')->exists($tempPath) && $tempPath !== $permanentPath) {
                Storage::disk('public')->move($tempPath, $permanentPath);
            }

            // Update the product with the image path
            $product->update(['image' => $permanentPath]);
        }

        // Handle additional images
        if (!empty($step1Data['images'])) {
            // If updating existing draft, clear old images first
            if ($draftCourseId && $product->wasRecentlyCreated === false) {
                $product->images()->delete();
            }

            foreach ($step1Data['images'] as $index => $imageData) {
                if ($index === 0) continue; // Skip first image as it's already set as primary

                // Move from course draft to permanent location
                $tempPath = $imageData['path'];

                // Handle both old temp path and new user-specific path
                if (strpos($tempPath, 'temp/course-images') !== false) {
                    $permanentPath = str_replace('temp/course-images', 'products/images', $tempPath);
                } else {
                    // New user-specific path structure
                    $filename = basename($tempPath);
                    $permanentPath = "products/images/{$filename}";
                }

                // Move the file if it exists (and avoid moving to same location)
                if (Storage::disk('public')->exists($tempPath) && $tempPath !== $permanentPath) {
                    Storage::disk('public')->move($tempPath, $permanentPath);
                }

                $product->images()->create([
                    'path' => $permanentPath,
                    'alt_text' => $product->name,
                    'sort_order' => $index + 1,
                ]);
            }
        }

        // Create course structure and content
        $this->createCourseStructure($product, $step2Data, $step3Data, $draftCourseId ? true : false);

        // Clear session data
        session()->forget('course_creation_data');
        session()->forget('draft_course_id');

        return redirect()->route('seller.products.show', $product)
            ->with('success', 'Course created successfully!');
    }

    /**
     * Create course structure (chapters, sub-chapters, and content)
     */
    private function createCourseStructure($product, $structureData, $contentData, $isUpdatingDraft = false)
    {
        // If updating an existing draft, clear old resources first
        if ($isUpdatingDraft) {
            $product->resources()->delete();
        }

        $resourceCounter = 1;

        foreach ($structureData['chapters'] as $chapterIndex => $chapterData) {
            // Create chapter as a resource with type 'chapter'
            $chapter = $product->resources()->create([
                'title' => $chapterData['title'],
                'description' => $chapterData['description'] ?? '',
                'type' => 'chapter',
                'content' => '',
                'metadata' => [
                    'chapter_index' => $chapterIndex,
                    'is_chapter' => true,
                ],
                'sort_order' => $resourceCounter++,
                'is_active' => true,
                'is_preview' => false,
            ]);

            // Create sub-chapters (lessons) under this chapter
            foreach ($chapterData['sub_chapters'] as $subChapterIndex => $subChapterData) {
                $subChapter = $product->resources()->create([
                    'title' => $subChapterData['title'],
                    'description' => $subChapterData['description'] ?? '',
                    'type' => 'sub_chapter',
                    'content' => '',
                    'metadata' => [
                        'chapter_index' => $chapterIndex,
                        'sub_chapter_index' => $subChapterIndex,
                        'parent_chapter_id' => $chapter->id,
                        'is_sub_chapter' => true,
                    ],
                    'sort_order' => $resourceCounter++,
                    'is_active' => true,
                    'is_preview' => false,
                ]);

                // Add content resources for this sub-chapter
                $subChapterContent = collect($contentData['content'])
                    ->where('chapter_index', $chapterIndex)
                    ->where('sub_chapter_index', $subChapterIndex)
                    ->first();

                if ($subChapterContent && !empty($subChapterContent['resources'])) {
                    foreach ($subChapterContent['resources'] as $resourceData) {
                        $resourceContent = '';
                        $metadata = [
                            'chapter_index' => $chapterIndex,
                            'sub_chapter_index' => $subChapterIndex,
                            'parent_sub_chapter_id' => $subChapter->id,
                            'is_content_resource' => true,
                        ];

                        // Handle different resource types
                        switch ($resourceData['type']) {
                            case 'video':
                            case 'link':
                                $resourceContent = $resourceData['content'] ?? '';
                                if ($resourceData['type'] === 'video') {
                                    $metadata['video_platform'] = $this->detectVideoPlatform($resourceContent);
                                }
                                break;

                            case 'text':
                                $resourceContent = $resourceData['content'] ?? '';
                                break;

                            case 'file':
                            case 'pdf':
                            case 'audio':
                            case 'document':
                            case 'image':
                                // Handle file uploads (if any)
                                if (!empty($resourceData['file_path'])) {
                                    $resourceContent = $resourceData['file_path'];
                                    $metadata = array_merge($metadata, [
                                        'original_name' => $resourceData['original_name'] ?? '',
                                        'size' => $resourceData['size'] ?? 0,
                                        'mime_type' => $resourceData['mime_type'] ?? '',
                                    ]);
                                }
                                break;
                        }

                        $product->resources()->create([
                            'title' => $resourceData['title'],
                            'description' => $resourceData['description'] ?? '',
                            'type' => $resourceData['type'],
                            'content' => $resourceContent,
                            'metadata' => $metadata,
                            'sort_order' => $resourceCounter++,
                            'is_active' => true,
                            'is_preview' => isset($resourceData['is_preview']) && $resourceData['is_preview'] === '1',
                        ]);
                    }
                }
            }
        }
    }

    public function store(Request $request)
    {
        // We don't need this anymore as we're not validating against a fixed list of legacy codes
        // $validLegacyCodes = \App\Models\ProductSubcategory::pluck('legacy_code')->filter()->toArray();

        // Basic validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string',
            'category_id' => 'nullable|exists:product_categories,id',
            'subcategory_id' => 'nullable|exists:product_subcategories,id',
            'detailed_category_id' => 'nullable|exists:product_detailed_categories,id',
            'price' => 'required|numeric|min:5000',
            'discount_price' => 'nullable|numeric|min:5000|lt:price',
            'status' => 'required|in:draft,active',
            'content_type' => 'required|in:simple,course',
            'product_type' => 'required|in:ebook,digital,course',
        ];

        // Filter out images that exceed size limit or exceed max count
        $validImages = [];
        if ($request->hasFile('images')) {
            $imageCount = 0;
            foreach ($request->file('images') as $index => $image) {
                if ($imageCount >= 10) { // Maximum 10 images
                    break;
                }
                if ($image->getSize() <= 2 * 1024 * 1024) { // 2MB in bytes
                    $validImages[] = $image;
                    $imageCount++;
                }
            }
        }

        // Filter out files that exceed size limit or exceed max count
        $validFiles = [];
        if ($request->hasFile('files')) {
            $fileCount = 0;
            foreach ($request->file('files') as $index => $file) {
                if ($fileCount >= 5) { // Maximum 5 files
                    break;
                }
                if ($file->getSize() <= 20 * 1024 * 1024) { // 20MB in bytes
                    $validFiles[] = $file;
                    $fileCount++;
                }
            }
        }

        // If status is active, check if we have at least one valid image and file
        if ($request->input('status') === 'active') {
            // For new products, we simply check if there are valid uploads
            // Check if we have at least one valid image
            if (count($validImages) === 0) {
                return response()->json([
                    'success' => false,
                    'errors' => ['images' => ['⚠️ At least one valid image (max 2MB each, up to 10 images) is required when publishing a product.']]
                ], 422);
            }

            // Check if the number of images exceeds the limit
            if (count($validImages) > 10) {
                return response()->json([
                    'success' => false,
                    'errors' => ['images' => ['⚠️ You can only upload a maximum of 10 images per product. You attempted to upload ' . count($validImages) . ' images.']]
                ], 422);
            }

            // Check content requirements based on product type
            $contentType = $request->input('content_type', 'simple');

            if ($contentType === 'simple') {
                if (count($validFiles) === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['files' => ['⚠️ At least one valid file (max 20MB each, up to 5 files) is required when publishing a simple product.']]
                    ], 422);
                }
            } else {
                // For course type, check if we have at least one resource
                $resources = $request->input('resources', []);
                $hasValidResources = false;

                foreach ($resources as $resource) {
                    if (!empty($resource['title']) && !empty($resource['type'])) {
                        $hasValidResources = true;
                        break;
                    }
                }

                if (!$hasValidResources) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['resources' => ['⚠️ At least one valid resource is required when publishing a course.']]
                    ], 422);
                }
            }

            // Check if the number of files exceeds the limit
            if (count($validFiles) > 5) {
                return response()->json([
                    'success' => false,
                    'errors' => ['files' => ['⚠️ You can only upload a maximum of 5 files per product. You attempted to upload ' . count($validFiles) . ' files.']]
                ], 422);
            }
        }

        // Log validation for debugging
        Log::info('Product create validation', [
            'status' => $request->input('status'),
            'valid_images' => count($validImages),
            'valid_files' => count($validFiles)
        ]);

        // Validate the basic fields
        $validated = $request->validate($rules);

        $productData = $validated;
        $productData['seller_id'] = auth()->id();

        // Generate unique slug
        $baseSlug = Str::slug($request->name); // e.g., "survey ipds" -> "survey-ipds"
        $slug = $baseSlug;
        $counter = 1;

        // Check for duplicate slugs and append a counter if needed
        while (Product::where('slug', $slug)->exists()) {
            $slug = "{$baseSlug}-{$counter}";
            $counter++;
        }
        $productData['slug'] = $slug;

        // For backward compatibility, store the first valid image in the image field
        if (count($validImages) > 0) {
            $path = $validImages[0]->store('products/images', 'public');
            $productData['image'] = $path;
        }

        // Handle files upload - only use valid files
        $files = [];
        foreach ($validFiles as $file) {
            $path = $file->store('products/files', 'public');
            $files[] = [
                'name' => $file->getClientOriginalName(),
                'path' => $path,
                'size' => $file->getSize(),
            ];
        }
        $productData['files'] = json_encode($files);

        // Create the product
        $product = Product::create($productData);

        // Handle course resources if content type is course
        if ($request->input('content_type') === 'course') {
            $this->handleCourseResources($request, $product);
        }

        // Handle multiple image uploads - only use valid images
        // Get the order of images from the request
        $imageOrder = $request->input('image_order', []);
        $hasCustomOrder = !empty($imageOrder);

        // Process each valid uploaded image
        $uploadedImages = [];
        foreach ($validImages as $index => $image) {
            $path = $image->store('products/images', 'public');

            // Store the uploaded image info for later processing
            $uploadedImages[] = [
                'path' => $path,
                'original_index' => $index,
                'name' => $image->getClientOriginalName()
            ];
        }

        // If we have a custom order, reorder the uploaded images
        if ($hasCustomOrder) {
            $orderedImages = [];
            foreach ($imageOrder as $imageName) {
                // Find the image with this name
                foreach ($uploadedImages as $uploadedImage) {
                    if ($uploadedImage['name'] === $imageName) {
                        $orderedImages[] = $uploadedImage;
                        break;
                    }
                }
            }

            // Add any images that weren't in the order array
            foreach ($uploadedImages as $uploadedImage) {
                if (!in_array($uploadedImage, $orderedImages)) {
                    $orderedImages[] = $uploadedImage;
                }
            }

            $uploadedImages = $orderedImages;
        }

        // Now create the product image records in the correct order
        foreach ($uploadedImages as $index => $uploadedImage) {
            $product->images()->create([
                'path' => $uploadedImage['path'],
                'is_primary' => $index === 0, // First image is primary
                'sort_order' => $index,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Product created successfully!',
            'redirect' => route('seller.products.index'),
        ], 200);
    }

    public function edit(Product $product)
    {
        // Load product resources if it's a course
        $product->load('resources');

        // Get all categories with their subcategories and detailed categories
        $categoryTree = \App\Models\ProductCategory::active()
            ->with(['activeSubcategories', 'activeSubcategories.activeDetailedCategories'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // For backward compatibility, generate legacy category groups from the database
        $categoryGroups = [];
        $legacyCategoryMapping = [];
        $legacySubcategoryMapping = [];

        foreach ($categoryTree as $category) {
            $categoryGroups[$category->name] = [];

            foreach ($category->activeSubcategories as $subcategory) {
                try {
                    // Try to use the legacy_code if available, otherwise use the slug or create one from the name
                    // This is wrapped in a try-catch in case the legacy_code column doesn't exist yet
                    $legacyCode = $subcategory->legacy_code;
                    if (empty($legacyCode)) {
                        $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                    }
                } catch (\Exception $e) {
                    // If there's an error (like the column doesn't exist), use the slug or create one from the name
                    $legacyCode = $subcategory->getLegacyCategoryAttribute() ?? \Illuminate\Support\Str::slug($subcategory->name);
                }

                // Add to the category groups
                $categoryGroups[$category->name][$legacyCode] = $subcategory->name;

                // Build the mappings for JavaScript
                $legacyCategoryMapping[$legacyCode] = $category->name;
                $legacySubcategoryMapping[$legacyCode] = $subcategory->name;
            }
        }

        // Determine product type and content type for the edit view
        $productType = $product->product_type ?? 'digital'; // Default to digital for backward compatibility
        $contentType = $product->content_type ?? 'simple'; // Default to simple for backward compatibility

        return view('seller.products.edit', compact('product', 'categoryGroups', 'categoryTree', 'legacyCategoryMapping', 'legacySubcategoryMapping', 'productType', 'contentType'));
    }

    public function update(Request $request, Product $product)
    {
        // Basic validation rules
        $rules = [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|string',
            'category_id' => 'nullable|exists:product_categories,id',
            'subcategory_id' => 'nullable|exists:product_subcategories,id',
            'detailed_category_id' => 'nullable|exists:product_detailed_categories,id',
            'price' => 'required|numeric|min:5000',
            'discount_price' => 'nullable|numeric|min:5000|lt:price',
            'status' => 'required|in:draft,active',
            'content_type' => 'required|in:simple,course',
            'product_type' => 'nullable|in:ebook,digital,course', // Allow nullable for existing products
        ];

        // Filter out images that exceed size limit or exceed max count
        $validImages = [];
        if ($request->hasFile('images')) {
            $imageCount = 0;
            foreach ($request->file('images') as $index => $image) {
                if ($imageCount >= 10) { // Maximum 10 images
                    break;
                }
                if ($image->getSize() <= 2 * 1024 * 1024) { // 2MB in bytes
                    $validImages[] = $image;
                    $imageCount++;
                }
            }
        }

        // Filter out files that exceed size limit or exceed max count
        $validFiles = [];
        if ($request->hasFile('files')) {
            $fileCount = 0;
            foreach ($request->file('files') as $index => $file) {
                if ($fileCount >= 5) { // Maximum 5 files
                    break;
                }
                if ($file->getSize() <= 20 * 1024 * 1024) { // 20MB in bytes
                    $validFiles[] = $file;
                    $fileCount++;
                }
            }
        }

        // Get existing files
        $existingFiles = json_decode($product->files, true) ?: [];
        $removeFiles = array_filter(
            $request->input('remove_files', []),
            fn($v) => is_string($v) || is_int($v)
        );
        $remainingFiles = array_diff_key($existingFiles, array_flip($removeFiles));

        // If status is active, check if we have at least one valid image and file
        if ($request->input('status') === 'active') {
            try {
                // Get the count of existing images that aren't being removed
                $existingImagesCount = $product->images()->count();
                $removeImages = $request->input('remove_images', []);
                if (!empty($removeImages)) {
                    $existingImagesCount = $product->images()->whereNotIn('id', $removeImages)->count();
                }

                // Check if the total number of images (existing + new) exceeds the limit
                if ($existingImagesCount + count($validImages) > 10) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['images' => ['⚠️ You can only have a maximum of 10 images per product. This product will have ' . ($existingImagesCount + count($validImages)) . ' images after upload.']]
                    ], 422);
                }

                // Check if the total number of files (existing + new) exceeds the limit
                $existingFilesCount = count($remainingFiles);
                if ($existingFilesCount + count($validFiles) > 5) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['files' => ['⚠️ You can only have a maximum of 5 files per product. This product will have ' . ($existingFilesCount + count($validFiles)) . ' files after upload.']]
                    ], 422);
                }

                // Only require new images if there are no existing ones after removals
                if (count($validImages) === 0 && $existingImagesCount === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['images' => ['⚠️ At least one valid image (max 2MB each, up to 10 images) is required when publishing a product. Please ensure your images meet these requirements.']]
                    ], 422);
                }

                // Check content requirements based on product type
                $contentType = $request->input('content_type', 'simple');

                if ($contentType === 'simple') {
                    // Check if we have at least one file (existing or new valid upload)
                    if (empty($remainingFiles) && count($validFiles) === 0) {
                        return response()->json([
                            'success' => false,
                            'errors' => ['files' => ['⚠️ At least one valid file (max 20MB each, up to 5 files) is required when publishing a simple product. Please ensure your files meet these requirements.']]
                        ], 422);
                    }
                } else {
                    // For course type, check if we have at least one resource
                    $resources = $request->input('resources', []);
                    $hasValidResources = false;

                    foreach ($resources as $resource) {
                        if (!empty($resource['title']) && !empty($resource['type'])) {
                            $hasValidResources = true;
                            break;
                        }
                    }

                    if (!$hasValidResources) {
                        return response()->json([
                            'success' => false,
                            'errors' => ['resources' => ['⚠️ At least one valid resource is required when publishing a course.']]
                        ], 422);
                    }
                }
            } catch (\Exception $e) {
                // If there's an error (like product is null), log it and require at least one valid file and image
                Log::error('Error in product update validation', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                if (count($validImages) === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['images' => ['⚠️ At least one valid image (max 2MB each, up to 10 images) is required when publishing a product. Please ensure your images meet these requirements.']]
                    ], 422);
                }

                if (count($validFiles) === 0) {
                    return response()->json([
                        'success' => false,
                        'errors' => ['files' => ['⚠️ At least one valid file (max 20MB each, up to 5 files) is required when publishing a product. Please ensure your files meet these requirements.']]
                    ], 422);
                }
            }
        }

        // Log the counts for debugging
        Log::info('Product update validation', [
            'product_id' => $product->id,
            'status' => $request->input('status'),
            'existing_images' => $product->images()->count(),
            'valid_images' => count($validImages),
            'remaining_files' => count($remainingFiles),
            'valid_files' => count($validFiles)
        ]);

        // Validate the basic fields
        $validated = $request->validate($rules);

        $productData = $validated;

        // Generate unique slug, excluding the current product
        $baseSlug = Str::slug($request->name);
        $slug = $baseSlug;
        $counter = 1;

        // Check for duplicate slugs, excluding the current product
        while (Product::where('slug', $slug)->where('id', '!=', $product->id)->exists()) {
            $slug = "{$baseSlug}-{$counter}";
            $counter++;
        }
        $productData['slug'] = $slug;

        // Handle images to be removed
        if ($removeImageIds = $request->input('remove_images', [])) {
            foreach ($removeImageIds as $imageId) {
                $image = $product->images()->find($imageId);
                if ($image) {
                    Storage::disk('public')->delete($image->path);
                    $image->delete();
                }
            }
        }

        // Handle image reordering
        if ($imageOrder = $request->input('image_order', [])) {
            foreach ($imageOrder as $index => $imageId) {
                $product->images()->where('id', $imageId)->update(['sort_order' => $index]);
            }
        }

        // Handle primary image setting
        if ($primaryImageId = $request->input('primary_image_id')) {
            // First, set all images as non-primary
            $product->images()->update(['is_primary' => false]);

            // Then set the selected image as primary
            $primaryImage = $product->images()->find($primaryImageId);
            if ($primaryImage) {
                $primaryImage->update(['is_primary' => true]);

                // Also update the main image field for backward compatibility
                $productData['image'] = $primaryImage->path;
            }
        }

        // Handle new image uploads - only use valid images
        if (count($validImages) > 0) {
            // For backward compatibility, update the main image field with the first valid image
            // Only update the main image field if there's no primary image yet
            // or if the main image field is empty
            $hasPrimaryImage = $product->images()->where('is_primary', true)->exists();

            if (!$hasPrimaryImage || !$product->image) {
                $path = $validImages[0]->store('products/images', 'public');
                $productData['image'] = $path;
            }

            // Process all valid images
            $existingImagesCount = $product->images()->count();

            foreach ($validImages as $index => $image) {
                $path = $image->store('products/images', 'public');

                // Determine if this should be the primary image
                $isPrimary = false;

                // Make it primary if it's the first image and no primary exists
                if ($index === 0 && $product->images()->where('is_primary', true)->count() === 0) {
                    $isPrimary = true;
                }

                // Create a product image record
                $product->images()->create([
                    'path' => $path,
                    'is_primary' => $isPrimary,
                    'sort_order' => $existingImagesCount + $index,
                ]);
            }
        } elseif ($request->input('remove_all_images')) {
            // Delete all images
            foreach ($product->images as $image) {
                Storage::disk('public')->delete($image->path);
            }
            $product->images()->delete();

            // Also clear the main image field
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
                $productData['image'] = null;
            }
        }

        // Handle existing files removal
        $existingFiles = json_decode($product->files, true) ?: [];
        if ($removeFiles = $request->input('remove_files', [])) {
            foreach ($removeFiles as $index) {
                if (isset($existingFiles[$index])) {
                    Storage::disk('public')->delete($existingFiles[$index]['path']);
                    unset($existingFiles[$index]);
                }
            }
            $existingFiles = array_values($existingFiles); // Reindex array
        }

        // Handle new files upload - only use valid files
        foreach ($validFiles as $file) {
            $path = $file->store('products/files', 'public');
            $existingFiles[] = [
                'name' => $file->getClientOriginalName(),
                'path' => $path,
                'size' => $file->getSize(),
            ];
        }
        $productData['files'] = json_encode($existingFiles);

        $product->update($productData);

        // Handle course resources if content type is course
        if ($request->input('content_type') === 'course') {
            $this->handleCourseResourcesUpdate($request, $product);
        } elseif ($product->content_type === 'course' && $request->input('content_type') === 'simple') {
            // If switching from course to simple, remove all resources
            $product->resources()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Product updated successfully!',
            'redirect' => route('seller.products.index'),
        ], 200);
    }

    public function destroy(Product $product)
    {
        // Delete associated files
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        // Delete all product images
        foreach ($product->images as $image) {
            Storage::disk('public')->delete($image->path);
        }

        // Delete product files (handle different structures for simple vs course products)
        $files = json_decode($product->files, true) ?: [];

        // For course products, files may contain session data instead of actual files
        if ($product->content_type === 'course') {
            // Course products don't store files in the files field, they use ProductResource model
            // Just delete any course resources
            $product->resources()->delete();
        } else {
            // For simple products, delete the actual files
            foreach ($files as $file) {
                if (is_array($file) && isset($file['path'])) {
                    Storage::disk('public')->delete($file['path']);
                }
            }
        }

        $product->delete();

        return redirect()->route('seller.products.index')->with('success', 'Product deleted successfully!');
    }

    /**
     * Preview a product before it's published.
     *
     * @param  \App\Models\Product  $product
     * @return \Illuminate\Http\Response
     */
    public function preview(Product $product)
    {
        // Get the specific product with its category relationships
        $product = Product::where('seller_id', auth()->id())
            ->where('slug', $product->slug)
            ->where('status', 'active')
            ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->firstOrFail();

        // Check if we're using the new category structure
        $hasNewCategories = $product->category_id || $product->subcategory_id || $product->detailed_category_id;

        // Get related products
        $relatedQuery = Product::where('seller_id', auth()->id())
            ->where('status', 'active')
            ->where('id', '!=', $product->id);

        if ($hasNewCategories) {
            // Try to find related products by subcategory first
            if ($product->subcategory_id) {
                $relatedQuery->where('subcategory_id', $product->subcategory_id);
            } elseif ($product->category_id) {
                // Fallback to category
                $relatedQuery->where('category_id', $product->category_id);
            } else {
                // Fallback to legacy category
                $relatedQuery->where('category', $product->category);
            }
        } else {
            // Use legacy category field
            $relatedQuery->where('category', $product->category);
        }

        $relatedProducts = $relatedQuery->take(4)->get();

        // Check if the user has already purchased any of the related products
        $purchasedProductIds = [];
        if (auth()->check()) {
            $purchasedProductIds = Order::where('buyer_id', auth()->id())
                ->where('status', 'success')
                ->pluck('product_id')
                ->toArray();
        }

        $seller = SellerApplication::where('user_id', auth()->id())->firstOrFail();

        $seller->store_slug = $seller->store_name_slug;

        // Get category name for display
        if ($hasNewCategories) {
            if ($product->productDetailedCategory) {
                $categoryName = $product->productDetailedCategory->name;
            } elseif ($product->productSubcategory) {
                $categoryName = $product->productSubcategory->name;
            } elseif ($product->productCategory) {
                $categoryName = $product->productCategory->name;
            } else {
                $categoryName = ucfirst($product->category);
            }
        } else {
            $categoryName = ucfirst($product->category);
        }

        return view('seller.products.preview', compact('seller', 'product', 'relatedProducts', 'categoryName', 'hasNewCategories', 'purchasedProductIds'));
    }

    /**
     * Handle course resources creation and updates
     */
    private function handleCourseResources(Request $request, Product $product)
    {
        $resources = $request->input('resources', []);

        foreach ($resources as $resourceData) {
            // Skip empty resources
            if (empty($resourceData['title']) || empty($resourceData['type'])) {
                continue;
            }

            $resourceContent = '';
            $metadata = [];

            // Handle different resource types
            switch ($resourceData['type']) {
                case 'file':
                case 'pdf':
                case 'audio':
                case 'document':
                case 'image':
                    // Handle file upload
                    $fileKey = "resources.{$resourceData['sort_order']}.file";
                    if ($request->hasFile($fileKey)) {
                        $file = $request->file($fileKey);
                        $path = $file->store('products/resources', 'public');
                        $resourceContent = $path;
                        $metadata = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $file->getMimeType(),
                        ];
                    }
                    break;

                case 'link':
                case 'video':
                case 'text':
                    // Handle text/URL content
                    $resourceContent = $resourceData['content'] ?? '';
                    if ($resourceData['type'] === 'video') {
                        $metadata['video_platform'] = $this->detectVideoPlatform($resourceContent);
                    }
                    break;
            }

            // Create the resource
            $product->resources()->create([
                'title' => $resourceData['title'],
                'description' => $resourceData['description'] ?? '',
                'type' => $resourceData['type'],
                'content' => $resourceContent,
                'metadata' => $metadata,
                'sort_order' => $resourceData['sort_order'] ?? 0,
                'is_active' => true,
                'is_preview' => isset($resourceData['is_preview']) && $resourceData['is_preview'] === '1',
            ]);
        }
    }

    /**
     * Handle course resources updates (for editing existing products)
     */
    private function handleCourseResourcesUpdate(Request $request, Product $product)
    {
        $resources = $request->input('resources', []);
        $existingResourceIds = [];

        foreach ($resources as $resourceData) {
            // Skip empty resources
            if (empty($resourceData['title']) || empty($resourceData['type'])) {
                continue;
            }

            $resourceContent = '';
            $metadata = [];

            // Check if this is an existing resource
            $existingResource = null;
            if (!empty($resourceData['id'])) {
                $existingResource = $product->resources()->find($resourceData['id']);
                $existingResourceIds[] = $resourceData['id'];
            }

            // Handle different resource types
            switch ($resourceData['type']) {
                case 'file':
                case 'pdf':
                case 'audio':
                case 'document':
                case 'image':
                    // Handle file upload
                    $fileKey = "resources.{$resourceData['sort_order']}.file";
                    if ($request->hasFile($fileKey)) {
                        // New file uploaded
                        $file = $request->file($fileKey);
                        $path = $file->store('products/resources', 'public');
                        $resourceContent = $path;
                        $metadata = [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $file->getMimeType(),
                        ];

                        // Delete old file if updating existing resource
                        if ($existingResource && $existingResource->content) {
                            Storage::disk('public')->delete($existingResource->content);
                        }
                    } elseif ($existingResource) {
                        // Keep existing file
                        $resourceContent = $existingResource->content;
                        $metadata = $existingResource->metadata;
                    }
                    break;

                case 'link':
                case 'video':
                case 'text':
                    // Handle text/URL content
                    $resourceContent = $resourceData['content'] ?? '';
                    if ($resourceData['type'] === 'video') {
                        $metadata['video_platform'] = $this->detectVideoPlatform($resourceContent);
                    }
                    break;
            }

            $resourceUpdateData = [
                'title' => $resourceData['title'],
                'description' => $resourceData['description'] ?? '',
                'type' => $resourceData['type'],
                'content' => $resourceContent,
                'metadata' => $metadata,
                'sort_order' => $resourceData['sort_order'] ?? 0,
                'is_active' => true,
                'is_preview' => isset($resourceData['is_preview']) && $resourceData['is_preview'] === '1',
            ];

            if ($existingResource) {
                // Update existing resource
                $existingResource->update($resourceUpdateData);
            } else {
                // Create new resource
                $product->resources()->create($resourceUpdateData);
            }
        }

        // Remove resources that are no longer in the form
        $product->resources()
            ->whereNotIn('id', $existingResourceIds)
            ->get()
            ->each(function ($resource) {
                // Delete associated files
                if ($resource->isFileType() && $resource->content) {
                    Storage::disk('public')->delete($resource->content);
                }
                $resource->delete();
            });
    }

    /**
     * Detect video platform from URL
     */
    private function detectVideoPlatform($url)
    {
        if (strpos($url, 'youtube.com') !== false || strpos($url, 'youtu.be') !== false) {
            return 'youtube';
        } elseif (strpos($url, 'vimeo.com') !== false) {
            return 'vimeo';
        } elseif (strpos($url, 'dailymotion.com') !== false) {
            return 'dailymotion';
        }

        return 'other';
    }
}
