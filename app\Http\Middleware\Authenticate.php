<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        // For AJAX requests or JSON expected requests, return null to trigger JSON response
        if ($request->expectsJson() || $request->ajax()) {
            return null;
        }

        return route('login');
    }
}
