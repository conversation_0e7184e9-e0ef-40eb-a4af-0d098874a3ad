@extends('layouts.browse')

@section('title', 'Course Access Granted')

@push('styles')
<style>
.access-granted-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.access-granted-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    max-width: 600px;
    width: 100%;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.access-granted-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #48bb78, #38a169);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 1s ease-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.access-granted-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.access-granted-message {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.course-info {
    background: #f0fff4;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #48bb78;
}

.course-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.course-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #48bb78;
}

.stat-label {
    font-size: 0.875rem;
    color: #4a5568;
}

.countdown-container {
    background: #edf2f7;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.countdown-text {
    color: #4a5568;
    margin-bottom: 1rem;
}

.countdown-timer {
    font-size: 2rem;
    font-weight: 700;
    color: #48bb78;
    font-family: 'Courier New', monospace;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(72, 187, 120, 0.3);
}

.btn-secondary {
    background: white;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.purchase-info {
    background: #e6fffa;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    border: 1px solid #81e6d9;
}

.purchase-info-text {
    color: #234e52;
    font-size: 0.875rem;
    margin: 0;
}

@media (max-width: 640px) {
    .access-granted-card {
        padding: 2rem;
        margin: 1rem;
    }
    
    .access-granted-title {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
    
    .course-stats {
        gap: 1rem;
    }
}
</style>
@endpush

@section('content')
<div class="access-granted-container">
    <div class="access-granted-card">
        <!-- Icon -->
        <div class="access-granted-icon">
            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>

        <!-- Title -->
        <h1 class="access-granted-title">Welcome to Your Course!</h1>

        <!-- Message -->
        <p class="access-granted-message">
            Congratulations! You now have full access to this course. Start learning at your own pace and unlock your potential with our comprehensive course materials.
        </p>

        <!-- Course Info -->
        @if(isset($course))
        <div class="course-info">
            <div class="course-title">{{ $course->title }}</div>
            
            @if(isset($course->sections))
            <div class="course-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ $course->sections->count() }}</span>
                    <span class="stat-label">Sections</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ $course->sections->sum(function($section) { return $section->curriculumItems->count(); }) }}</span>
                    <span class="stat-label">Lessons</span>
                </div>
                @if($course->estimated_duration)
                <div class="stat-item">
                    <span class="stat-number">{{ $course->estimated_duration }}</span>
                    <span class="stat-label">Minutes</span>
                </div>
                @endif
            </div>
            @endif
        </div>
        @endif

        <!-- Purchase Info -->
        @if(isset($order))
        <div class="purchase-info">
            <p class="purchase-info-text">
                <strong>Order ID:</strong> {{ $order->order_id }} | 
                <strong>Purchase Date:</strong> {{ $order->created_at->format('M d, Y') }}
            </p>
        </div>
        @endif

        <!-- Countdown -->
        <div class="countdown-container">
            <div class="countdown-text">Taking you to your course in:</div>
            <div class="countdown-timer" id="countdown">10</div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            @if(isset($course))
                <a href="{{ route('browse.courses.access', $course) }}" class="btn btn-primary">
                    Start Learning Now
                </a>
                <a href="{{ route('browse.courses.show', $course) }}" class="btn btn-secondary">
                    Course Details
                </a>
            @endif
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let countdown = 10;
    const countdownElement = document.getElementById('countdown');
    const redirectUrl = @if(isset($course)) "{{ route('browse.courses.access', $course) }}" @else "{{ route('browse.courses') }}" @endif;
    
    const timer = setInterval(function() {
        countdown--;
        countdownElement.textContent = countdown;
        
        if (countdown <= 0) {
            clearInterval(timer);
            window.location.href = redirectUrl;
        }
    }, 1000);
    
    // Allow users to cancel the redirect by clicking anywhere
    document.addEventListener('click', function() {
        clearInterval(timer);
        countdownElement.textContent = 'Redirect cancelled';
        countdownElement.style.color = '#a0aec0';
    });
});
</script>
@endsection
