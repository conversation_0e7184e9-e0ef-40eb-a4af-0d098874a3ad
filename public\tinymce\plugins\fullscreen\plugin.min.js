/**
 * TinyMCE version 6.0.1 (2022-03-23)
 */
!function(){"use strict";const e=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}};var t=tinymce.util.Tools.resolve("tinymce.PluginManager");const n=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=r=e,(o=String).prototype.isPrototypeOf(n)||(null===(s=r.constructor)||void 0===s?void 0:s.name)===o.name)?"string":t;var n,r,o,s})(t)===e,r=e=>t=>typeof t===e,o=n("string"),s=n("array"),i=(null,e=>null===e);const l=r("boolean"),a=e=>!(e=>null==e)(e),c=r("function"),u=r("number"),d=()=>{},m=e=>()=>e;function h(e,...t){return(...n)=>{const r=t.concat(n);return e.apply(null,r)}}const g=m(!1),p=m(!0);class f{constructor(e,t){this.tag=e,this.value=t}static some(e){return new f(!0,e)}static none(){return f.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?f.some(e(this.value)):f.none()}bind(e){return this.tag?e(this.value):f.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:f.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return a(e)?f.some(e):f.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}f.singletonNone=new f(!1);const v=t=>{const n=e(f.none()),r=()=>n.get().each(t);return{clear:()=>{r(),n.set(f.none())},isSet:()=>n.get().isSome(),get:()=>n.get(),set:e=>{r(),n.set(f.some(e))}}},w=()=>v((e=>e.unbind())),y=Array.prototype.push,b=(e,t)=>{const n=e.length,r=new Array(n);for(let o=0;o<n;o++){const n=e[o];r[o]=t(n,o)}return r},S=(e,t)=>{for(let n=0,r=e.length;n<r;n++)t(e[n],n)},x=(e,t)=>{const n=[];for(let r=0,o=e.length;r<o;r++){const o=e[r];t(o,r)&&n.push(o)}return n},E=(e,t)=>((e,t,n)=>{for(let r=0,o=e.length;r<o;r++){const o=e[r];if(t(o,r))return f.some(o);if(n(o,r))break}return f.none()})(e,t,g),F=Object.keys,O=(e,t)=>-1!==e.indexOf(t),T=e=>void 0!==e.style&&c(e.style.getPropertyValue),k=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},C=k;"undefined"!=typeof window?window:Function("return this;")();const A=e=>t=>(e=>e.dom.nodeType)(t)===e,R=A(1),L=A(3),M=A(9),N=A(11),P=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},D=e=>C(e.dom.ownerDocument),W=e=>b(e.dom.childNodes,C),q=c(Element.prototype.attachShadow)&&c(Node.prototype.getRootNode),H=m(q),I=q?e=>C(e.dom.getRootNode()):e=>M(e)?e:D(e),B=e=>{const t=I(e);return N(n=t)&&a(n.dom.host)?f.some(t):f.none();var n},V=e=>C(e.dom.host),_=e=>{const t=L(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return B(C(t)).fold((()=>n.body.contains(t)),(r=_,o=V,e=>r(o(e))));var r,o},j=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},z=(e,t)=>{e.dom.removeAttribute(t)},$=(e,t)=>{const n=e.dom;((e,t)=>{const n=F(e);for(let r=0,o=n.length;r<o;r++){const o=n[r];t(e[o],o)}})(t,((e,t)=>{((e,t,n)=>{if(!o(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);T(e)&&e.style.setProperty(t,n)})(n,t,e)}))},U=e=>{const t=C((e=>{if(H()&&a(e.target)){const t=C(e.target);if(R(t)&&a(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return((e,t)=>0<e.length?f.some(e[0]):f.none())(t)}}return f.from(e.target)})(e).getOr(e.target)),n=()=>e.stopPropagation(),r=()=>e.preventDefault(),o=(s=r,i=n,(...e)=>s(i.apply(null,e)));var s,i;return((e,t,n,r,o,s,i)=>({target:e,x:t,y:n,stop:r,prevent:o,kill:s,raw:i}))(t,e.clientX,e.clientY,n,r,o,e)},K=(e,t,n,r)=>{e.dom.removeEventListener(t,n,r)},X=p,Y=(e,t,n)=>((e,t,n,r)=>((e,t,n,r,o)=>{const s=((e,t)=>n=>{e(n)&&t(U(n))})(n,r);return e.dom.addEventListener(t,s,o),{unbind:h(K,e,t,s,o)}})(e,t,n,r,!1))(e,t,X,n),G=()=>J(0,0),J=(e,t)=>({major:e,minor:t}),Q={nu:J,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?G():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const r=e[n];if(r.test(t))return r}})(e,t);if(!n)return{major:0,minor:0};const r=e=>Number(t.replace(n,"$"+e));return J(r(1),r(2))})(e,n)},unknown:G},Z=(e,t)=>{const n=String(t).toLowerCase();return E(e,(e=>e.search(n)))},ee=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,te=e=>t=>O(t,e),ne=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>O(e,"edge/")&&O(e,"chrome")&&O(e,"safari")&&O(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ee],search:e=>O(e,"chrome")&&!O(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>O(e,"msie")||O(e,"trident")},{name:"Opera",versionRegexes:[ee,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:te("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:te("firefox")},{name:"Safari",versionRegexes:[ee,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(O(e,"safari")||O(e,"mobile/"))&&O(e,"applewebkit")}],re=[{name:"Windows",search:te("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>O(e,"iphone")||O(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:te("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:te("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:te("linux"),versionRegexes:[]},{name:"Solaris",search:te("sunos"),versionRegexes:[]},{name:"FreeBSD",search:te("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:te("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],oe={browsers:m(ne),oses:m(re)},se="Edge",ie="Chromium",le="Opera",ae="Firefox",ce="Safari",ue=e=>{const t=e.current,n=e.version,r=e=>()=>t===e;return{current:t,version:n,isEdge:r(se),isChromium:r(ie),isIE:r("IE"),isOpera:r(le),isFirefox:r(ae),isSafari:r(ce)}},de=()=>ue({current:void 0,version:Q.unknown()}),me=ue,he=(m(se),m(ie),m("IE"),m(le),m(ae),m(ce),"Windows"),ge="Android",pe="Linux",fe="macOS",ve="Solaris",we="FreeBSD",ye="ChromeOS",be=e=>{const t=e.current,n=e.version,r=e=>()=>t===e;return{current:t,version:n,isWindows:r(he),isiOS:r("iOS"),isAndroid:r(ge),isMacOS:r(fe),isLinux:r(pe),isSolaris:r(ve),isFreeBSD:r(we),isChromeOS:r(ye)}},Se=()=>be({current:void 0,version:Q.unknown()}),xe=be,Ee=(m(he),m("iOS"),m(ge),m(pe),m(fe),m(ve),m(we),m(ye),(e,t,n)=>{const r=oe.browsers(),o=oe.oses(),s=t.bind((e=>((e,t)=>((e,t)=>{for(let n=0;n<e.length;n++){const r=t(e[n]);if(r.isSome())return r}return f.none()})(t.brands,(t=>{const n=t.brand.toLowerCase();return E(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Q.nu(parseInt(t.version,10),0)})))})))(r,e))).orThunk((()=>((e,t)=>Z(e,t).map((e=>{const n=Q.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e))).fold(de,me),i=((e,t)=>Z(e,t).map((e=>{const n=Q.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e).fold(Se,xe),l=((e,t,n,r)=>{const o=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!o,i=e.isiOS()||e.isAndroid(),l=i||r("(pointer:coarse)"),a=o||!s&&i&&r("(min-device-width:768px)"),c=s||i&&!a,u=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),d=!c&&!a&&!u;return{isiPad:m(o),isiPhone:m(s),isTablet:m(a),isPhone:m(c),isTouch:m(l),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:m(u),isDesktop:m(d)}})(i,s,e,n);return{browser:s,os:i,deviceType:l}}),Fe=e=>window.matchMedia(e).matches;let Oe=(e=>{let t,n=!1;return(...r)=>(n||(n=!0,t=e.apply(null,r)),t)})((()=>Ee(navigator.userAgent,f.from(navigator.userAgentData),Fe)));const Te=(e,t)=>({left:e,top:t,translate:(n,r)=>Te(e+n,t+r)}),ke=Te,Ce=e=>{const t=void 0===e?window:e;return Oe().browser.isFirefox()?f.none():f.from(t.visualViewport)},Ae=(e,t,n,r)=>({x:e,y:t,width:n,height:r,right:e+n,bottom:t+r}),Re=e=>{const t=void 0===e?window:e,n=t.document,r=(e=>{const t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,r=t.body.scrollTop||t.documentElement.scrollTop;return ke(n,r)})(C(n));return Ce(t).fold((()=>{const e=t.document.documentElement,n=e.clientWidth,o=e.clientHeight;return Ae(r.left,r.top,n,o)}),(e=>Ae(Math.max(e.pageLeft,r.left),Math.max(e.pageTop,r.top),e.width,e.height)))},Le=(e,t,n)=>Ce(n).map((n=>{const r=e=>t(U(e));return n.addEventListener(e,r),{unbind:()=>n.removeEventListener(e,r)}})).getOrThunk((()=>({unbind:d})));var Me=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Ne=tinymce.util.Tools.resolve("tinymce.Env");const Pe=(e,t)=>{e.dispatch("FullscreenStateChanged",{state:t})},De=("fullscreen_native",e=>e.options.get("fullscreen_native"));const We=e=>{return e.dom===(void 0!==(t=D(e).dom).fullscreenElement?t.fullscreenElement:void 0!==t.msFullscreenElement?t.msFullscreenElement:void 0!==t.webkitFullscreenElement?t.webkitFullscreenElement:null);var t},qe=(e,t,n)=>((e,t,n)=>x(((e,t)=>{const n=c(t)?t:g;let r=e.dom;const o=[];for(;null!==r.parentNode&&void 0!==r.parentNode;){const e=r.parentNode,t=C(e);if(o.push(t),!0===n(t))break;r=e}return o})(e,n),t))(e,(e=>P(e,t)),n),He=(e,t)=>((e,n)=>{return x((e=>f.from(e.dom.parentNode).map(C))(r=e).map(W).map((e=>x(e,(e=>{return t=e,!(r.dom===t.dom);var t})))).getOr([]),(e=>P(e,t)));var r})(e),Ie="data-ephox-mobile-fullscreen-style",Be="position:absolute!important;",Ve="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",_e=Ne.os.isAndroid(),je=e=>{const t=((e,t)=>{const n=e.dom,r=window.getComputedStyle(n).getPropertyValue(t);return""!==r||_(e)?r:((e,t)=>T(e)?e.style.getPropertyValue(t):"")(n,t)})(e,"background-color");return void 0!==t&&""!==t?"background-color:"+t+"!important":"background-color:rgb(255,255,255)!important;"},ze=Me.DOM,$e=Ce().fold((()=>({bind:d,unbind:d})),(e=>{const t=(()=>{const e=v(d);return{...e,on:t=>e.get().each(t)}})(),n=w(),r=w(),o=((e,t)=>{let n=null;return{cancel:()=>{i(n)||(clearTimeout(n),n=null)},throttle:(...t)=>{i(n)&&(n=setTimeout((()=>{n=null,e.apply(null,t)}),50))}}})((()=>{document.body.scrollTop=0,document.documentElement.scrollTop=0,window.requestAnimationFrame((()=>{t.on((t=>$(t,{top:e.offsetTop+"px",left:e.offsetLeft+"px",height:e.height+"px",width:e.width+"px"})))}))}));return{bind:e=>{t.set(e),o.throttle(),n.set(Le("resize",o.throttle)),r.set(Le("scroll",o.throttle))},unbind:()=>{t.on((()=>{n.clear(),r.clear()})),t.clear()}}})),Ue=(e,t)=>{const n=document.body,r=document.documentElement,i=e.getContainer(),a=C(i),c=(e=>{const t=C(e.getElement());return B(t).map(V).getOrThunk((()=>(e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return C(t)})(D(t))))})(e),d=t.get(),m=C(e.getBody()),h=Ne.deviceType.isTouch(),g=i.style,p=e.iframeElement.style,v=e=>{e(n,"tox-fullscreen"),e(r,"tox-fullscreen"),e(i,"tox-fullscreen"),B(a).map((e=>V(e).dom)).each((t=>{e(t,"tox-fullscreen"),e(t,"tox-shadowhost")}))},w=()=>{h&&(e=>{const t=((e,t)=>{const n=document;return 1!==(r=n).nodeType&&9!==r.nodeType&&11!==r.nodeType||0===r.childElementCount?[]:b(n.querySelectorAll(e),C);var r})("["+Ie+"]");S(t,(t=>{const n=j(t,Ie);"no-styles"!==n?$(t,e.parseStyle(n)):z(t,"style"),z(t,Ie)}))})(e.dom),v(ze.removeClass),$e.unbind(),f.from(t.get()).each((e=>e.fullscreenChangeHandler.unbind()))};if(d)d.fullscreenChangeHandler.unbind(),De(e)&&We(c)&&(e=>{const t=e.dom;t.exitFullscreen?t.exitFullscreen():t.msExitFullscreen?t.msExitFullscreen():t.webkitCancelFullScreen&&t.webkitCancelFullScreen()})(D(c)),p.width=d.iframeWidth,p.height=d.iframeHeight,g.width=d.containerWidth,g.height=d.containerHeight,g.top=d.containerTop,g.left=d.containerLeft,w(),x=d.scrollPos,window.scrollTo(x.x,x.y),t.set(null),Pe(e,!1),e.off("remove",w);else{const n=Y(D(c),void 0!==document.fullscreenElement?"fullscreenchange":void 0!==document.msFullscreenElement?"MSFullscreenChange":void 0!==document.webkitFullscreenElement?"webkitfullscreenchange":"fullscreenchange",(n=>{De(e)&&(We(c)||null===t.get()||Ue(e,t))})),r={scrollPos:Re(window),containerWidth:g.width,containerHeight:g.height,containerTop:g.top,containerLeft:g.left,iframeWidth:p.width,iframeHeight:p.height,fullscreenChangeHandler:n};h&&((e,t,n)=>{const r=t=>n=>{const r=j(n,"style"),s=void 0===r?"no-styles":r.trim();s!==t&&(((e,t,n)=>{((e,t,n)=>{if(!(o(n)||l(n)||u(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")})(e.dom,t,n)})(n,Ie,s),$(n,e.parseStyle(t)))},i=qe(t,"*"),a=(e=>{const t=[];for(let n=0,r=e.length;n<r;++n){if(!s(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);y.apply(t,e[n])}return t})(b(i,(e=>He(e,"*:not(.tox-silver-sink)")))),c=je(n);S(a,r("display:none!important;")),S(i,r(Be+Ve+c)),r((!0===_e?"":Be)+Ve+c)(t)})(e.dom,a,m),p.width=p.height="100%",g.width=g.height="",v(ze.addClass),$e.bind(a),e.on("remove",w),t.set(r),De(e)&&(e=>{const t=e.dom;t.requestFullscreen?t.requestFullscreen():t.msRequestFullscreen?t.msRequestFullscreen():t.webkitRequestFullScreen&&t.webkitRequestFullScreen()})(c),Pe(e,!0)}var x},Ke=(e,t)=>n=>{n.setActive(null!==t.get());const r=e=>n.setActive(e.state);return e.on("FullscreenStateChanged",r),()=>e.off("FullscreenStateChanged",r)};t.add("fullscreen",(t=>{const n=e(null);return t.inline||((e=>{(0,e.options.register)("fullscreen_native",{processor:"boolean",default:!1})})(t),((e,t)=>{e.addCommand("mceFullScreen",(()=>{Ue(e,t)}))})(t,n),((e,t)=>{const n=()=>e.execCommand("mceFullScreen");e.ui.registry.addToggleMenuItem("fullscreen",{text:"Fullscreen",icon:"fullscreen",shortcut:"Meta+Shift+F",onAction:n,onSetup:Ke(e,t)}),e.ui.registry.addToggleButton("fullscreen",{tooltip:"Fullscreen",icon:"fullscreen",onAction:n,onSetup:Ke(e,t)})})(t,n),t.addShortcut("Meta+Shift+F","","mceFullScreen")),(e=>({isFullscreen:()=>null!==e.get()}))(n)}))}();