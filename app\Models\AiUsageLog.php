<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AiUsageLog extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'usage_date',
        'prompts_used',
        'usage_type',
        'context',
    ];

    protected $casts = [
        'usage_date' => 'date',
        'prompts_used' => 'integer',
    ];

    /**
     * Get the user that owns this usage log.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get today's usage for a specific user and type.
     */
    public static function getTodayUsage($userId, $usageType = 'chat')
    {
        return static::where('user_id', $userId)
                    ->where('usage_date', Carbon::today())
                    ->where('usage_type', $usageType)
                    ->first();
    }

    /**
     * Increment usage for today.
     */
    public static function incrementUsage($userId, $usageType = 'chat', $context = null)
    {
        $today = Carbon::today();
        
        $usage = static::firstOrCreate(
            [
                'user_id' => $userId,
                'usage_date' => $today,
                'usage_type' => $usageType,
            ],
            [
                'prompts_used' => 0,
                'context' => $context,
            ]
        );

        $usage->increment('prompts_used');
        
        if ($context) {
            $usage->update(['context' => $context]);
        }

        return $usage;
    }

    /**
     * Get total prompts used today for a user across all types.
     */
    public static function getTotalTodayUsage($userId)
    {
        return static::where('user_id', $userId)
                    ->where('usage_date', Carbon::today())
                    ->sum('prompts_used');
    }

    /**
     * Check if user has exceeded daily limit.
     */
    public static function hasExceededDailyLimit($userId, $dailyLimit)
    {
        if ($dailyLimit === -1) {
            return false; // Unlimited
        }

        $todayUsage = static::getTotalTodayUsage($userId);
        return $todayUsage >= $dailyLimit;
    }

    /**
     * Get remaining prompts for today.
     */
    public static function getRemainingPrompts($userId, $dailyLimit)
    {
        if ($dailyLimit === -1) {
            return -1; // Unlimited
        }

        $todayUsage = static::getTotalTodayUsage($userId);
        return max(0, $dailyLimit - $todayUsage);
    }
}
