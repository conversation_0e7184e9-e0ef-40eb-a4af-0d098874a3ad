<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\MembershipTier;
use App\Models\UserMembership;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Find all users who don't have any active membership
        $usersWithoutMembership = User::whereDoesntHave('memberships', function ($query) {
            $query->where('status', 'active');
        })->get();

        // Get the Starter membership tier
        $starterTier = MembershipTier::where('slug', 'starter')->first();

        if ($starterTier && $usersWithoutMembership->count() > 0) {
            foreach ($usersWithoutMembership as $user) {
                UserMembership::create([
                    'user_id' => $user->id,
                    'membership_tier_id' => $starterTier->id,
                    'started_at' => now(),
                    'status' => 'active',
                    'amount_paid' => 0,
                    'payment_method' => 'auto_assigned',
                ]);
            }

            echo "Assigned Starter membership to " . $usersWithoutMembership->count() . " existing users.\n";
        } else {
            echo "No users found without membership or Starter tier not found.\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove auto-assigned starter memberships
        UserMembership::where('payment_method', 'auto_assigned')
            ->where('amount_paid', 0)
            ->whereHas('membershipTier', function ($query) {
                $query->where('slug', 'starter');
            })
            ->delete();
    }
};
