@extends('store.layout')

@section('content')
<div class="py-8 bg-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumbs -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('store.show', $seller->store_slug) }}" class="text-gray-500 hover:text-indigo-600 text-sm">
                        Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{{ route('store.category', [$seller->store_slug, $product->category]) }}" class="text-gray-500 hover:text-indigo-600 ml-1 text-sm">
                            {{ $product->category }}
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-500 ml-1 text-sm">{{ $product->name }}</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Product -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Product Images -->
            <div>
                @php
                    // Get product images ordered by sort_order
                    $productImages = $product->images()->orderBy('sort_order')->get();

                    // Get the primary image or the first image in the collection
                    $primaryImage = $productImages->where('is_primary', true)->first() ?? $productImages->first();

                    // Fallback to the legacy image field if no images in the collection
                    $mainImagePath = $primaryImage ? asset('storage/' . $primaryImage->path) :
                                    ($product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg'));
                @endphp
                <div class="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden bg-gray-100 mb-4">
                    <img id="main-image" src="{{ $mainImagePath }}" alt="{{ $product->name }}" class="w-full h-full object-center object-cover">
                </div>
                <div class="grid grid-cols-4 gap-4" id="thumbnails-container">
                    @if($productImages->count() > 0)
                        @foreach($productImages as $index => $image)
                            <div class="aspect-w-1 aspect-h-1 rounded-md overflow-hidden bg-gray-100 cursor-pointer {{ $image->is_primary || ($index == 0 && !$productImages->where('is_primary', true)->count()) ? 'border-2 border-indigo-500' : '' }} thumbnail-container"
                                data-index="{{ $index }}">
                                <img src="{{ asset('storage/' . $image->path) }}" alt="{{ $product->name }}"
                                    class="w-full h-full object-center object-cover thumbnail"
                                    data-src="{{ asset('storage/' . $image->path) }}">
                            </div>
                        @endforeach

                        @for($i = $productImages->count(); $i < 4; $i++)
                            <div class="aspect-w-1 aspect-h-1 rounded-md overflow-hidden bg-gray-100 cursor-pointer">
                                <div class="w-full h-full flex items-center justify-center text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                        @endfor
                    @else
                        <div class="aspect-w-1 aspect-h-1 rounded-md overflow-hidden bg-gray-100 cursor-pointer border-2 border-indigo-500">
                            <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                alt="{{ $product->name }}" class="w-full h-full object-center object-cover thumbnail"
                                data-src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}">
                        </div>
                        @for($i = 1; $i < 4; $i++)
                            <div class="aspect-w-1 aspect-h-1 rounded-md overflow-hidden bg-gray-100 cursor-pointer">
                                <div class="w-full h-full flex items-center justify-center text-gray-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                            </div>
                        @endfor
                    @endif
                </div>
            </div>

            <!-- Product Info -->
            <div>
                <h1 class="text-2xl font-bold text-gray-900 sm:text-3xl">{{ $product->name }}</h1>

                <div class="mt-2 flex items-center">
                    <div class="flex items-center">
                        @for($i = 1; $i <= 5; $i++)
                            @if($i <= ($product->average_rating ?? 5))
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            @else
                            <svg class="h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            @endif
                        @endfor
                    </div>
                    <span class="text-sm text-gray-500 ml-2">{{ $product->reviews_count ?? 0 }} reviews</span>
                </div>

                <div class="mt-4">
                    @if($product->discount_price)
                    <div class="flex items-center">
                        <p class="text-3xl font-bold text-gray-900">Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                        <p class="ml-3 text-lg text-gray-500 line-through">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                        <span class="ml-3 bg-red-100 text-red-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                            {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% OFF
                        </span>
                    </div>
                    @else
                    <p class="text-3xl font-bold text-gray-900">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                    @endif
                </div>

                <div class="mt-6 border-t border-b py-6">
                    <div class="prose prose-sm max-w-none text-gray-500">
                        <p>{{ $product->description ?? 'No description available for this product.' }}</p>
                    </div>
                </div>

                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-900">Highlights</h3>
                    <div class="mt-4">
                        <ul class="list-disc space-y-2 pl-4 text-sm">
                            <li class="text-gray-500">Instant digital download</li>
                            <li class="text-gray-500">Compatible with all major software</li>
                            <li class="text-gray-500">Lifetime updates</li>
                            <li class="text-gray-500">Premium support</li>
                        </ul>
                    </div>
                </div>

                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-900">What's Included</h3>
                    <div class="mt-4 space-y-2">
                        @php
                            $files = json_decode($product->files, true) ?: [];
                        @endphp
                        @forelse($files as $file)
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="ml-2 text-sm text-gray-500">{{ $file['name'] }} ({{ number_format($file['size'] / 1024 / 1024, 2) }} MB)</span>
                            </div>
                        @empty
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="ml-2 text-sm text-gray-500">Main product file</span>
                            </div>
                        @endforelse
                    </div>
                </div>

                <div class="mt-8">
                    <form action="{{ route('cart.add') }}" method="POST">
                        @csrf
                        <input type="hidden" name="product_id" value="{{ $product->id }}">
                        <input type="hidden" name="quantity" value="1">
                        <button type="submit" class="w-full bg-indigo-600 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Add to Cart
                        </button>
                    </form>
                    <div class="mt-4 flex">
                        <form action="{{ route('cart.add') }}" method="POST" class="w-full">
                            @csrf
                            <input type="hidden" name="product_id" value="{{ $product->id }}">
                            <input type="hidden" name="quantity" value="1">
                            <button type="submit" formaction="{{ route('cart.add') }}?redirect=checkout" class="w-full bg-gray-100 border border-transparent rounded-md py-3 px-8 flex items-center justify-center text-base font-medium text-gray-900 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                Buy Now
                            </button>
                        </form>
                    </div>
                </div>

                <div class="mt-6">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span class="ml-2 text-sm text-gray-500">Secure payment</span>
                    </div>
                    <div class="flex items-center mt-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span class="ml-2 text-sm text-gray-500">Instant download after purchase</span>
                    </div>
                    <div class="flex items-center mt-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span class="ml-2 text-sm text-gray-500">30-day money-back guarantee</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Details Tabs -->
        <div class="mt-16" x-data="{ activeTab: 'description' }">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button @click="activeTab = 'description'" :class="{ 'border-indigo-600 text-indigo-600': activeTab === 'description', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'description' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Description
                    </button>
                    <button @click="activeTab = 'details'" :class="{ 'border-indigo-600 text-indigo-600': activeTab === 'details', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'details' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Details
                    </button>
                    <button @click="activeTab = 'reviews'" :class="{ 'border-indigo-600 text-indigo-600': activeTab === 'reviews', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'reviews' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Reviews
                    </button>
                </nav>
            </div>

            <!-- Description Tab -->
            <div x-show="activeTab === 'description'" class="py-6 prose prose-sm max-w-none text-gray-500">
                <p>{{ $product->description ?? 'No detailed description available for this product.' }}</p>
                <p>Explore the features of this product in detail. It’s designed to help you achieve your goals efficiently, whether you’re a professional or a hobbyist. This product includes all the tools you need to get started immediately.</p>
            </div>

            <!-- Details Tab -->
            <div x-show="activeTab === 'details'" class="py-6 prose prose-sm max-w-none text-gray-500">
                <h4 class="text-gray-900 font-medium">Product Specifications</h4>
                <ul class="list-disc pl-4 space-y-1">
                    <li><strong>Category:</strong> {{ $product->category }}</li>
                    <li><strong>Format:</strong> Digital Download</li>
                    <li><strong>Size:</strong> {{ collect(json_decode($product->files, true))->sum('size') / 1024 / 1024 }} MB</li>
                    <li><strong>Last Updated:</strong> {{ $product->updated_at->format('F d, Y') }}</li>
                    <li><strong>Compatibility:</strong> Works with all major software platforms</li>
                </ul>
            </div>

            <!-- Reviews Tab -->
            <div x-show="activeTab === 'reviews'" class="py-6">
                @if(($product->reviews_count ?? 0) > 0)
                    <div class="space-y-6">
                        <!-- Placeholder for reviews -->
                        <div class="border-b pb-4">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endfor
                                </div>
                                <span class="ml-2 text-sm text-gray-500">John Doe - {{ now()->subDays(5)->format('F d, Y') }}</span>
                            </div>
                            <p class="text-gray-600">This product is fantastic! It has everything I needed and more. The quality is top-notch, and the support team was very helpful.</p>
                        </div>
                        <div class="border-b pb-4">
                            <div class="flex items-center mb-2">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 4; $i++)
                                        <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endfor
                                    <svg class="h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                </div>
                                <span class="ml-2 text-sm text-gray-500">Jane Smith - {{ now()->subDays(10)->format('F d, Y') }}</span>
                            </div>
                            <p class="text-gray-600">Great product, but I wish there were more customization options. Overall, a solid purchase.</p>
                        </div>
                    </div>
                @else
                    <p class="text-gray-500">No reviews yet. Be the first to share your experience!</p>
                @endif
            </div>
        </div>

        <!-- Related Products -->
        @if($relatedProducts->count() > 0)
        <div class="mt-16">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Related Products</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedProducts as $relatedProduct)
                <div class="group relative">
                    <div class="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden bg-gray-100">
                        <img src="{{ $relatedProduct->image ? asset('storage/' . $relatedProduct->image) : asset('images/placeholder.jpg') }}" alt="{{ $relatedProduct->name }}" class="w-full h-full object-center object-cover group-hover:opacity-75">
                        @if($relatedProduct->discount_price)
                        <div class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                            SALE
                        </div>
                        @endif
                    </div>
                    <div class="mt-4">
                        <h3 class="text-sm font-medium text-gray-900">
                            <a href="{{ route('store.product', [$seller->store_slug, $relatedProduct->slug]) }}">
                                <span aria-hidden="true" class="absolute inset-0"></span>
                                {{ $relatedProduct->name }}
                            </a>
                        </h3>
                        <p class="mt-1 text-sm text-gray-500">{{ $relatedProduct->category }}</p>
                        <div class="mt-2 flex items-center justify-between">
                            <div>
                                @if($relatedProduct->discount_price)
                                <p class="text-sm font-medium text-gray-900">Rp {{ number_format($relatedProduct->discount_price, 0, ',', '.') }}</p>
                                <p class="text-sm text-gray-500 line-through">Rp {{ number_format($relatedProduct->price, 0, ',', '.') }}</p>
                                @else
                                <p class="text-sm font-medium text-gray-900">Rp {{ number_format($relatedProduct->price, 0, ',', '.') }}</p>
                                @endif
                            </div>
                            <div class="flex items-center">
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= ($relatedProduct->average_rating ?? 5))
                                        <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        @else
                                        <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        @endif
                                    @endfor
                                </div>
                                <span class="text-xs text-gray-500 ml-1">({{ $relatedProduct->reviews_count ?? 0 }})</span>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>

<script src="{{ asset(js_path() . '/product-detail.js') }}"></script>
@endsection
