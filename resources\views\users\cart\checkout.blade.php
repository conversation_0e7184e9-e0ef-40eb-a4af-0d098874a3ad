@extends('layouts.user-dashboard')

@section('content')
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h1 class="text-2xl font-bold text-gray-900">Checkout</h1>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Complete your purchase</p>
    </div>

    <div class="border-t border-gray-200">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Checkout Form -->
                <div>
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Shipping Information</h2>

                    <form action="#" method="POST" class="space-y-4">
                        @csrf

                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                            <input type="text" name="name" id="name" value="{{ Auth::user()->name }}" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                            <input type="email" name="email" id="email" value="{{ Auth::user()->email }}" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                            <input type="text" name="phone" id="phone" value="{{ Auth::user()->phone ?? '' }}" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <div>
                            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                            <textarea name="address" id="address" rows="3" required
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ Auth::user()->address ?? '' }}</textarea>
                        </div>

                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700">City</label>
                            <input type="text" name="city" id="city" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <div>
                            <label for="postal_code" class="block text-sm font-medium text-gray-700">Postal Code</label>
                            <input type="text" name="postal_code" id="postal_code" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <h2 class="text-lg font-medium text-gray-900 mt-8 mb-4">Payment Information</h2>

                        <div>
                            <label for="card_number" class="block text-sm font-medium text-gray-700">Card Number</label>
                            <input type="text" name="card_number" id="card_number" placeholder="1234 5678 9012 3456" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="expiry_date" class="block text-sm font-medium text-gray-700">Expiry Date</label>
                                <input type="text" name="expiry_date" id="expiry_date" placeholder="MM/YY" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            </div>

                            <div>
                                <label for="cvv" class="block text-sm font-medium text-gray-700">CVV</label>
                                <input type="text" name="cvv" id="cvv" placeholder="123" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            </div>
                        </div>

                        <div class="mt-8">
                            <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Complete Order
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Order Summary -->
                <div>
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Order Summary</h2>

                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="flow-root">
                            <ul role="list" class="-my-6 divide-y divide-gray-200">
                                @foreach($cart->items as $item)
                                    <li class="py-6 flex">
                                        <div class="flex-shrink-0 w-16 h-16 border border-gray-200 rounded-md overflow-hidden">
                                            @if($item->isCourseItem())
                                                <img src="{{ $item->course->thumbnail ? asset('storage/' . $item->course->thumbnail) : asset('images/placeholder.jpg') }}"
                                                     alt="{{ $item->course->title }}"
                                                     class="w-full h-full object-center object-cover">
                                            @else
                                                <img src="{{ $item->product->image ? asset('storage/' . $item->product->image) : asset('images/placeholder.jpg') }}"
                                                     alt="{{ $item->product->name }}"
                                                     class="w-full h-full object-center object-cover">
                                            @endif
                                        </div>

                                        <div class="ml-4 flex-1 flex flex-col">
                                            <div>
                                                <div class="flex justify-between text-base font-medium text-gray-900">
                                                    <h3>{{ $item->getItemName() }}</h3>
                                                    <p class="ml-4">Rp {{ number_format($item->price, 0, ',', '.') }}</p>
                                                </div>
                                                <p class="mt-1 text-sm text-gray-500">{{ $item->product->category }}</p>
                                            </div>
                                            <div class="flex-1 flex items-end justify-between text-sm">
                                                <p class="text-gray-500">Qty {{ $item->quantity }}</p>
                                                <p class="font-medium text-gray-900">Rp {{ number_format($item->subtotal, 0, ',', '.') }}</p>
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>

                        <div class="border-t border-gray-200 pt-6">
                            <div class="flex justify-between text-base font-medium text-gray-900 mb-2">
                                <p>Subtotal</p>
                                <p>Rp {{ number_format($cart->total, 0, ',', '.') }}</p>
                            </div>
                            <div class="flex justify-between text-base font-medium text-gray-900 mb-2">
                                <p>Shipping</p>
                                <p>Rp 0</p>
                            </div>
                            <div class="flex justify-between text-base font-medium text-gray-900 mb-4">
                                <p>Tax</p>
                                <p>Rp 0</p>
                            </div>
                            <div class="flex justify-between text-lg font-bold text-gray-900">
                                <p>Total</p>
                                <p>Rp {{ number_format($cart->total, 0, ',', '.') }}</p>
                            </div>
                        </div>

                        <div class="mt-6">
                            <a href="{{ route('cart.index') }}" class="text-sm text-indigo-600 hover:text-indigo-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                                </svg>
                                Back to Cart
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
