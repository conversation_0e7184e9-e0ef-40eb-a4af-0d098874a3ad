<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DocumentationSection;
use App\Models\DocumentationTopic;
use App\Models\VideoTutorial;
use DOMDocument;
use Illuminate\Support\Str;

class DocumentationController extends Controller
{
    /**
     * Display the documentation index page.
     */
    public function index(Request $request)
    {
        $search = $request->query('search');
        $sectionId = $request->query('section');

        if ($search) {
            $sections = DocumentationSection::with(['topics' => function ($query) use ($search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
            }])->get();

            $sections = $sections->filter(function ($section) {
                return $section->topics->isNotEmpty();
            });
        } elseif ($sectionId) {
            // If a specific section is requested, load all topics for that section
            $sections = DocumentationSection::with('topics')
                ->where('id', $sectionId)
                ->get();
        } else {
            // Fetch all sections first
            $sections = DocumentationSection::all();

            // Then load topics for each section separately with a limit
            foreach ($sections as $section) {
                $section->load(['topics' => function ($query) {
                    $query->take(4); // Limit to 4 topics per section
                }]);
            }
        }

        // Ensure at least one topic exists per section for display
        $sections = $sections->filter(function ($section) {
            return $section->topics->isNotEmpty();
        });

        $videoTutorials = VideoTutorial::take(3)->get();

        return view('seller.documentation.index', compact('sections', 'videoTutorials'));
    }

    /**
     * Display a specific documentation topic.
     */
    public function show($slug)
    {
        $topic = DocumentationTopic::with('section', 'relatedTopics')->where('slug', $slug)->firstOrFail();

        // Extract headings for table of contents
        $toc = $this->generateTableOfContents($topic->content);

        return view('seller.documentation.show', compact('topic', 'toc'));
    }

    /**
     * Record feedback on a topic.
     */
    public function feedback(Request $request, $slug)
    {
        $topic = DocumentationTopic::where('slug', $slug)->firstOrFail();
        $type = $request->input('type');

        if ($type === 'yes') {
            $topic->increment('helpful_yes');
        } elseif ($type === 'no') {
            $topic->increment('helpful_no');
        }

        return redirect()->route('seller.documentation.show', $slug)
            ->with('success', 'Thank you for your feedback!');
    }

    /**
     * Generate table of contents from content.
     */
    protected function generateTableOfContents($content)
    {
        $toc = [];
        $dom = new DOMDocument();
        @$dom->loadHTML('<?xml encoding="utf-8" ?>' . $content); // Suppress warnings for malformed HTML
        $headings = $dom->getElementsByTagName('h2');

        foreach ($headings as $heading) {
            $text = $heading->textContent;
            $slug = Str::slug($text);
            $toc[] = [
                'text' => $text,
                'slug' => $slug,
            ];
        }

        return $toc;
    }
}