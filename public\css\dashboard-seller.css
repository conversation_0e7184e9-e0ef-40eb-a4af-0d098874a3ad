/* Shared styles for seller dashboard pages */

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Cards */
.stat-card {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease-out forwards;
    animation-delay: calc(var(--animation-order, 0) * 0.1s);
}

.stat-card:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
}

.stat-card .value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
}

.stat-card:hover .value {
    transform: scale(1.05);
}

.stat-card p {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Buttons and Links */
.time-range-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.time-range-btn.active {
    background-color: #e0e7ff;
    color: #4f46e5;
}

.time-range-btn:hover {
    background-color: #f3f4f6;
}

/* Tables */
.table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: fadeIn 0.6s ease-out forwards;
    animation-delay: 0.2s;
}

.table-container:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-container table {
    width: 100%;
    border-collapse: collapse;
}

.table-container th,
.table-container td {
    padding: 1rem;
    text-align: left;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.table-container th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    font-size: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.table-container td {
    color: #374151;
}

.table-container tr {
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.table-container tbody tr:hover {
    background-color: #f9fafb;
}

/* Responsive table adjustments */
@media (max-width: 767px) {
    .table-container table {
        display: table;
        width: 100%;
    }

    .table-container th,
    .table-container td {
        padding: 0.75rem 0.5rem;
    }

    .table-container th {
        position: sticky;
        top: 0;
        background-color: #f9fafb;
        z-index: 10;
    }
}

/* Pagination Styles */
.pagination-container {
    width: 100%;
}

.pagination-container nav {
    width: 100%;
}

.pagination-container p {
    margin-bottom: 0.5rem;
}

@media (min-width: 640px) {
    .pagination-container p {
        margin-bottom: 0;
    }
}

/* Status Labels */
.status-label {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    min-width: 80px;
    text-align: center;
}

.status-label:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-label.completed,
.status-label.success,
.status-label.paid {
    border: 1px solid #d4f4e2;
    background-color: #e6f9ed;
    color: #2f855a;
}

.status-label.completed:hover,
.status-label.success:hover,
.status-label.paid:hover {
    background-color: #d1f7e4;
}

.status-label.pending {
    border: 1px solid #feebc8;
    background-color: #fffaf0;
    color: #c05621;
}

.status-label.pending:hover {
    background-color: #fef0d9;
}

.status-label.cancelled,
.status-label.cancel,
.status-label.failed {
    border: 1px solid #fee2e2;
    background-color: #fef2f2;
    color: #dc2626;
}

.status-label.cancelled:hover,
.status-label.cancel:hover,
.status-label.failed:hover {
    background-color: #fee2e2;
}

.status-label.expired {
    border: 1px solid #e5e7eb;
    background-color: #f3f4f6;
    color: #6b7280;
}

.status-label.expired:hover {
    background-color: #e5e7eb;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    /* Cards */
    .stat-card {
        padding: 1.25rem;
        margin-bottom: 0.5rem;
    }

    .stat-card .value {
        font-size: 1.25rem;
    }

    .stat-card h3,
    .stat-card p {
        font-size: 0.75rem;
    }

    /* Time Range Buttons */
    .time-range-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }

    /* Tables */
    .table-container th,
    .table-container td {
        padding: 0.75rem;
        font-size: 0.75rem;
    }

    .table-container th {
        font-size: 0.675rem;
    }

    /* Stack cards on mobile */
    .grid.md\:grid-cols-3,
    .grid.md\:grid-cols-2 {
        grid-template-columns: 1fr;
    }

    /* Forms and Selects */
    select,
    input {
        font-size: 0.75rem;
        padding: 0.5rem;
    }

    /* Status labels */
    .status-label {
        min-width: 70px;
        padding: 0.2rem 0.5rem;
    }

    /* Export dropdown */
    .export-dropdown {
        position: static;
        width: 100%;
        margin-top: 0.5rem;
    }

    /* Search input */
    input[type="search"] {
        width: 100%;
    }
}

@media (max-width: 640px) {
    /* Further adjustments for very small screens */
    .table-container th,
    .table-container td {
        padding: 0.5rem;
    }

    .stat-card .value {
        font-size: 1.125rem;
    }

    /* Improve table readability on very small screens */
    .table-container {
        border-radius: 0.5rem;
        overflow: hidden;
    }

    /* Improve header spacing */
    .space-y-6 > div:first-child {
        margin-bottom: 1rem;
    }

    /* Better spacing for order history section */
    .rounded-lg.border.bg-white.shadow-sm {
        border-radius: 0.5rem;
        overflow: hidden;
    }

    /* Improve pagination on mobile */
    nav[role="navigation"] {
        padding: 0.5rem;
        width: 100%;
    }

    nav[role="navigation"] span,
    nav[role="navigation"] a {
        margin: 0 0.25rem;
        padding: 0.25rem 0.5rem;
    }

    /* Fix pagination layout */
    .pagination-container {
        width: 100%;
    }
}