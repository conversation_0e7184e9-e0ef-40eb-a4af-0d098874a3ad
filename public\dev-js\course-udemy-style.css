/**
 * Udemy-Style Course Management Interface
 * Clean, professional design inspired by Udemy's course creation interface
 * Non-floating elements with integrated contextual placement
 */

/* Main Container */
.udemy-course-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    background: #f7f9fa;
    min-height: 100vh;
}

/* Header */
.course-header {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
}

.course-header-content {
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.course-header-info {
    flex: 1;
}

.course-title {
    font-size: 28px;
    font-weight: 700;
    color: #1c1d1f;
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.course-subtitle {
    font-size: 16px;
    color: #6a6f73;
    margin: 0;
}

.course-header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Course Overview Card */
.course-overview-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
}

.course-overview-content {
    padding: 24px;
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.course-thumbnail {
    flex-shrink: 0;
}

.thumbnail-image {
    width: 120px;
    height: 68px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid #e4e8eb;
}

.course-details {
    flex: 1;
}

.course-overview-title {
    font-size: 20px;
    font-weight: 600;
    color: #1c1d1f;
    margin: 0 0 8px 0;
}

.course-overview-description {
    font-size: 14px;
    color: #6a6f73;
    margin: 0 0 16px 0;
    line-height: 1.4;
}

.course-meta-stats {
    display: flex;
    align-items: center;
    gap: 12px;
}

.meta-badge {
    background: #f7f9fa;
    color: #6a6f73;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #e4e8eb;
}

.meta-badge.status-active {
    background: #ecfdf5;
    color: #065f46;
    border-color: #a7f3d0;
}

.meta-badge.status-draft {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
}

.meta-badge.status-inactive {
    background: #fef2f2;
    color: #991b1b;
    border-color: #fecaca;
}

/* Curriculum Section */
.curriculum-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.curriculum-header {
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid #e4e8eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.curriculum-title {
    font-size: 20px;
    font-weight: 600;
    color: #1c1d1f;
    margin: 0;
}

.curriculum-stats {
    font-size: 14px;
    color: #6a6f73;
    font-weight: 500;
}

.curriculum-content {
    padding: 0;
}

/* Udemy-Style Sections */
.udemy-section {
    border-bottom: 1px solid #e4e8eb;
}

.udemy-section:last-child {
    border-bottom: none;
}

.section-header {
    background: #f7f9fa;
    padding: 16px 24px;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-bottom: 1px solid #e4e8eb;
}

.section-info {
    flex: 1;
}

.section-number {
    font-size: 12px;
    font-weight: 600;
    color: #6a6f73;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1c1d1f;
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.section-description {
    font-size: 14px;
    color: #6a6f73;
    margin: 0;
    line-height: 1.4;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
}

/* Curriculum Items */
.curriculum-items {
    background: #fff;
}

.curriculum-item {
    padding: 12px 24px;
    border-bottom: 1px solid #f7f9fa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.15s ease;
}

.curriculum-item:hover {
    background: #f7f9fa;
}

.curriculum-item:last-child {
    border-bottom: none;
}

.item-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.item-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.item-type-lecture {
    background: #e0f2fe;
    color: #0277bd;
}

.item-type-video {
    background: #fce4ec;
    color: #c2185b;
}

.item-type-pdf {
    background: #fff3e0;
    color: #f57c00;
}

.item-type-document {
    background: #e8f5e8;
    color: #388e3c;
}

.item-details {
    flex: 1;
}

.item-title {
    font-size: 14px;
    font-weight: 500;
    color: #1c1d1f;
    margin: 0 0 2px 0;
    line-height: 1.3;
}

.item-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #6a6f73;
}

.item-type {
    font-weight: 500;
}

.item-duration {
    color: #6a6f73;
}

.item-preview {
    color: #16a085;
    font-weight: 500;
}

.item-actions {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Add Buttons - Udemy Style */
.add-section-container {
    padding: 24px;
    text-align: center;
}

.add-section-btn {
    background: #fff;
    border: 2px dashed #d1d7dc;
    border-radius: 8px;
    padding: 16px 24px;
    color: #6a6f73;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-width: 200px;
    justify-content: center;
}

.add-section-btn:hover {
    background: #f7f9fa;
    border-color: #a435f0;
    color: #a435f0;
}

.add-curriculum-item-container {
    padding: 16px 24px;
    background: #f7f9fa;
    border-top: 1px solid #e4e8eb;
}

.add-curriculum-item-btn {
    background: #fff;
    border: 1px dashed #d1d7dc;
    border-radius: 4px;
    padding: 12px 16px;
    color: #6a6f73;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    width: 100%;
    justify-content: center;
}

.add-curriculum-item-btn:hover {
    background: #f7f9fa;
    border-color: #a435f0;
    color: #a435f0;
}

/* Empty State */
.empty-state {
    padding: 48px 24px;
    text-align: center;
}

.empty-state-content {
    max-width: 400px;
    margin: 0 auto;
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    color: #d1d7dc;
    margin: 0 auto 16px auto;
}

.empty-state-title {
    font-size: 18px;
    font-weight: 600;
    color: #1c1d1f;
    margin: 0 0 8px 0;
}

.empty-state-description {
    font-size: 14px;
    color: #6a6f73;
    margin: 0;
    line-height: 1.4;
}

/* Action Buttons */
.action-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.15s ease;
    color: #6a6f73;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: #f7f9fa;
    color: #1c1d1f;
}

.edit-btn:hover {
    background: #e3f2fd;
    color: #1976d2;
}

.delete-btn:hover {
    background: #ffebee;
    color: #d32f2f;
}

/* Inline Forms - Udemy Style */
.inline-form-container {
    background: #fff;
    border: 1px solid #e4e8eb;
    border-radius: 8px;
    padding: 24px;
    margin: 16px 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #1c1d1f;
    margin-bottom: 8px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d7dc;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #fff;
}

.form-input:focus {
    outline: none;
    border-color: #a435f0;
    box-shadow: 0 0 0 2px rgba(164, 53, 240, 0.1);
}

.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d7dc;
    border-radius: 4px;
    font-size: 14px;
    resize: vertical;
    min-height: 100px;
    transition: all 0.2s ease;
    background: #fff;
    font-family: inherit;
}

.form-textarea:focus {
    outline: none;
    border-color: #a435f0;
    box-shadow: 0 0 0 2px rgba(164, 53, 240, 0.1);
}

.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d7dc;
    border-radius: 4px;
    font-size: 14px;
    background: #fff;
    transition: all 0.2s ease;
    cursor: pointer;
}

.form-select:focus {
    outline: none;
    border-color: #a435f0;
    box-shadow: 0 0 0 2px rgba(164, 53, 240, 0.1);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid #e4e8eb;
}

/* Buttons - Udemy Style */
.btn {
    padding: 12px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    line-height: 1;
}

.btn-primary {
    background: #a435f0;
    color: #fff;
}

.btn-primary:hover {
    background: #8710d8;
}

.btn-secondary {
    background: #fff;
    color: #1c1d1f;
    border: 1px solid #d1d7dc;
}

.btn-secondary:hover {
    background: #f7f9fa;
    border-color: #a435f0;
    color: #a435f0;
}

.btn-outline {
    background: transparent;
    color: #a435f0;
    border: 1px solid #a435f0;
}

.btn-outline:hover {
    background: #a435f0;
    color: #fff;
}

/* Notifications - Udemy Style */
.notification {
    position: fixed;
    top: 24px;
    right: 24px;
    padding: 16px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
    max-width: 400px;
}

.notification.success {
    background: #ecfdf5;
    color: #065f46;
    border: 1px solid #10b981;
}

.notification.error {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #ef4444;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .udemy-course-container {
        padding: 16px;
    }

    .course-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .course-header-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .course-overview-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .section-actions {
        margin-left: 0;
    }

    .curriculum-item {
        padding: 12px 16px;
    }

    .inline-form-container {
        margin: 16px 16px;
        padding: 20px;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .notification {
        right: 16px;
        left: 16px;
        max-width: none;
    }
}
