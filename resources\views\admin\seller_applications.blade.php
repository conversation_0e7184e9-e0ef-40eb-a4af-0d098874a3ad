@extends('layouts.main')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-white">
        <div class="container max-w-4xl mx-auto pt-4 pb-8 px-4">
            <!-- Header -->
            <div class="mb-5 text-center">
                <h1 class="text-3xl font-bold tracking-tight md:text-4xl text-gray-900">Manage Seller Applications</h1>
                <p class="mt-1 text-gray-600">Review and approve/reject seller applications</p>
            </div>

            <!-- Success Message -->
            @if (session('success'))
                <div class="mb-4 px-6 py-3 bg-green-50 border-t border-b border-green-200">
                    <p class="text-green-600 text-sm">{{ session('success') }}</p>
                </div>
            @endif

            <!-- Applications Table -->
            @if ($applications->isEmpty())
                <div class="text-center text-gray-600">
                    <p>No seller applications found.</p>
                </div>
            @else
                <div class="bg-white border-0 shadow-lg rounded-xl mx-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Store Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach ($applications as $application)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $application->user->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $application->email }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $application->store_name }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $application->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : ($application->status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') }}">
                                            {{ ucfirst($application->status) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <!-- Details Dropdown -->
                                        <details class="mb-2">
                                            <summary class="text-indigo-600 hover:underline cursor-pointer">View Details</summary>
                                            <div class="mt-2 p-4 bg-gray-50 rounded-md">
                                                <p><strong>Full Name:</strong> {{ $application->full_name }}</p>
                                                <p><strong>ID Type:</strong> {{ ucfirst($application->id_type) }}</p>
                                                <p><strong>ID Number:</strong> {{ $application->id_number }}</p>
                                                <p><strong>Bank Name:</strong> {{ $application->bank_name }}</p>
                                                <p><strong>Account Number:</strong> {{ $application->account_number }}</p>
                                                <p><strong>Account Holder:</strong> {{ $application->account_holder_name }}</p>
                                                <p><strong>Store Description:</strong> {{ $application->store_description }}</p>
                                                @if ($application->id_document)
                                                    <p><strong>ID Document:</strong> <a href="{{ route('seller.file', ['applicationId' => $application->id, 'type' => 'id_document']) }}" class="text-indigo-600 hover:underline">View File</a></p>
                                                @else
                                                    <p><strong>ID Document:</strong> Not uploaded</p>
                                                @endif
                                                @if ($application->store_logo)
                                                    <p><strong>Store Logo:</strong> <a href="{{ route('seller.file', ['applicationId' => $application->id, 'type' => 'store_logo']) }}" class="text-indigo-600 hover:underline">View File</a></p>
                                                @else
                                                    <p><strong>Store Logo:</strong> Not uploaded</p>
                                                @endif
                                            </div>
                                        </details>

                                        <!-- Status Update Form -->
                                        @if ($application->status === 'pending')
                                            <form action="{{ route('admin.seller_applications.update_status', $application) }}" method="POST" class="inline-flex space-x-2">
                                                @csrf
                                                <button type="submit" name="status" value="approved" class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700">Approve</button>
                                                <button type="submit" name="status" value="rejected" class="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700">Reject</button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div>
@endsection
