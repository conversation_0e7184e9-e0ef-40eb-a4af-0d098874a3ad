# JavaScript Obfuscation System

This project uses a JavaScript obfuscation system to protect the source code in production environments while maintaining readability during development.

## Directory Structure

- `public/dev-js`: Contains the original, readable JavaScript files for development
- `public/js`: Contains the obfuscated JavaScript files for production

## How It Works

1. During development (when `APP_ENV=local`), the application uses the JavaScript files from the `public/dev-js` directory.
2. In production (when `APP_ENV=production`), the application uses the obfuscated JavaScript files from the `public/js` directory.
3. JavaScript files need to be manually obfuscated before deployment to production.

## Usage

### Development

1. Make all your JavaScript changes in the `public/dev-js` directory.
2. The application will automatically use these files when `APP_ENV=local`.

### Manual Obfuscation

To obfuscate the JavaScript files, you can use one of the following methods:

#### Using npm script (without commit)

```bash
npm run obfuscate
```

This will obfuscate all JavaScript files from `public/dev-js` to `public/js` without committing the changes.

#### Using npm script (with auto-commit)

```bash
npm run obfuscate-commit
```

This will:
1. Obfuscate all JavaScript files from `public/dev-js` to `public/js`
2. Add the obfuscated files to the Git index
3. Commit the changes with the message "Auto-obfuscate JavaScript files" (if there are any changes)

## Configuration

The obfuscation settings are defined in the npm scripts in `package.json`. If you need to change the obfuscation settings, make sure to update this file.

## Troubleshooting

### The obfuscation script is not working

Make sure the JavaScript obfuscator is installed:

```bash
# Check if it's installed
npm list -g javascript-obfuscator
npm list javascript-obfuscator

# Install it globally
npm install -g javascript-obfuscator

# Or install it locally
npm install --save-dev javascript-obfuscator
```
