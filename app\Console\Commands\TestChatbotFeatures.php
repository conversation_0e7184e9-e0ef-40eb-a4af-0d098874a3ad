<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Product;
use App\Models\ProductChatbotData;
use App\Models\MembershipTier;
use App\Models\UserMembership;

class TestChatbotFeatures extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:chatbot-features';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test chatbot features implementation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Chatbot Features Implementation...');
        $this->newLine();

        // Test 1: Check membership tiers
        $this->info('1. Testing Membership Tiers...');
        $tiers = MembershipTier::all();
        foreach ($tiers as $tier) {
            $this->line("   - {$tier->name}: {$tier->chatbot_products_limit} chatbot limit");
        }
        $this->newLine();

        // Test 2: Find a seller with products
        $this->info('2. Finding seller with products...');
        $seller = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function($query) {
                $query->where('status', 'approved');
            })
            ->whereHas('products', function($query) {
                $query->where('status', 'active');
            })
            ->with(['sellerApplication', 'currentMembership.membershipTier'])
            ->first();

        if (!$seller) {
            $this->error('No seller with products found!');
            return Command::FAILURE;
        }

        $this->line("   Found seller: {$seller->name}");
        $this->line("   Store: {$seller->sellerApplication->store_name}");
        $currentTier = $seller->getCurrentMembershipTier();
        $this->line("   Membership: " . ($currentTier ? $currentTier->name : 'None'));
        $this->newLine();

        // Test 3: Check seller's products
        $this->info('3. Testing seller products...');
        $products = $seller->products()->where('status', 'active')->with('chatbotData')->get();
        $this->line("   Total products: {$products->count()}");
        
        foreach ($products as $product) {
            $hasChatbot = $product->hasActiveChatbot();
            $canActivate = $product->canActivateChatbot();
            $this->line("   - {$product->name}:");
            $this->line("     * Has active chatbot: " . ($hasChatbot ? 'Yes' : 'No'));
            $this->line("     * Can activate chatbot: " . ($canActivate ? 'Yes' : 'No'));
            if ($product->chatbotData) {
                $this->line("     * Chatbot completion: {$product->chatbotData->completion_percentage}%");
                $this->line("     * Is active: " . ($product->chatbotData->is_active ? 'Yes' : 'No'));
            }
        }
        $this->newLine();

        // Test 4: Test membership limits
        $this->info('4. Testing membership limits...');
        $canActivate = $seller->canActivateChatbot();
        $activeChatbots = $seller->products()
            ->whereHas('chatbotData', function ($query) {
                $query->where('is_active', true);
            })
            ->count();
        
        $this->line("   Can activate chatbot: " . ($canActivate ? 'Yes' : 'No'));
        $this->line("   Active chatbots: {$activeChatbots}");
        if ($currentTier) {
            $limit = $currentTier->chatbot_products_limit;
            $this->line("   Chatbot limit: " . ($limit === -1 ? 'Unlimited' : $limit));
        }
        $this->newLine();

        // Test 5: Create a test chatbot if possible
        $this->info('5. Testing chatbot creation...');
        $testProduct = $products->first();
        if ($testProduct && $seller->canActivateChatbot()) {
            $chatbotData = $testProduct->chatbotData ?: new ProductChatbotData();
            
            $chatbotData->fill([
                'product_id' => $testProduct->id,
                'main_function' => 'Test product untuk demonstrasi fitur chatbot AI.',
                'key_features' => 'Fitur utama, mudah digunakan, support lengkap.',
                'target_users' => 'Semua pengguna yang membutuhkan solusi digital.',
                'requirements' => 'Tidak ada persyaratan khusus.',
                'usage_instructions' => 'Download dan gunakan sesuai petunjuk.',
                'is_active' => true,
                'language' => 'id',
            ]);
            
            $chatbotData->save();
            
            if (!$testProduct->chatbot_data_id) {
                $testProduct->update(['chatbot_data_id' => $chatbotData->id]);
            }
            
            $this->line("   ✓ Test chatbot created for: {$testProduct->name}");
            $this->line("   ✓ Completion: {$chatbotData->completion_percentage}%");
            $this->line("   ✓ Has required data: " . ($chatbotData->hasRequiredData() ? 'Yes' : 'No'));
        } else {
            $this->line("   ⚠ Cannot create test chatbot (no products or limit reached)");
        }
        $this->newLine();

        // Test 6: Test store view URLs
        $this->info('6. Testing store URLs...');
        $storeSlug = $seller->sellerApplication->store_name_slug;
        $this->line("   Store URL: /store/{$storeSlug}");
        
        if ($testProduct) {
            $this->line("   Product URL: /store/{$storeSlug}/{$testProduct->slug}");
        }
        $this->newLine();

        $this->info('✅ Chatbot features test completed!');
        $this->info('You can now test the features in the browser:');
        $this->info("1. Visit the store: /store/{$storeSlug}");
        $this->info("2. Check membership badges and chatbot indicators");
        $this->info("3. Test product chatbot functionality");
        
        return Command::SUCCESS;
    }
}
