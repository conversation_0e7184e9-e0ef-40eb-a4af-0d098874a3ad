<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseSection;
use App\Models\CourseCurriculumItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CourseCurriculumItemController extends Controller
{
    /**
     * Store a newly created curriculum item in storage.
     */
    public function store(Request $request, Course $course, CourseSection $section)
    {
        // Ensure the course and section belong to the authenticated seller
        if ($course->seller_id !== Auth::id() || $section->course_id !== $course->id) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:lecture,video,pdf,document',
            'content' => 'nullable|string',
            'file' => 'nullable|file|max:20480', // 20MB max
            'estimated_duration' => 'nullable|integer|min:1',
            'is_preview' => 'nullable|boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Custom validation for content/file based on type
        if (in_array($validated['type'], ['lecture', 'video']) && empty($validated['content'])) {
            return response()->json([
                'success' => false,
                'message' => 'Content is required for lecture and video types.',
                'errors' => ['content' => ['Content is required for lecture and video types.']]
            ], 422);
        }

        if (in_array($validated['type'], ['pdf', 'document']) && !$request->hasFile('file') && empty($validated['content'])) {
            return response()->json([
                'success' => false,
                'message' => 'Either file upload or content is required for PDF and document types.',
                'errors' => ['file' => ['Either file upload or content is required for PDF and document types.']]
            ], 422);
        }

        $validated['course_id'] = $course->id;
        $validated['section_id'] = $section->id;

        // Handle file upload for PDF and document type items
        if (in_array($validated['type'], ['pdf', 'document']) && $request->hasFile('file')) {
            $file = $request->file('file');
            $path = $file->store('courses/curriculum-items', 'public');
            $validated['file_path'] = $path;
            $validated['content'] = null; // Clear content for file type

            // Store file metadata
            $validated['metadata'] = [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
            ];
        } else {
            $validated['file_path'] = null;
        }

        // If no sort_order provided, set it to the next available position
        if (!isset($validated['sort_order'])) {
            $maxOrder = $section->curriculumItems()->max('sort_order') ?? 0;
            $validated['sort_order'] = $maxOrder + 1;
        }

        $item = CourseCurriculumItem::create($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Curriculum item created successfully!',
                'item' => $item->fresh(),
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Curriculum item created successfully!');
    }

    /**
     * Update the specified curriculum item in storage.
     */
    public function update(Request $request, Course $course, CourseSection $section, CourseCurriculumItem $item)
    {
        // Ensure the course, section, and item belong to the authenticated seller
        if ($course->seller_id !== Auth::id() || 
            $section->course_id !== $course->id || 
            $item->section_id !== $section->id) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:lecture,video,pdf,document',
            'content' => 'nullable|string',
            'file' => 'nullable|file|max:20480', // 20MB max
            'estimated_duration' => 'nullable|integer|min:1',
            'is_active' => 'nullable|boolean',
            'is_preview' => 'nullable|boolean',
            'remove_file' => 'nullable|boolean',
        ]);

        // Custom validation for content/file based on type (only for new items without existing content/file)
        if (in_array($validated['type'], ['lecture', 'video']) && empty($validated['content']) && empty($item->content)) {
            return response()->json([
                'success' => false,
                'message' => 'Content is required for lecture and video types.',
                'errors' => ['content' => ['Content is required for lecture and video types.']]
            ], 422);
        }

        // Handle file removal
        if ($request->input('remove_file') && $item->file_path) {
            Storage::disk('public')->delete($item->file_path);
            $validated['file_path'] = null;
            $validated['metadata'] = null;
        }

        // Handle new file upload for PDF and document types
        if (in_array($validated['type'], ['pdf', 'document']) && $request->hasFile('file')) {
            // Delete old file if exists
            if ($item->file_path) {
                Storage::disk('public')->delete($item->file_path);
            }
            
            $file = $request->file('file');
            $path = $file->store('courses/curriculum-items', 'public');
            $validated['file_path'] = $path;
            $validated['content'] = null; // Clear content for file type
            
            // Store file metadata
            $validated['metadata'] = [
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
            ];
        } elseif (in_array($validated['type'], ['lecture', 'video'])) {
            // Clear file data for non-file types
            $validated['file_path'] = null;
            $validated['metadata'] = null;
        }

        $item->update($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Curriculum item updated successfully!',
                'item' => $item->fresh(),
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Curriculum item updated successfully!');
    }

    /**
     * Remove the specified curriculum item from storage.
     */
    public function destroy(Course $course, CourseSection $section, CourseCurriculumItem $item)
    {
        // Ensure the course, section, and item belong to the authenticated seller
        if ($course->seller_id !== Auth::id() || 
            $section->course_id !== $course->id || 
            $item->section_id !== $section->id) {
            abort(403);
        }

        $item->delete();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Curriculum item deleted successfully!',
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Curriculum item deleted successfully!');
    }

    /**
     * Reorder curriculum items for the section.
     */
    public function reorder(Request $request, Course $course, CourseSection $section)
    {
        // Ensure the course and section belong to the authenticated seller
        if ($course->seller_id !== Auth::id() || $section->course_id !== $course->id) {
            abort(403);
        }

        $validated = $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:course_curriculum_items,id',
            'items.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['items'] as $itemData) {
            CourseCurriculumItem::where('id', $itemData['id'])
                ->where('section_id', $section->id)
                ->update(['sort_order' => $itemData['sort_order']]);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Curriculum items reordered successfully!',
            ]);
        }

        return redirect()->route('seller.courses.show', $course)
            ->with('success', 'Curriculum items reordered successfully!');
    }

    /**
     * Toggle the active status of a curriculum item.
     */
    public function toggleStatus(Request $request, Course $course, CourseSection $section, CourseCurriculumItem $item)
    {
        // Ensure the course, section, and item belong to the authenticated seller
        if ($course->seller_id !== Auth::id() || 
            $section->course_id !== $course->id || 
            $item->section_id !== $section->id) {
            abort(403);
        }

        // Validate the status input
        $validated = $request->validate([
            'status' => 'required|in:0,1'
        ]);

        $isActive = (bool) $validated['status'];

        $item->update([
            'is_active' => $isActive
        ]);

        $status = $isActive ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Curriculum item {$status} successfully!",
            'status' => $isActive ? 'active' : 'inactive',
            'item' => $item->fresh(),
        ]);
    }
}
