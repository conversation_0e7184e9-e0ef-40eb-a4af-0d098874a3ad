// Seller Application Form
document.addEventListener('DOMContentLoaded', function() {
    // We don't need to add seller-page class here as it's already in the HTML
    // Instead, let's make sure the background extends properly
    const mainContainer = document.querySelector('.min-h-screen.bg-gradient-to-br.seller-page');
    if (mainContainer) {
        // Ensure the container takes full height and has no margin gap
        document.body.style.backgroundColor = '#eef2ff';
    }

    const sellerForm = document.querySelector('.seller-apply-form');
    if (sellerForm) {
        // Get the buttons and step input
        const continueButton = document.getElementById('continue-button');
        const backButton = document.getElementById('back-button');
        const stepInput = document.getElementById('step-input');
        const container = document.querySelector('.container.max-w-2xl');

        // Store form data between steps
        let formData = {};

        // Add animation class to container for better spacing
        if (container) {
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            setTimeout(() => {
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        }

        // Debug: Log the current step
        console.log('Current Step:', stepInput.value);

        // Animate progress steps
        animateProgressSteps();

        // Cache the current form content for smooth transitions
        let formSections = {};

        // Initialize form sections cache and set up event listeners
        initializeFormSections();

        function initializeFormSections() {
            // Get all possible form sections from the server or pre-render them
            // For now, we'll just store the current one
            const currentStep = parseInt(stepInput.value);
            const formContent = document.querySelector('.seller-apply-form .form-container');
            if (formContent) {
                // Cache the current form content
                formSections[currentStep] = formContent.innerHTML;

                // Set up event listeners for the current form elements
                setupFormEventListeners(formContent);

                // Pre-fill the formData object with initial values
                const inputs = formContent.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    if (input.name && input.value) {
                        formData[input.name] = input.value;
                    }
                });

                // Add a subtle entrance animation
                formContent.style.opacity = '0';
                formContent.style.transform = 'translateY(10px)';
                setTimeout(() => {
                    formContent.style.transition = 'opacity 0.6s ease-out, transform 0.6s cubic-bezier(0.16, 1, 0.3, 1)';
                    formContent.style.opacity = '1';
                    formContent.style.transform = 'translateY(0)';
                }, 100);
            }
        }

        // Handle continue button click
        if (continueButton) {
            continueButton.addEventListener('click', (e) => {
                e.preventDefault(); // Prevent form submission

                // Validate the current step with smooth animations
                const stepInputs = sellerForm.querySelectorAll('input[required], textarea[required], select[required]');
                let hasError = false;

                stepInputs.forEach(input => {
                    if (!input.value.trim()) {
                        hasError = true;
                        input.classList.add('border-red-500');
                        input.classList.add('shake-animation');

                        // Remove shake animation after it completes
                        setTimeout(() => {
                            input.classList.remove('shake-animation');
                        }, 500);

                        const errorMessage = input.nextElementSibling?.classList.contains('text-red-500') ?
                            input.nextElementSibling : document.createElement('p');
                        errorMessage.classList.add('text-red-500', 'text-xs', 'mt-1');
                        errorMessage.textContent = 'This field is required';
                        errorMessage.style.opacity = '0';

                        if (!input.nextElementSibling?.classList.contains('text-red-500')) {
                            input.parentNode.appendChild(errorMessage);
                            // Fade in the error message
                            setTimeout(() => {
                                errorMessage.style.opacity = '1';
                                errorMessage.style.transition = 'opacity 0.3s ease';
                            }, 10);
                        }
                    } else {
                        input.classList.remove('border-red-500');
                        if (input.nextElementSibling?.classList.contains('text-red-500')) {
                            // Fade out the error message before removing
                            const errorMsg = input.nextElementSibling;
                            errorMsg.style.opacity = '0';
                            setTimeout(() => {
                                errorMsg.remove();
                            }, 300);
                        }

                        // Store form data
                        formData[input.name] = input.value;
                    }
                });

                if (hasError) {
                    // Use a more elegant notification instead of alert
                    showNotification('Please fill in all required fields.', 'error');
                    return;
                }

                // Store current form section if not already stored
                const currentStep = parseInt(stepInput.value);
                const currentFormContent = document.querySelector('.seller-apply-form .bg-white');
                if (currentFormContent && !formSections[currentStep]) {
                    formSections[currentStep] = currentFormContent.innerHTML;
                }

                // Update the progress bar without page refresh
                const nextStep = currentStep + 1;

                // Update the progress bar width with smooth animation
                const progressBar = document.querySelector('.absolute.h-1.bg-indigo-600');
                if (progressBar) {
                    const totalSteps = document.querySelectorAll('.flex.justify-between.max-w-md > div').length - 1;
                    const newWidth = ((nextStep - 1) / totalSteps) * 100 + '%';
                    progressBar.style.width = newWidth;
                }

                // Update step indicators with animation
                const stepIndicators = document.querySelectorAll('.flex.justify-between.max-w-md > div');
                stepIndicators.forEach((indicator, index) => {
                    const stepNum = index + 1;
                    const circle = indicator.querySelector('.rounded-full');
                    const text = indicator.querySelector('.text-xs');

                    if (stepNum < nextStep) {
                        // Completed step
                        circle.classList.add('border-indigo-600', 'bg-indigo-600', 'text-white');
                        circle.classList.remove('border-gray-300', 'text-gray-300');
                        text.classList.add('text-indigo-600');
                        text.classList.remove('text-gray-400');

                        // Add check icon if not already present
                        if (!circle.querySelector('svg')) {
                            circle.innerHTML = `<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>`;
                            // Animate the check mark
                            const svg = circle.querySelector('svg');
                            if (svg) {
                                svg.style.opacity = '0';
                                svg.style.transform = 'scale(0.5)';
                                setTimeout(() => {
                                    svg.style.transition = 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)';
                                    svg.style.opacity = '1';
                                    svg.style.transform = 'scale(1)';
                                }, 50);
                            }
                        }
                    } else if (stepNum === nextStep) {
                        // Current step with animation
                        setTimeout(() => {
                            circle.classList.add('border-indigo-600', 'text-indigo-600');
                            circle.classList.remove('border-gray-300', 'text-gray-300', 'bg-indigo-600', 'text-white');
                            text.classList.add('text-indigo-600');
                            text.classList.remove('text-gray-400');
                            circle.innerHTML = stepNum;

                            // Add pulse effect
                            circle.animate([
                                { transform: 'scale(1)', boxShadow: '0 0 0 4px rgba(99, 102, 241, 0.1)' },
                                { transform: 'scale(1.2)', boxShadow: '0 0 0 6px rgba(99, 102, 241, 0.2)' },
                                { transform: 'scale(1.1)', boxShadow: '0 0 0 5px rgba(99, 102, 241, 0.15)' }
                            ], {
                                duration: 600,
                                easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
                                fill: 'forwards'
                            });
                        }, 300); // Slight delay for better visual flow
                    }
                });

                // Set the step value
                stepInput.value = nextStep;

                // Transition to the next form section without page refresh
                const formContent = document.querySelector('.seller-apply-form .form-container');
                if (formContent) {
                    // Fade out current content with a slide effect
                    formContent.style.transition = 'opacity 0.5s ease-out, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
                    formContent.style.opacity = '0';
                    formContent.style.transform = 'translateY(10px) scale(0.98)';

                    // After fade out, load next content or fetch from server
                    setTimeout(() => {
                        // Check if we have the next section cached
                        if (formSections[nextStep]) {
                            // Use cached content
                            formContent.innerHTML = formSections[nextStep];

                            // Restore any form values from formData
                            const inputs = formContent.querySelectorAll('input, textarea, select');
                            inputs.forEach(input => {
                                if (formData[input.name]) {
                                    input.value = formData[input.name];
                                }
                            });

                            // Add event listeners to new form elements
                            setupFormEventListeners(formContent);

                            // Fade in new content with a slide-up effect
                            setTimeout(() => {
                                formContent.style.opacity = '1';
                                formContent.style.transform = 'translateY(0) scale(1)';
                            }, 50);
                        } else {
                            // If not cached, make an AJAX request to get the next step content
                            // Create a FormData object to send the current form data
                            const formDataObj = new FormData(sellerForm);
                            formDataObj.append('_ajax_request', 'true');
                            formDataObj.append('step', nextStep);

                            // Use fetch API to get the next step content
                            fetch(sellerForm.action, {
                                method: 'POST',
                                body: formDataObj,
                                headers: {
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            })
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('Network response was not ok');
                                }
                                return response.text();
                            })
                            .then(html => {
                                // Extract the form content from the response
                                const parser = new DOMParser();
                                const doc = parser.parseFromString(html, 'text/html');
                                const newContent = doc.querySelector('.form-container');

                                if (newContent) {
                                    // Cache the content for future use
                                    formSections[nextStep] = newContent.innerHTML;

                                    // Update the form content
                                    formContent.innerHTML = newContent.innerHTML;

                                    // Add event listeners to new form elements
                                    setupFormEventListeners(formContent);

                                    // Fade in new content
                                    setTimeout(() => {
                                        formContent.style.opacity = '1';
                                        formContent.style.transform = 'translateY(0) scale(1)';
                                    }, 50);
                                } else {
                                    // Fallback to traditional form submission if content not found
                                    sellerForm.submit();
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching next step:', error);
                                // Fallback to traditional form submission
                                sellerForm.submit();
                            });
                        }
                    }, 400);
                } else {
                    // Fallback to traditional form submission if content element not found
                    sellerForm.submit();
                }
            });
        }

        // Handle back button click
        if (backButton) {
            backButton.addEventListener('click', (e) => {
                e.preventDefault(); // Prevent form submission

                // Update the progress bar without page refresh
                const currentStep = parseInt(stepInput.value);
                const prevStep = currentStep - 1;

                // Store current form section if not already stored
                if (!formSections[currentStep]) {
                    const currentFormContent = document.querySelector('.seller-apply-form .bg-white');
                    if (currentFormContent) {
                        formSections[currentStep] = currentFormContent.innerHTML;
                    }
                }

                // Update the progress bar width with smooth animation
                const progressBar = document.querySelector('.absolute.h-1.bg-indigo-600');
                if (progressBar) {
                    const totalSteps = document.querySelectorAll('.flex.justify-between.max-w-md > div').length - 1;
                    const newWidth = ((prevStep - 1) / totalSteps) * 100 + '%';
                    progressBar.style.width = newWidth;
                }

                // Update step indicators with animation
                const stepIndicators = document.querySelectorAll('.flex.justify-between.max-w-md > div');
                stepIndicators.forEach((indicator, index) => {
                    const stepNum = index + 1;
                    const circle = indicator.querySelector('.rounded-full');
                    const text = indicator.querySelector('.text-xs');

                    if (stepNum === currentStep) {
                        // Remove current step styling with animation
                        circle.animate([
                            { transform: 'scale(1.1)', boxShadow: '0 0 0 5px rgba(99, 102, 241, 0.15)' },
                            { transform: 'scale(1)', boxShadow: '0 0 0 0px rgba(99, 102, 241, 0)' }
                        ], {
                            duration: 400,
                            easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
                            fill: 'forwards'
                        });

                        setTimeout(() => {
                            circle.classList.remove('border-indigo-600', 'text-indigo-600');
                            circle.classList.add('border-gray-300', 'text-gray-300');
                            text.classList.remove('text-indigo-600');
                            text.classList.add('text-gray-400');
                            circle.innerHTML = stepNum;
                        }, 200);
                    } else if (stepNum === prevStep) {
                        // Set previous step as current with animation
                        setTimeout(() => {
                            circle.classList.add('border-indigo-600', 'text-indigo-600');
                            circle.classList.remove('bg-indigo-600', 'text-white');
                            text.classList.add('text-indigo-600');
                            text.classList.remove('text-gray-400');

                            // If there's a check icon, replace it with the step number
                            if (circle.querySelector('svg')) {
                                // Fade out the check icon
                                const svg = circle.querySelector('svg');
                                svg.style.transition = 'all 0.3s ease';
                                svg.style.opacity = '0';
                                svg.style.transform = 'scale(0.5)';

                                // Replace with step number after fade out
                                setTimeout(() => {
                                    circle.innerHTML = stepNum;
                                    // Highlight the current step
                                    circle.animate([
                                        { transform: 'scale(0.9)', boxShadow: '0 0 0 2px rgba(99, 102, 241, 0.1)' },
                                        { transform: 'scale(1.1)', boxShadow: '0 0 0 5px rgba(99, 102, 241, 0.2)' },
                                        { transform: 'scale(1)', boxShadow: '0 0 0 4px rgba(99, 102, 241, 0.15)' }
                                    ], {
                                        duration: 500,
                                        easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
                                        fill: 'forwards'
                                    });
                                }, 300);
                            } else {
                                // Highlight the current step
                                circle.animate([
                                    { transform: 'scale(0.9)', boxShadow: '0 0 0 2px rgba(99, 102, 241, 0.1)' },
                                    { transform: 'scale(1.1)', boxShadow: '0 0 0 5px rgba(99, 102, 241, 0.2)' },
                                    { transform: 'scale(1)', boxShadow: '0 0 0 4px rgba(99, 102, 241, 0.15)' }
                                ], {
                                    duration: 500,
                                    easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
                                    fill: 'forwards'
                                });
                            }
                        }, 200);
                    }
                });

                // Set the step value
                stepInput.value = prevStep;

                // Transition to the previous form section without page refresh
                const formContent = document.querySelector('.seller-apply-form .form-container');
                if (formContent) {
                    // Fade out current content with a slide effect
                    formContent.style.transition = 'opacity 0.5s ease-out, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
                    formContent.style.opacity = '0';
                    formContent.style.transform = 'translateY(-10px) scale(0.98)';

                    // After fade out, load previous content or fetch from server
                    setTimeout(() => {
                        // Check if we have the previous section cached
                        if (formSections[prevStep]) {
                            // Use cached content
                            formContent.innerHTML = formSections[prevStep];

                            // Restore any form values from formData
                            const inputs = formContent.querySelectorAll('input, textarea, select');
                            inputs.forEach(input => {
                                if (formData[input.name]) {
                                    input.value = formData[input.name];
                                }
                            });

                            // Add event listeners to new form elements
                            setupFormEventListeners(formContent);

                            // Fade in new content with a slide-up effect
                            setTimeout(() => {
                                formContent.style.opacity = '1';
                                formContent.style.transform = 'translateY(0) scale(1)';
                            }, 50);
                        } else {
                            // If not cached, make an AJAX request to get the previous step content
                            // Create a FormData object to send the current form data
                            const formDataObj = new FormData(sellerForm);
                            formDataObj.append('_ajax_request', 'true');
                            formDataObj.append('step', prevStep);

                            // Use fetch API to get the previous step content
                            fetch(sellerForm.action, {
                                method: 'POST',
                                body: formDataObj,
                                headers: {
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            })
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error('Network response was not ok');
                                }
                                return response.text();
                            })
                            .then(html => {
                                // Extract the form content from the response
                                const parser = new DOMParser();
                                const doc = parser.parseFromString(html, 'text/html');
                                const newContent = doc.querySelector('.form-container');

                                if (newContent) {
                                    // Cache the content for future use
                                    formSections[prevStep] = newContent.innerHTML;

                                    // Update the form content
                                    formContent.innerHTML = newContent.innerHTML;

                                    // Add event listeners to new form elements
                                    setupFormEventListeners(formContent);

                                    // Fade in new content
                                    setTimeout(() => {
                                        formContent.style.opacity = '1';
                                        formContent.style.transform = 'translateY(0) scale(1)';
                                    }, 50);
                                } else {
                                    // Fallback to traditional form submission if content not found
                                    sellerForm.submit();
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching previous step:', error);
                                // Fallback to traditional form submission
                                sellerForm.submit();
                            });
                        }
                    }, 400);
                } else {
                    // Fallback to traditional form submission if content element not found
                    sellerForm.submit();
                }
            });
        }

        // Remove error styles on input with smooth transitions
        const inputs = sellerForm.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                input.classList.remove('border-red-500');
                if (input.nextElementSibling?.classList.contains('text-red-500')) {
                    const errorMsg = input.nextElementSibling;
                    errorMsg.style.opacity = '0';
                    errorMsg.style.transition = 'opacity 0.3s ease';
                    setTimeout(() => {
                        errorMsg.remove();
                    }, 300);
                }
            });

            // Add focus effects
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused-field');
            });

            input.addEventListener('blur', () => {
                input.parentElement.classList.remove('focused-field');
            });
        });

        // Scroll to top on page load with smooth animation
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Helper function to set up event listeners for form elements
    function setupFormEventListeners(container) {
        // Add event listeners to inputs, selects, and textareas
        const formElements = container.querySelectorAll('input, select, textarea');
        formElements.forEach(element => {
            // Store input values in formData object on change
            element.addEventListener('input', () => {
                if (element.name) {
                    formData[element.name] = element.value;
                }
            });

            // Add focus effects
            element.addEventListener('focus', () => {
                element.parentElement.classList.add('focused-field');
            });

            element.addEventListener('blur', () => {
                element.parentElement.classList.remove('focused-field');
            });

            // Remove error styles on input
            element.addEventListener('input', () => {
                element.classList.remove('border-red-500');
                if (element.nextElementSibling?.classList.contains('text-red-500')) {
                    const errorMsg = element.nextElementSibling;
                    errorMsg.style.opacity = '0';
                    errorMsg.style.transition = 'opacity 0.3s ease';
                    setTimeout(() => {
                        errorMsg.remove();
                    }, 300);
                }
            });
        });
    }

    // Helper function to animate progress steps
    function animateProgressSteps() {
        const steps = document.querySelectorAll('.flex.justify-between.max-w-md > div');
        const currentStep = parseInt(document.getElementById('step-input')?.value || '1');

        steps.forEach((step, index) => {
            const stepNumber = index + 1;
            const circle = step.querySelector('.rounded-full');
            const text = step.querySelector('.text-xs');

            if (stepNumber < currentStep) {
                // Completed steps
                setTimeout(() => {
                    circle.classList.add('completed-step');
                    text.classList.add('completed-step-text');
                }, 100 * stepNumber);
            } else if (stepNumber === currentStep) {
                // Current step
                setTimeout(() => {
                    circle.classList.add('current-step');
                    text.classList.add('current-step-text');
                }, 100 * stepNumber);
            }
        });
    }

    // Helper function to show notifications
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <p>${message}</p>
            </div>
        `;

        // Add styles
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.padding = '12px 20px';
        notification.style.borderRadius = '8px';
        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        notification.style.zIndex = '9999';
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(-10px)';
        notification.style.transition = 'all 0.3s ease';

        if (type === 'error') {
            notification.style.backgroundColor = '#FEE2E2';
            notification.style.color = '#B91C1C';
            notification.style.border = '1px solid #FECACA';
        } else {
            notification.style.backgroundColor = '#E0F2FE';
            notification.style.color = '#0369A1';
            notification.style.border = '1px solid #BAE6FD';
        }

        // Add to DOM
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateY(0)';
        }, 10);

        // Remove after 4 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 4000);
    }
});

// Add CSS for animations
document.addEventListener('DOMContentLoaded', function() {
    // Add CSS for shake animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake-animation {
            animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
        }

        .focused-field {
            transform: translateX(5px);
            transition: transform 0.3s ease;
        }

        .completed-step {
            background-color: #4F46E5 !important;
            transform: scale(1.05);
            transition: all 0.5s ease;
        }

        .current-step {
            transform: scale(1.1);
            box-shadow: 0 0 0 5px rgba(99, 102, 241, 0.2);
            transition: all 0.5s ease;
        }

        .completed-step-text, .current-step-text {
            font-weight: 600;
            transition: all 0.3s ease;
        }
    `;
    document.head.appendChild(style);
});