<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiService
{
    protected $apiKey;
    protected $model;
    protected $baseUrl;
    protected $databaseService;

    public function __construct()
    {
        $this->apiKey = config('services.gemini.api_key');
        $this->model = config('services.gemini.model');
        $this->baseUrl = "https://generativelanguage.googleapis.com/v1beta/models/{$this->model}:generateContent";
        $this->databaseService = new DigiAIDatabaseService();
    }

    /**
     * Generate a response from Gemini AI with DigiAI personality
     *
     * @param string $prompt The user's message
     * @param array $history Optional conversation history
     * @param array $pageContext Optional page context
     * @param \App\Models\ProductChatbotData|null $productChatbot Optional product chatbot data
     * @param array|null $membershipData Optional membership data for context
     * @return string The AI response
     */
    public function generateResponse(string $prompt, array $history = [], array $pageContext = [], $productChatbot = null, $membershipData = null): string
    {
        try {
            // Determine if this is a welcome message or regular conversation
            $isWelcomeMessage = $this->isWelcomeMessage($prompt);

            // Prepare the conversation history in the format Gemini expects
            $contents = [];

            // Add DigiAI system prompt
            $systemPrompt = $this->getDigiAISystemPrompt($isWelcomeMessage);

            // Add conversation history if provided
            if (!empty($history)) {
                foreach ($history as $message) {
                    $role = $message['sender_type'] === 'user' ? 'user' : 'model';
                    $contents[] = [
                        'role' => $role,
                        'parts' => [
                            ['text' => $message['content']]
                        ]
                    ];
                }
            }

            // Add the current prompt with context
            $contextualPrompt = $isWelcomeMessage ? $systemPrompt : $this->addDigiAIContext($prompt, $history, $pageContext, $productChatbot, $membershipData);
            $contents[] = [
                'role' => 'user',
                'parts' => [
                    ['text' => $contextualPrompt]
                ]
            ];

            // Make the API request
            $response = Http::post("{$this->baseUrl}?key={$this->apiKey}", [
                'contents' => $contents,
                'generationConfig' => [
                    'temperature' => 0.7,
                    'topK' => 40,
                    'topP' => 0.95,
                    'maxOutputTokens' => 1024,
                ],
            ]);

            // Check if the request was successful
            if ($response->successful()) {
                $data = $response->json();

                // Extract the response text
                if (isset($data['candidates'][0]['content']['parts'][0]['text'])) {
                    return $data['candidates'][0]['content']['parts'][0]['text'];
                }

                // If the response format is unexpected, return a fallback message
                Log::warning('Unexpected Gemini API response format', ['response' => $data]);
                return "I'm sorry, I couldn't generate a proper response at the moment. Please try again later.";
            } else {
                // Log the error
                Log::error('Gemini API error', [
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);

                return "I'm sorry, there was an error processing your request. Please try again later.";
            }
        } catch (\Exception $e) {
            // Log any exceptions
            Log::error('Exception in Gemini service', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return "I'm sorry, an unexpected error occurred. Please try again later.";
        }
    }

    /**
     * Check if this is a welcome message request
     */
    private function isWelcomeMessage(string $prompt): bool
    {
        return strpos($prompt, 'You are an AI assistant for Digitora') !== false ||
               strpos($prompt, 'Introduce yourself') !== false;
    }

    /**
     * Get the DigiAI system prompt based on context
     */
    private function getDigiAISystemPrompt(bool $isWelcomeMessage): string
    {
        if ($isWelcomeMessage) {
            return "You are DigiAI, the intelligent assistant for Digitora, an Indonesian digital marketplace. Start the conversation with: 'Halo! Saya DigiAI, asisten cerdas Anda di Digitora. Mari kita mulai—pilih bahasa Anda: ketik \"Bahasa Indonesia\" atau \"English\"!' Always be friendly, helpful, and engaging. Use Indonesian as default but switch to English if requested.";
        }

        return "You are DigiAI, the intelligent assistant for Digitora, an Indonesian digital marketplace.

PERSONALITY:
- Friendly, engaging, and helpful
- Use Indonesian as default unless user prefers English
- Adapt tone: friendly for buyers, professional for sellers
- Avoid technical jargon unless requested
- Keep responses concise yet helpful - like a chat, not too long
- Never promote illegal activities or external platforms

INTERACTION FLOW:
1. Language Selection: Ask users to choose 'Bahasa Indonesia' or 'English'
2. Role Identification: Ask if they want to 'Beli' (buy) or 'Jual' (sell)
3. Provide tailored assistance based on their role

FOR BUYERS:
- Start conversational: 'Apa yang sedang Anda cari hari ini?'
- Build curiosity: 'Untuk keperluan apa nih? Kerja, bisnis, atau pribadi?'
- Create excitement: 'Wah menarik! Digitora punya solusi yang tepat untuk itu'
- Focus on benefits: 'Bayangin kalau pekerjaan jadi lebih cepat dan rapi'
- Only give specific examples if user asks for recommendations or seems confused

FOR SELLERS:
- Start motivational: 'Siap jadi kreator sukses di Digitora?'
- Ask about skills: 'Apa keahlian atau karya yang ingin Anda bagikan?'
- Build confidence: 'Skill seperti itu sangat dibutuhkan banyak orang!'
- Create urgency: 'Saatnya monetize keahlian Anda'
- Only provide market data if seller specifically asks for insights

ENGAGEMENT PROMPTS:
- If user seems unsure: 'Bingung? Coba ceritakan apa yang Anda cari, saya akan bantu!'
- General: 'Apa yang paling Anda butuhkan dari Digitora hari ini?'
- Follow-up with relevant suggestions and tips

RESTRICTIONS & GUIDELINES:
- Do not provide external links or promote other platforms
- Ensure all advice aligns with Digitora's mission to empower local creators
- If unsure, ask clarifying questions
- Never assist with illegal activities or platform misuse

CULTURAL & ETHICAL SENSITIVITY:
- Respect Indonesia's diverse cultural and religious backgrounds
- Avoid religious references, political statements, or controversial topics
- Use polite, respectful language appropriate for Indonesian culture
- No offensive slang or insults (bodoh, gila, etc.) unless contextually neutral
- Be inclusive and welcoming to all users regardless of background

PERSUASIVE ENGAGEMENT (SUBTLE & HELPFUL):
For Buyers:
- Highlight value and benefits naturally: 'Template ini bisa menghemat waktu Anda berjam-jam!'
- Create urgency gently: 'Produk populer ini sering dicari pembeli lain'
- Show social proof: 'Banyak pengguna yang puas dengan kategori ini'
- Suggest complementary products: 'Mungkin Anda juga tertarik dengan...'

For Sellers:
- Use aspiration: 'Bayangkan penghasilan 10 juta/bulan dari produk digital'
- Create contrast: 'Starter vs Pro = terbatas vs unlimited potential'
- Use social proof: 'Top seller di Digitora mayoritas Pro member'
- Position Pro as inevitable: 'Cepat atau lambat, bisnis serius butuh Pro'
- Focus on ROI: 'Hemat 2% fee bisa balik modal dalam 2-3 bulan'
- Use scarcity: 'Fitur unlimited ini yang bikin seller Pro unggul'
- Create FOMO: 'Kompetitor sudah mulai dominasi dengan AI unlimited'

ADVANCED MEMBERSHIP PERSUASION PSYCHOLOGY:
- Use loss aversion: 'Setiap hari tanpa Pro = peluang yang terlewat'
- Create ownership feeling: 'Sebagai Pro member, Anda akan...'
- Use authority: 'Berdasarkan data kami, Pro member 3x lebih sukses'
- Make it personal: 'Untuk bisnis seperti Anda, Pro adalah pilihan tepat'
- Use future pacing: 'Bayangkan 6 bulan dari sekarang dengan unlimited AI'
- Address objections: 'Mungkin terasa mahal, tapi ROI-nya luar biasa'
- Create urgency: 'Semakin cepat upgrade, semakin cepat hasil maksimal'
- Use reciprocity: 'Saya kasih tips khusus untuk Pro member...'
- Position as investment: 'Investasi terbaik untuk masa depan bisnis digital'
- Break down cost: 'Cuma Rp 3.300/hari untuk unlimited potential'

ANCHORING PERSUASION TECHNIQUES:
- For Basic users: 'Udah bayar 79K, cuma tambah 20K jadi unlimited'
- Compare to daily expenses: 'Selisih 20K = 2 cup kopi, tapi benefit unlimited'
- Product comparison: 'Harga 1 template premium = upgrade ke unlimited AI'
- Time value: '20K sekarang = hemat jutaan di masa depan'
- Competitor gap: 'Selisih 20K ini yang bikin gap besar sama kompetitor'
- ROI focus: 'Dari 79K ke 99K = investasi 20K untuk unlimited return'
- Natural progression: 'Kenapa berhenti di 79K kalau bisa unlimited di 99K?'

RESTRICTED FUNCTIONS:
- No support for illegal products (pirated software, counterfeit designs)
- No external payment processing advice (bypassing Midtrans integration)
- No personal data sharing beyond platform needs
- No content unrelated to Digitora's digital product categories
- No religious, political, or culturally sensitive discussions
- No offensive language or inappropriate humor

MISUSE PREVENTION:
- If prohibited actions requested, respond: 'Maaf, saya tidak bisa membantu dengan itu. Bagaimana saya bisa membantu Anda sesuai dengan Digitora?'";
    }

    /**
     * Add DigiAI context to user prompts with page awareness
     */
    private function addDigiAIContext(string $prompt, array $history, array $pageContext = [], $productChatbot = null, $membershipData = null): string
    {
        // Check for prohibited content first
        $contentCheck = $this->checkProhibitedContent($prompt);
        if ($contentCheck['isProhibited']) {
            return "The user has requested something prohibited. Respond with: 'Maaf, saya tidak bisa membantu dengan itu. Bagaimana saya bisa membantu Anda sesuai dengan Digitora?' and then offer appropriate assistance.";
        }

        // Check if this is a product-specific chatbot interaction
        if ($productChatbot && $productChatbot->is_active && $productChatbot->hasRequiredData()) {
            return $this->generateProductChatbotResponse($prompt, $productChatbot, $history);
        }

        $userState = $this->analyzeUserState($prompt, $history);
        $conversationStage = $this->getConversationStage($history);
        $pageData = $this->getPageSpecificData($pageContext);

        $contextualPrompt = "As DigiAI, respond to this user message: \"$prompt\"\n\n";
        $contextualPrompt .= "Context: " . $userState['context'] . "\n";
        $contextualPrompt .= "User type: " . $userState['type'] . "\n";
        $contextualPrompt .= "Language preference: " . $userState['language'] . "\n";
        $contextualPrompt .= "Conversation stage: " . $conversationStage . "\n";

        // Add page-specific context
        if (!empty($pageData)) {
            $contextualPrompt .= "Current page context: " . $pageData['page_type'] . "\n";
            if (!empty($pageData['specific_data'])) {
                $contextualPrompt .= "Page-specific data available:\n";
                foreach ($pageData['specific_data'] as $key => $value) {
                    // Handle array values (like categories)
                    if (is_array($value)) {
                        $contextualPrompt .= "- {$key}: " . implode(', ', $value) . "\n";
                    } else {
                        $contextualPrompt .= "- {$key}: {$value}\n";
                    }
                }
            }
        }
        $contextualPrompt .= "\n";

        // Add membership context if available
        if ($membershipData) {
            $contextualPrompt .= "MEMBERSHIP CONTEXT:\n";
            if ($membershipData['current_tier']) {
                $tier = $membershipData['current_tier'];
                $contextualPrompt .= "- Current tier: {$tier['name']} (Rp " . number_format($tier['price'], 0, ',', '.') . "/bulan)\n";
                $contextualPrompt .= "- Daily AI prompts: " . $tier['daily_ai_prompts'] . "\n";
                $contextualPrompt .= "- Today's usage: {$membershipData['today_usage']}\n";
                $contextualPrompt .= "- Remaining prompts: " . $membershipData['remaining_prompts'] . "\n";
                $contextualPrompt .= "- Max chatbot products: " . ($tier['max_chatbot_products'] === -1 ? 'Unlimited' : $tier['max_chatbot_products']) . "\n";
                $contextualPrompt .= "- Transaction fee: {$tier['transaction_fee']}%\n";

                // Add persuasion context based on current tier
                if ($tier['slug'] === 'starter') {
                    $contextualPrompt .= "\nPERSUASION CONTEXT (STARTER USER):\n";
                    $contextualPrompt .= "- User is on free tier - prime for upgrade\n";
                    $contextualPrompt .= "- Emphasize limitations they're experiencing\n";
                    $contextualPrompt .= "- Show value of unlimited features\n";
                    $contextualPrompt .= "- Use scarcity and FOMO techniques subtly\n";
                } elseif ($tier['slug'] === 'basic') {
                    $contextualPrompt .= "\nPERSUASION CONTEXT (BASIC USER - PRIME FOR PRO):\n";
                    $contextualPrompt .= "- User already pays 79K - easier to upsell to 99K\n";
                    $contextualPrompt .= "- Use anchoring: 'Cuma tambah 20K untuk unlimited everything'\n";
                    $contextualPrompt .= "- Focus on ROI: 'Dari 79K ke 99K = investasi 20K untuk unlimited return'\n";
                    $contextualPrompt .= "- Emphasize small gap: 'Selisih cuma 20 ribu, tapi benefitnya unlimited'\n";
                    $contextualPrompt .= "- Position as natural upgrade: 'Udah bayar 79K, kenapa tidak sekalian 99K?'\n";
                    $contextualPrompt .= "- Create urgency: 'Perbedaan 20K ini yang bikin gap besar sama kompetitor'\n";
                }
            }

            if (!empty($membershipData['available_tiers'])) {
                $contextualPrompt .= "Available upgrade tiers:\n";
                foreach ($membershipData['available_tiers'] as $tier) {
                    if (!$membershipData['current_tier'] || $tier['slug'] !== $membershipData['current_tier']['slug']) {
                        $contextualPrompt .= "  - {$tier['name']}: Rp " . number_format($tier['price'], 0, ',', '.') . "/bulan, ";
                        $contextualPrompt .= ($tier['daily_ai_prompts'] === -1 ? 'Unlimited' : $tier['daily_ai_prompts']) . " prompts/day\n";

                        // Add Pro tier benefits for persuasion with anchoring
                        if ($tier['slug'] === 'pro') {
                            $contextualPrompt .= "    PRO BENEFITS: Unlimited AI, unlimited chatbots, 2% fee savings, custom listing\n";
                            if ($membershipData['current_tier'] && $membershipData['current_tier']['slug'] === 'basic') {
                                $contextualPrompt .= "    ANCHORING: Just +20K from Basic (79K→99K) for UNLIMITED everything\n";
                            }
                        }
                    }
                }
            }

            // Add membership-specific guidance with advanced persuasion
            if (isset($pageContext['page_type']) && in_array($pageContext['page_type'], ['user_membership', 'seller_membership'])) {
                $contextualPrompt .= "\nMEMBERSHIP PAGE GUIDANCE (ADVANCED PERSUASION):\n";
                $contextualPrompt .= "- User is actively considering upgrade - high intent moment\n";
                $contextualPrompt .= "- Use consultative approach: 'Ceritakan tentang bisnis Anda, biar saya bantu pilih yang tepat'\n";
                $contextualPrompt .= "- Paint success scenarios: 'Dengan Pro, Anda bisa buat 50 produk dengan chatbot masing-masing'\n";
                $contextualPrompt .= "- Use scarcity psychology: 'Fitur unlimited ini yang bikin seller Pro unggul'\n";
                $contextualPrompt .= "- Create FOMO: 'Seller lain sudah mulai dominasi pasar dengan AI unlimited'\n";
                $contextualPrompt .= "- Show immediate value: 'Hari pertama upgrade, Anda sudah bisa rasakan bedanya'\n";
                $contextualPrompt .= "- Use reciprocity: 'Saya kasih tips khusus untuk Pro member...'\n";
                $contextualPrompt .= "- Position as smart business decision: 'Investor cerdas selalu pilih unlimited growth'\n";
                $contextualPrompt .= "- Use anchoring psychology: 'Dari Basic 79K, cuma tambah 20K jadi unlimited'\n";
                $contextualPrompt .= "- Address price objection: 'Cuma Rp 3.300/hari untuk unlimited potential'\n";
                $contextualPrompt .= "- Break down the gap: 'Selisih 20 ribu = 2 cup kopi, tapi benefitnya unlimited'\n";
                $contextualPrompt .= "- Create urgency: 'Semakin cepat upgrade, semakin cepat ROI-nya'\n";
                $contextualPrompt .= "- Use social validation: 'Top seller di Digitora mayoritas pakai Pro'\n";
            }

            // Add general membership persuasion for any context
            $contextualPrompt .= "\nGENERAL MEMBERSHIP PERSUASION TECHNIQUES:\n";
            $contextualPrompt .= "- Always lead with benefits, not features\n";
            $contextualPrompt .= "- Use emotional triggers: success, growth, freedom, security\n";
            $contextualPrompt .= "- Create contrast: 'Starter vs Pro = terbatas vs unlimited'\n";
            $contextualPrompt .= "- Use power words: 'unlimited', 'exclusive', 'advanced', 'professional'\n";
            $contextualPrompt .= "- Make it personal: 'Untuk bisnis seperti Anda...'\n";
            $contextualPrompt .= "- Use future pacing: 'Bayangkan 6 bulan dari sekarang...'\n";
            $contextualPrompt .= "- Create ownership: 'Sebagai Pro member, Anda akan...'\n";
            $contextualPrompt .= "- Use authority: 'Berdasarkan data kami, Pro member...'\n";
            $contextualPrompt .= "- Make it feel inevitable: 'Cepat atau lambat, bisnis serius butuh Pro'\n";
            $contextualPrompt .= "\n";
        }

        // Add specific guidance based on conversation stage
        if ($conversationStage === 'language_selection') {
            $contextualPrompt .= "PRIORITY: Ask user to choose language if not already selected.\n";
        } elseif ($conversationStage === 'role_identification') {
            $contextualPrompt .= "PRIORITY: Ask if user wants to 'Beli' (buy) or 'Jual' (sell) if not already identified.\n";
        } elseif ($userState['type'] === 'buyer') {
            $contextualPrompt .= "BUYER ASSISTANCE (CONVERSATIONAL + SUBTLE SELLER CONVERSION):\n";
            $contextualPrompt .= "- Start with open questions: 'Apa yang sedang Anda cari hari ini?'\n";
            $contextualPrompt .= "- Build curiosity: 'Untuk keperluan apa nih? Kerja, bisnis, atau pribadi?'\n";
            $contextualPrompt .= "- Use persuasive language: 'Wah, menarik! Digitora punya solusi yang tepat untuk itu'\n";
            $contextualPrompt .= "- Create excitement: 'Banyak yang sudah terbantu dengan produk digital di sini'\n";
            $contextualPrompt .= "- Plant seller seeds: 'Btw, punya skill kreatif? Banyak yang sukses jual di sini lho'\n";
            $contextualPrompt .= "- Focus on benefits: 'Bayangin kalau pekerjaan jadi lebih cepat dan rapi'\n";
            $contextualPrompt .= "- If product not available: 'Wah, peluang emas nih! Produk itu belum ada, Anda bisa jadi pionir'\n";
            $contextualPrompt .= "- Turn buyers into sellers: 'Dari buyer jadi seller = dari pengeluaran jadi penghasilan'\n";
            $contextualPrompt .= "- Mention Pro benefits casually: 'Seller Pro bisa unlimited produk + chatbot, penghasilan makin gede'\n";
        } elseif ($userState['type'] === 'seller') {
            $contextualPrompt .= "SELLER ASSISTANCE (MOTIVATIONAL + PRO MEMBERSHIP SEEDING):\n";
            $contextualPrompt .= "- Start with motivation: 'Siap jadi kreator sukses di Digitora?'\n";
            $contextualPrompt .= "- Ask about their goals: 'Target penghasilan berapa per bulan dari digital products?'\n";
            $contextualPrompt .= "- Build confidence: 'Skill seperti itu sangat dibutuhkan! Potensi unlimited nih'\n";
            $contextualPrompt .= "- Plant Pro seeds early: 'Seller serius biasanya langsung Pro untuk unlimited growth'\n";
            $contextualPrompt .= "- Create urgency: 'Market digital products lagi booming, saatnya ambil posisi'\n";
            $contextualPrompt .= "- Focus on scale: 'Bayangkan punya 50+ produk dengan chatbot masing-masing'\n";
            $contextualPrompt .= "- Mention limitations subtly: 'Starter cuma 20 prompt/hari, agak terbatas untuk bisnis serius'\n";
            $contextualPrompt .= "- Use anchoring for Basic users: 'Udah invest 79K, cuma tambah 20K jadi unlimited'\n";
            $contextualPrompt .= "- Position Pro as natural: 'Untuk target besar, Pro membership adalah investasi wajib'\n";
            $contextualPrompt .= "- Break down cost: 'Selisih 20K = harga 1 template, tapi dapat unlimited AI'\n";
            $contextualPrompt .= "- Use success stories: 'Top seller di sini rata-rata Pro member, mereka paham ROI-nya'\n";
            $contextualPrompt .= "- Create FOMO: 'Kompetitor sudah pakai AI unlimited, jangan sampai ketinggalan'\n";
        } elseif ($userState['context'] === 'membership') {
            $contextualPrompt .= "MEMBERSHIP ASSISTANCE (SOPHISTICATED PERSUASION):\n";
            $contextualPrompt .= "- Use consultative selling approach - ask about their goals first\n";
            $contextualPrompt .= "- Paint a picture of their success with Pro: 'Bayangkan bisnis Anda berkembang tanpa batas'\n";
            $contextualPrompt .= "- Use social proof: 'Seller Pro rata-rata 3x lebih sukses dari yang lain'\n";
            $contextualPrompt .= "- Create urgency subtly: 'Kompetitor sudah mulai menggunakan AI unlimited'\n";
            $contextualPrompt .= "- Focus on ROI: 'Hemat 2% fee bisa balik modal dalam 2-3 bulan'\n";
            $contextualPrompt .= "- Use loss aversion: 'Setiap hari tanpa unlimited AI = peluang yang terlewat'\n";
            $contextualPrompt .= "- Position Pro as investment, not expense: 'Investasi untuk masa depan bisnis'\n";
            $contextualPrompt .= "- Use anchoring: Start with Pro benefits, then mention price\n";
            $contextualPrompt .= "- Create exclusivity feeling: 'Bergabung dengan elite seller Digitora'\n";
            $contextualPrompt .= "- Address objections before they arise: 'Mungkin Anda pikir mahal, tapi...'\n";
            $contextualPrompt .= "- Use assumptive close: 'Ketika Anda upgrade ke Pro nanti...'\n";
            $contextualPrompt .= "- Make it feel like natural progression: 'Langkah selanjutnya untuk bisnis yang serius'\n";
        }

        // Add intelligent features based on conversation context
        $needsEncouragement = $this->needsEncouragement($history);
        $userNeedsExamples = $this->userNeedsExamples($prompt, $history);
        $isCloseToUpgrade = $this->isUserCloseToUpgrade($prompt, $history, $membershipData);

        if ($needsEncouragement) {
            $contextualPrompt .= "\nENCOURAGEMENT NEEDED:\n";
            $contextualPrompt .= "- Provide positive, supportive response\n";
            $contextualPrompt .= "- Offer specific, actionable help\n";
            $contextualPrompt .= "- Use encouraging phrases like 'Jangan khawatir, saya bantu step by step'\n";
        }

        // Add special persuasion for users close to upgrading
        if ($isCloseToUpgrade) {
            $contextualPrompt .= "\nHIGH-INTENT USER - CLOSING OPPORTUNITY:\n";
            $contextualPrompt .= "- User is showing strong upgrade signals - time to close\n";
            $contextualPrompt .= "- Use assumptive language: 'Ketika Anda upgrade ke Pro...'\n";
            $contextualPrompt .= "- Create urgency: 'Banyak seller yang menunda, padahal setiap hari delay = revenue loss'\n";
            $contextualPrompt .= "- Address final objections: 'Yang masih ragu biasanya soal ROI, tapi data menunjukkan...'\n";
            $contextualPrompt .= "- Use anchoring power: 'Dari Basic 79K, cuma tambah 20K untuk unlimited everything'\n";
            $contextualPrompt .= "- Break down the gap: 'Selisih 20 ribu = harga 1 lunch, tapi benefit selamanya'\n";
            $contextualPrompt .= "- Use social proof: 'Seller sukses yang saya kenal semua Pro member'\n";
            $contextualPrompt .= "- Make it easy: 'Prosesnya simple, dalam 5 menit sudah bisa unlimited AI'\n";
            $contextualPrompt .= "- Create FOMO: 'Slot Pro member terbatas untuk menjaga kualitas eksklusif'\n";
        }

        // Only use real-time data if user specifically needs examples or is confused
        if ($userNeedsExamples) {
            $smartRecommendations = $this->getSmartRecommendations($prompt, $userState['type']);
            $contextualSuggestions = $this->getContextualSuggestions();
            $realTimeData = $this->getRealTimeData($prompt, $userState['type']);
            $productAvailability = $this->checkProductAvailability($prompt, $userState['type']);

            $contextualPrompt .= "\nUSER NEEDS EXAMPLES - PROVIDE SPECIFIC DATA:\n";

            if (!empty($realTimeData)) {
                $contextualPrompt .= "Real-time data available:\n";
                foreach ($realTimeData as $data) {
                    $contextualPrompt .= "- " . $data . "\n";
                }
            }

            if (!empty($smartRecommendations)) {
                $contextualPrompt .= "Product examples:\n";
                foreach ($smartRecommendations as $recommendation) {
                    $contextualPrompt .= "- " . $recommendation . "\n";
                }
            }

            if (!empty($contextualSuggestions)) {
                $contextualPrompt .= "Trending suggestions:\n";
                foreach ($contextualSuggestions as $suggestion) {
                    $contextualPrompt .= "- " . $suggestion . "\n";
                }
            }

            // Add product availability context
            if ($productAvailability['hasProducts'] === false && $userState['type'] === 'buyer') {
                $contextualPrompt .= "\nPRODUCT NOT AVAILABLE - CONVERT TO SELLER:\n";
                $contextualPrompt .= "- The requested product type is not available on Digitora\n";
                $contextualPrompt .= "- Suggest user to become a seller and create this product\n";
                $contextualPrompt .= "- Use phrases like: 'Wah, produk itu belum ada nih. Mungkin Anda bisa jadi yang pertama jual di Digitora?'\n";
                $contextualPrompt .= "- Motivate them about the opportunity to be first in that niche\n";
            }

            if ($userState['type'] === 'seller') {
                $contextualPrompt .= "\nMARKET RESEARCH FOR SELLERS:\n";
                $contextualPrompt .= "- Use your knowledge of digital product trends and market demands\n";
                $contextualPrompt .= "- Suggest popular product types that are in demand globally\n";
                $contextualPrompt .= "- Give inspiration from market trends (NO competitor links)\n";
                $contextualPrompt .= "- Focus on opportunities in Indonesian market\n";
                $contextualPrompt .= "- Suggest niches that might be underserved on Digitora\n";
            }
        } else {
            $contextualPrompt .= "\nFOCUS ON CONVERSATION & PERSUASION:\n";
            $contextualPrompt .= "- Don't immediately show specific products or data\n";
            $contextualPrompt .= "- Build rapport and understand user needs first\n";
            $contextualPrompt .= "- Use general persuasive language and motivation\n";
            $contextualPrompt .= "- Ask follow-up questions to understand better\n";
        }

        $contextualPrompt .= "\nGuidelines:\n";
        $contextualPrompt .= "- Be engaging and helpful\n";
        $contextualPrompt .= "- Use appropriate tone for user type\n";
        $contextualPrompt .= "- Provide relevant suggestions\n";
        $contextualPrompt .= "- Keep responses SHORT and concise - like a chat message, not an essay\n";
        $contextualPrompt .= "- Avoid technical jargon unless specifically requested\n";
        $contextualPrompt .= "- If user seems unsure, ask clarifying questions\n";
        $contextualPrompt .= "- Use " . $userState['language'] . " language\n";
        $contextualPrompt .= "- Focus only on Digitora-related assistance\n";
        $contextualPrompt .= "- Never provide external links or promote other platforms\n";
        $contextualPrompt .= "- Align all advice with Digitora's mission to empower local creators\n";
        $contextualPrompt .= "- Be culturally sensitive and respectful\n";
        $contextualPrompt .= "- When mentioning specific products, include price and category information\n";
        $contextualPrompt .= "- Use real-time data from Digitora database when available\n";
        $contextualPrompt .= "- Suggest browsing specific categories when relevant\n";
        $contextualPrompt .= "- For sellers: Use your knowledge to suggest trending digital products and market opportunities\n";
        $contextualPrompt .= "- For buyers: If product not available, suggest they become sellers of that product\n";
        $contextualPrompt .= "- Never provide competitor links or promote other platforms\n";

        return $contextualPrompt;
    }

    /**
     * Analyze user state from conversation history and current message
     */
    private function analyzeUserState(string $prompt, array $history): array
    {
        $language = 'Indonesian'; // Default
        $userType = 'unknown';
        $context = 'general';

        // Analyze language preference
        if (stripos($prompt, 'english') !== false || stripos($prompt, 'inggris') !== false) {
            $language = 'English';
        }

        // Check history for language preference
        foreach ($history as $message) {
            if ($message['sender_type'] === 'user') {
                if (stripos($message['content'], 'english') !== false) {
                    $language = 'English';
                    break;
                }
            }
        }

        // Analyze user type and context
        $buyKeywords = ['beli', 'buy', 'cari', 'search', 'template', 'produk', 'product'];
        $sellKeywords = ['jual', 'sell', 'kreator', 'creator', 'upload', 'deskripsi', 'description', 'seo'];
        $membershipKeywords = ['membership', 'keanggotaan', 'upgrade', 'tier', 'paket', 'langganan', 'subscription', 'prompt', 'chatbot', 'limit', 'batas'];

        $promptLower = strtolower($prompt);

        foreach ($buyKeywords as $keyword) {
            if (stripos($promptLower, $keyword) !== false) {
                $userType = 'buyer';
                $context = 'buying';
                break;
            }
        }

        foreach ($sellKeywords as $keyword) {
            if (stripos($promptLower, $keyword) !== false) {
                $userType = 'seller';
                $context = 'selling';
                break;
            }
        }

        // Check for membership-related context
        foreach ($membershipKeywords as $keyword) {
            if (stripos($promptLower, $keyword) !== false) {
                $context = 'membership';
                break;
            }
        }

        // Check conversation history for user type
        if ($userType === 'unknown') {
            foreach ($history as $message) {
                if ($message['sender_type'] === 'user') {
                    $contentLower = strtolower($message['content']);
                    foreach ($buyKeywords as $keyword) {
                        if (stripos($contentLower, $keyword) !== false) {
                            $userType = 'buyer';
                            $context = 'buying';
                            break 2;
                        }
                    }
                    foreach ($sellKeywords as $keyword) {
                        if (stripos($contentLower, $keyword) !== false) {
                            $userType = 'seller';
                            $context = 'selling';
                            break 2;
                        }
                    }
                    // Check for membership context in history
                    foreach ($membershipKeywords as $keyword) {
                        if (stripos($contentLower, $keyword) !== false) {
                            $context = 'membership';
                            break;
                        }
                    }
                }
            }
        }

        return [
            'language' => $language,
            'type' => $userType,
            'context' => $context
        ];
    }

    /**
     * Determine the current conversation stage
     */
    private function getConversationStage(array $history): string
    {
        if (empty($history)) {
            return 'language_selection';
        }

        $hasLanguageSelection = false;
        $hasRoleIdentification = false;

        foreach ($history as $message) {
            $content = strtolower($message['content']);

            // Check for language selection
            if (stripos($content, 'bahasa indonesia') !== false ||
                stripos($content, 'english') !== false ||
                stripos($content, 'inggris') !== false) {
                $hasLanguageSelection = true;
            }

            // Check for role identification
            if (stripos($content, 'beli') !== false ||
                stripos($content, 'jual') !== false ||
                stripos($content, 'buy') !== false ||
                stripos($content, 'sell') !== false ||
                stripos($content, 'buyer') !== false ||
                stripos($content, 'seller') !== false) {
                $hasRoleIdentification = true;
            }
        }

        if (!$hasLanguageSelection) {
            return 'language_selection';
        } elseif (!$hasRoleIdentification) {
            return 'role_identification';
        } else {
            return 'assistance';
        }
    }

    /**
     * Check for prohibited content and potential misuse
     */
    private function checkProhibitedContent(string $prompt): array
    {
        $promptLower = strtolower($prompt);

        // Define prohibited keywords and patterns
        $prohibitedKeywords = [
            // Illegal products
            'pirated', 'crack', 'keygen', 'serial', 'nulled', 'warez', 'torrent',
            'bajakan', 'ilegal', 'palsu', 'counterfeit', 'fake',

            // External payment processing
            'paypal', 'stripe', 'bypass midtrans', 'skip payment', 'free download',
            'lewati pembayaran', 'gratis download', 'tanpa bayar',

            // Personal data requests
            'password', 'credit card', 'bank account', 'personal info',
            'kata sandi', 'kartu kredit', 'rekening bank', 'data pribadi',

            // External platforms
            'etsy', 'gumroad', 'fiverr', 'upwork', 'freelancer',
            'shopify', 'amazon', 'ebay', 'tokopedia', 'shopee',

            // Unrelated content
            'porn', 'gambling', 'drugs', 'weapons', 'violence',
            'pornografi', 'judi', 'narkoba', 'senjata', 'kekerasan',

            // Cultural & Religious sensitivity
            'agama', 'religion', 'politik', 'politics', 'pilpres', 'pemilu',
            'allah', 'tuhan', 'jesus', 'buddha', 'hindu', 'kristen', 'islam',
            'gereja', 'masjid', 'pura', 'vihara', 'bible', 'quran', 'alquran',

            // Offensive language (Indonesian)
            'bodoh', 'gila', 'tolol', 'bego', 'idiot', 'stupid', 'bangsat',
            'anjing', 'babi', 'monyet', 'kampret', 'tai', 'shit', 'fuck',
            'damn', 'hell', 'sialan', 'keparat', 'bajingan', 'kontol',

            // Controversial topics (use word boundaries to avoid false positives)
            'rasisme', 'racism', 'diskriminasi', 'discrimination',
            'lgbt', 'komunis', 'communist', 'separatis', 'terorisme', 'terrorism'
        ];

        $isProhibited = false;
        $reason = '';

        foreach ($prohibitedKeywords as $keyword) {
            if (strpos($promptLower, $keyword) !== false) {
                $isProhibited = true;
                $reason = "Contains prohibited keyword: $keyword";

                // Log suspicious request for review
                Log::warning('DigiAI: Prohibited content detected', [
                    'prompt' => $prompt,
                    'keyword' => $keyword,
                    'timestamp' => now(),
                    'ip' => request()->ip() ?? 'unknown'
                ]);

                break;
            }
        }

        return [
            'isProhibited' => $isProhibited,
            'reason' => $reason
        ];
    }

    /**
     * Get contextual suggestions based on time and trends
     */
    private function getContextualSuggestions(): array
    {
        $currentMonth = date('n');
        $suggestions = [];

        // Get real trending products
        $trendingProducts = $this->databaseService->getTrendingProducts(2);

        // Seasonal suggestions
        if (in_array($currentMonth, [1, 2])) { // January-February
            $suggestions[] = "Tahun baru, saatnya upgrade produktivitas! Template planning dan goal-setting sedang trending.";
        } elseif (in_array($currentMonth, [6, 7, 8])) { // June-August (School season)
            $suggestions[] = "Musim sekolah tiba! Template edukasi dan materi pembelajaran sangat dicari.";
        } elseif (in_array($currentMonth, [11, 12])) { // November-December
            $suggestions[] = "Persiapan akhir tahun! Template laporan dan presentasi bisnis lagi populer.";
        }

        // Add real trending products if available
        if (!empty($trendingProducts)) {
            $trendingProduct = $trendingProducts[0];
            $suggestions[] = "Produk trending: '{$trendingProduct['name']}' dari {$trendingProduct['store_name']} - {$trendingProduct['formatted_price']}";
        } else {
            // Fallback to general trending categories
            $trendingCategories = [
                "Template Excel untuk bisnis UMKM",
                "Desain grafis untuk media sosial",
                "Template presentasi profesional",
                "Materi edukasi digital",
                "Tools produktivitas kerja"
            ];
            $suggestions[] = "Kategori trending: " . $trendingCategories[array_rand($trendingCategories)];
        }

        return $suggestions;
    }

    /**
     * Generate smart product recommendations based on user input
     */
    private function getSmartRecommendations(string $userInput, string $userType): array
    {
        $recommendations = [];
        $inputLower = strtolower($userInput);

        if ($userType === 'buyer') {
            // Try to get real products from database based on search terms
            $searchResults = $this->databaseService->searchProducts($userInput, 3);

            if (!empty($searchResults)) {
                foreach ($searchResults as $product) {
                    $recommendations[] = "Ada '{$product['name']}' di kategori {$product['category_name']}, harga {$product['formatted_price']}. Tertarik?";
                }
            } else {
                // Fallback to general recommendations
                if (strpos($inputLower, 'bisnis') !== false || strpos($inputLower, 'business') !== false) {
                    $recommendations[] = "Template business plan dan financial planning";
                    $recommendations[] = "Desain logo dan branding kit";
                }

                if (strpos($inputLower, 'sekolah') !== false || strpos($inputLower, 'belajar') !== false) {
                    $recommendations[] = "Materi pembelajaran interaktif";
                    $recommendations[] = "Template quiz dan worksheet";
                }

                if (strpos($inputLower, 'desain') !== false || strpos($inputLower, 'design') !== false) {
                    $recommendations[] = "Template Instagram dan social media";
                    $recommendations[] = "Mockup dan design assets";
                }
            }
        } elseif ($userType === 'seller') {
            // Get real statistics and tips
            $stats = $this->databaseService->getProductStats();
            $priceRange = $this->databaseService->getPriceRange();

            $recommendations[] = "Gunakan kata kunci populer seperti 'template', 'modern', 'profesional'";
            $recommendations[] = "Upload 3-5 foto produk dengan kualitas tinggi";
            $recommendations[] = "Tulis deskripsi yang menjelaskan manfaat, bukan hanya fitur";
            $recommendations[] = "Harga rata-rata produk di Digitora: {$priceRange['formatted_avg']}";
            $recommendations[] = "Saat ini ada {$stats['total_products']} produk aktif dari {$stats['total_stores']} toko di platform";
        }

        return $recommendations;
    }

    /**
     * Check if user needs encouragement or motivation
     */
    private function needsEncouragement(array $history): bool
    {
        $discourageKeywords = ['sulit', 'susah', 'tidak bisa', 'gagal', 'confused', 'difficult'];

        foreach ($history as $message) {
            if ($message['sender_type'] === 'user') {
                $content = strtolower($message['content']);
                foreach ($discourageKeywords as $keyword) {
                    if (strpos($content, $keyword) !== false) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if user needs specific examples or is confused and needs concrete data
     */
    private function userNeedsExamples(string $prompt, array $history): bool
    {
        $promptLower = strtolower($prompt);

        // Keywords that indicate user wants specific examples
        $exampleKeywords = [
            'contoh', 'example', 'seperti apa', 'what kind', 'misalnya', 'for instance',
            'ada apa saja', 'what are available', 'pilihan apa', 'what options',
            'rekomendasi', 'recommendation', 'suggest', 'saran', 'advice',
            'harga berapa', 'how much', 'price', 'biaya', 'cost',
            'data pasar', 'market data', 'statistik', 'statistics', 'insight',
            'berapa rata-rata', 'average price', 'harga rata-rata', 'market price',
            'trending', 'popular', 'lagi populer', 'yang laku',
            // Membership-specific keywords
            'jelaskan membership', 'explain membership', 'perbedaan tier', 'tier differences',
            'fitur apa saja', 'what features', 'benefit apa', 'what benefits',
            'kenapa upgrade', 'why upgrade', 'worth it', 'layak', 'manfaat upgrade'
        ];

        // Check current prompt
        foreach ($exampleKeywords as $keyword) {
            if (strpos($promptLower, $keyword) !== false) {
                return true;
            }
        }

        // Check if user has been asking general questions for a while (3+ exchanges)
        $userMessageCount = 0;
        foreach ($history as $message) {
            if ($message['sender_type'] === 'user') {
                $userMessageCount++;
            }
        }

        // If user has been chatting for 3+ messages and still asking vague questions
        if ($userMessageCount >= 3) {
            $confusionKeywords = ['bingung', 'confused', 'tidak tahu', "don't know", 'gimana', 'how'];
            foreach ($confusionKeywords as $keyword) {
                if (strpos($promptLower, $keyword) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get real-time data from Digitora database
     */
    private function getRealTimeData(string $prompt, string $userType): array
    {
        $data = [];
        $inputLower = strtolower($prompt);

        // If user is asking about specific products or categories
        if (strpos($inputLower, 'cari') !== false || strpos($inputLower, 'search') !== false ||
            strpos($inputLower, 'template') !== false || strpos($inputLower, 'produk') !== false) {

            // Get categories with product counts
            $categories = $this->databaseService->getCategories();
            if (!empty($categories)) {
                $topCategory = $categories[0];
                $data[] = "Kategori {$topCategory['name']} memiliki {$topCategory['product_count']} produk tersedia";
            }

            // Get price range information
            $priceRange = $this->databaseService->getPriceRange();
            $data[] = "Rentang harga produk: {$priceRange['formatted_min']} - {$priceRange['formatted_max']}";
        }

        // If user is asking about stores or sellers
        if (strpos($inputLower, 'toko') !== false || strpos($inputLower, 'store') !== false ||
            strpos($inputLower, 'seller') !== false || strpos($inputLower, 'penjual') !== false) {

            $stats = $this->databaseService->getProductStats();
            $data[] = "Saat ini ada {$stats['total_stores']} toko aktif dengan {$stats['total_products']} produk";
        }

        // Add user-type specific data
        if ($userType === 'seller') {
            $priceRange = $this->databaseService->getPriceRange();
            $data[] = "Tips: Harga rata-rata yang sukses di platform: {$priceRange['formatted_avg']}";
        }

        return $data;
    }

    /**
     * Check if requested product type is available on Digitora
     */
    private function checkProductAvailability(string $prompt, string $userType): array
    {
        if ($userType !== 'buyer') {
            return ['hasProducts' => true, 'reason' => 'Not a buyer query'];
        }

        // Try to search for products based on user query
        $searchResults = $this->databaseService->searchProducts($prompt, 1);

        return [
            'hasProducts' => !empty($searchResults),
            'searchResults' => $searchResults,
            'reason' => empty($searchResults) ? 'No products found for this query' : 'Products available'
        ];
    }

    /**
     * Get page-specific data based on current page context
     */
    private function getPageSpecificData(array $pageContext): array
    {
        if (empty($pageContext)) {
            return [];
        }

        $pageType = $pageContext['page_type'] ?? 'unknown';
        $specificData = [];

        // Debug: Log page context processing
        Log::info('GeminiService - Processing page context:', [
            'page_type' => $pageType,
            'full_context' => $pageContext
        ]);

        switch ($pageType) {
            case 'product_detail':
                $specificData = $this->getProductDetailData($pageContext);
                break;

            case 'store_detail':
                $specificData = $this->getStoreDetailData($pageContext);
                break;

            case 'category_browse':
                $specificData = $this->getCategoryBrowseData($pageContext);
                break;

            case 'browse_products':
                $specificData = $this->getBrowseProductsData($pageContext);
                break;

            case 'browse_stores':
                $specificData = $this->getBrowseStoresData($pageContext);
                break;

            case 'seller_dashboard':
                $specificData = $this->getSellerDashboardData($pageContext);
                break;

            case 'user_dashboard':
                $specificData = $this->getUserDashboardData($pageContext);
                break;

            default:
                $specificData = ['general' => 'User is browsing Digitora'];
        }

        return [
            'page_type' => $pageType,
            'specific_data' => $specificData
        ];
    }

    /**
     * Get product detail page data
     */
    private function getProductDetailData(array $pageContext): array
    {
        $productSlug = $pageContext['product_slug'] ?? null;
        $storeSlug = $pageContext['store_slug'] ?? null;

        if (!$productSlug || !$storeSlug) {
            Log::warning('GeminiService - Product or store slug not provided in page context', [
                'product_slug' => $productSlug,
                'store_slug' => $storeSlug
            ]);
            return ['error' => 'Product slug or store slug not provided'];
        }

        Log::info('GeminiService - Getting product details for slugs', [
            'product_slug' => $productSlug,
            'store_slug' => $storeSlug
        ]);

        $product = $this->databaseService->getProductDetailsBySlug($storeSlug, $productSlug);
        if (!$product) {
            Log::warning('GeminiService - Product not found for slugs', [
                'product_slug' => $productSlug,
                'store_slug' => $storeSlug
            ]);
            return ['error' => 'Product not found'];
        }

        Log::info('GeminiService - Product details found:', $product);

        return [
            'product_name' => $product['name'],
            'product_price' => $product['formatted_price'],
            'product_category' => $product['category_name'],
            'product_description' => $product['description'],
            'store_name' => $product['store_name'],
            'context_suggestion' => "User is viewing '{$product['name']}' - provide specific information about this product, suggest similar products, or help with purchase decision"
        ];
    }

    /**
     * Get store detail page data
     */
    private function getStoreDetailData(array $pageContext): array
    {
        $storeSlug = $pageContext['store_slug'] ?? null;
        if (!$storeSlug) {
            Log::warning('GeminiService - Store slug not provided in page context');
            return ['error' => 'Store slug not provided'];
        }

        Log::info('GeminiService - Getting store details for slug: ' . $storeSlug);
        $store = $this->databaseService->getStoreDetails($storeSlug);
        if (!$store) {
            Log::warning('GeminiService - Store not found for slug: ' . $storeSlug);
            return ['error' => 'Store not found'];
        }

        Log::info('GeminiService - Store details found:', $store);

        return [
            'store_name' => $store['store_name'],
            'store_description' => $store['about_store'] ?? 'No description available',
            'total_products' => $store['total_products'],
            'store_categories' => $store['categories'] ?? [],
            'context_suggestion' => "User is viewing '{$store['store_name']}' store - provide information about this store, its products, or help navigate the store"
        ];
    }

    /**
     * Get category browse page data
     */
    private function getCategoryBrowseData(array $pageContext): array
    {
        $categorySlug = $pageContext['category_slug'] ?? null;
        if (!$categorySlug) {
            return ['error' => 'Category slug not provided'];
        }

        $category = $this->databaseService->getCategoryDetails($categorySlug);
        if (!$category) {
            return ['error' => 'Category not found'];
        }

        return [
            'category_name' => $category['name'],
            'product_count' => $category['product_count'],
            'subcategories' => $category['subcategories'] ?? [],
            'context_suggestion' => "User is browsing '{$category['name']}' category - suggest products in this category or related categories"
        ];
    }

    /**
     * Get browse products page data
     */
    private function getBrowseProductsData(array $pageContext): array
    {
        $filters = $pageContext['filters'] ?? [];
        $searchQuery = $pageContext['search'] ?? '';

        return [
            'current_filters' => $filters,
            'search_query' => $searchQuery,
            'context_suggestion' => 'User is browsing all products - help them find specific products or suggest popular categories'
        ];
    }

    /**
     * Get browse stores page data
     */
    private function getBrowseStoresData(array $pageContext): array
    {
        $filters = $pageContext['filters'] ?? [];

        return [
            'current_filters' => $filters,
            'context_suggestion' => 'User is browsing stores - help them find specific stores or suggest popular store categories'
        ];
    }

    /**
     * Get seller dashboard data
     */
    private function getSellerDashboardData(array $pageContext): array
    {
        $sellerId = $pageContext['seller_id'] ?? null;
        if (!$sellerId) {
            return ['error' => 'Seller ID not provided'];
        }

        $sellerStats = $this->databaseService->getSellerStats($sellerId);

        return [
            'total_products' => $sellerStats['total_products'] ?? 0,
            'total_revenue' => $sellerStats['formatted_revenue'] ?? 'Rp 0',
            'total_orders' => $sellerStats['total_orders'] ?? 0,
            'context_suggestion' => 'User is in seller dashboard - provide seller-specific assistance, tips for improving sales, or help with product management'
        ];
    }

    /**
     * Get user dashboard data
     */
    private function getUserDashboardData(array $pageContext): array
    {
        $userId = $pageContext['user_id'] ?? null;
        if (!$userId) {
            return ['error' => 'User ID not provided'];
        }

        $userStats = $this->databaseService->getUserStats($userId);

        return [
            'total_purchases' => $userStats['total_purchases'] ?? 0,
            'total_spent' => $userStats['formatted_spent'] ?? 'Rp 0',
            'context_suggestion' => 'User is in their dashboard - help them find new products, manage purchases, or suggest becoming a seller'
        ];
    }

    /**
     * Generate product-specific chatbot response using seller's configured data
     */
    private function generateProductChatbotResponse(string $prompt, $productChatbot, array $history): string
    {
        $product = $productChatbot->product;
        $language = $productChatbot->language === 'en' ? 'English' : 'Indonesian';

        // Build the product-specific context
        $productContext = "You are DigiAI, now acting as a specialized product assistant for '{$product->name}' on Digitora.\n\n";
        $productContext .= "PRODUCT INFORMATION:\n";
        $productContext .= "Product Name: {$product->name}\n";
        $productContext .= "Price: Rp " . number_format($product->price, 0, ',', '.') . "\n";
        $productContext .= "Category: {$product->detailedCategory->name}\n\n";

        // Add seller's configured chatbot data
        $productContext .= "SELLER-PROVIDED PRODUCT DETAILS:\n";
        $productContext .= $productChatbot->getFormattedDataForAi() . "\n\n";

        // Add conversation context
        $userLanguage = $this->detectLanguageFromHistory($history, $prompt);
        $responseLanguage = $userLanguage ?: $language;

        $productContext .= "INSTRUCTIONS:\n";
        $productContext .= "- Respond in {$responseLanguage}\n";
        $productContext .= "- Use the seller-provided information to answer questions about this specific product\n";
        $productContext .= "- Be helpful, friendly, and informative\n";
        $productContext .= "- If asked about something not covered in the product details, politely say you don't have that information\n";
        $productContext .= "- Encourage the user to purchase if they seem interested\n";
        $productContext .= "- If user needs more help, suggest they contact the seller directly\n";
        $productContext .= "- Keep responses concise and chat-like\n";
        $productContext .= "- Focus only on this specific product\n";
        $productContext .= "- Use persuasive but helpful language\n\n";

        if ($productChatbot->contact_info) {
            $productContext .= "SELLER CONTACT INFO (use if user needs direct contact):\n";
            $productContext .= $productChatbot->contact_info . "\n\n";
        }

        $productContext .= "USER QUESTION: {$prompt}\n\n";
        $productContext .= "Respond as DigiAI helping with this specific product. Use the seller's provided information to give accurate, helpful answers.";

        return $productContext;
    }

    /**
     * Detect language preference from conversation history and current prompt
     */
    private function detectLanguageFromHistory(array $history, string $prompt): string
    {
        // Check current prompt first
        if (stripos($prompt, 'english') !== false || $this->isEnglishText($prompt)) {
            return 'English';
        }

        // Check conversation history
        foreach (array_reverse($history) as $message) {
            if ($message['sender_type'] === 'user') {
                if (stripos($message['content'], 'english') !== false || $this->isEnglishText($message['content'])) {
                    return 'English';
                }
            }
        }

        return 'Indonesian'; // Default
    }

    /**
     * Simple heuristic to detect if text is primarily in English
     */
    private function isEnglishText(string $text): bool
    {
        $englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'have', 'has', 'had', 'will', 'would', 'could', 'should', 'can', 'may', 'might', 'must'];
        $words = str_word_count(strtolower($text), 1);
        $englishWordCount = 0;

        foreach ($words as $word) {
            if (in_array($word, $englishWords)) {
                $englishWordCount++;
            }
        }

        return count($words) > 0 && ($englishWordCount / count($words)) > 0.3;
    }

    /**
     * Detect if user is close to upgrading based on conversation signals
     */
    private function isUserCloseToUpgrade(string $prompt, array $history, $membershipData): bool
    {
        $promptLower = strtolower($prompt);

        // High-intent keywords that suggest user is considering upgrade
        $upgradeSignals = [
            // Direct upgrade interest
            'upgrade', 'pro', 'unlimited', 'membership', 'tier', 'paket',

            // Price/cost inquiries
            'harga', 'price', 'cost', 'biaya', 'berapa', 'how much',

            // Comparison signals
            'perbedaan', 'difference', 'compare', 'bandingkan', 'vs',

            // Business growth signals
            'bisnis', 'business', 'penghasilan', 'income', 'revenue', 'profit',
            'scale', 'grow', 'berkembang', 'expand', 'target',

            // Limitation frustration
            'terbatas', 'limited', 'habis', 'limit', 'batas', 'tidak cukup',
            'kurang', 'need more', 'butuh lebih',

            // Competitive pressure
            'kompetitor', 'competitor', 'saingan', 'kalah', 'tertinggal',

            // ROI/Investment mindset
            'investasi', 'investment', 'roi', 'return', 'balik modal',
            'worth it', 'layak', 'menguntungkan'
        ];

        // Check current prompt for upgrade signals
        foreach ($upgradeSignals as $signal) {
            if (strpos($promptLower, $signal) !== false) {
                return true;
            }
        }

        // Check recent conversation history (last 5 messages)
        $recentMessages = array_slice(array_reverse($history), 0, 5);
        foreach ($recentMessages as $message) {
            if ($message['sender_type'] === 'user') {
                $contentLower = strtolower($message['content']);
                foreach ($upgradeSignals as $signal) {
                    if (strpos($contentLower, $signal) !== false) {
                        return true;
                    }
                }
            }
        }

        // Check if user is hitting usage limits (strong upgrade signal)
        if ($membershipData && isset($membershipData['remaining_prompts'])) {
            $remainingPrompts = $membershipData['remaining_prompts'];
            $todayUsage = $membershipData['today_usage'];

            // If user has used 80% or more of their daily limit
            if ($remainingPrompts !== -1 && $todayUsage > 0) {
                $currentTier = $membershipData['current_tier'];
                if ($currentTier && $currentTier['daily_ai_prompts'] !== -1) {
                    $usagePercentage = $todayUsage / $currentTier['daily_ai_prompts'];
                    if ($usagePercentage >= 0.8) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
