<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating product categories...');

        // Define categories structure
        $categories = [
            [
                'name' => 'Productivity Tools',
                'icon' => 'briefcase',
                'description' => 'Tools to boost your productivity and streamline your workflow.',
                'subcategories' => [
                    [
                        'name' => 'Templates',
                        'description' => 'Ready-to-use templates for various purposes.',
                        'legacy_code' => 'template',
                        'detailed_categories' => [
                            ['name' => 'Business Templates', 'description' => 'Templates for business documents and presentations.'],
                            ['name' => 'Resume Templates', 'description' => 'Professional resume and CV templates.'],
                            ['name' => 'Marketing Templates', 'description' => 'Templates for marketing materials and campaigns.'],
                            ['name' => 'Presentation Templates', 'description' => 'Ready-to-use presentation templates.'],
                            ['name' => 'Legal Templates', 'description' => 'Templates for legal documents and contracts.'],
                            ['name' => 'Project Management Templates', 'description' => 'Templates for managing projects and tasks.'],
                        ]
                    ],
                    [
                        'name' => 'Spreadsheets',
                        'description' => 'Pre-built spreadsheets for data management.',
                        'legacy_code' => 'spreadsheet',
                        'detailed_categories' => [
                            ['name' => 'Budget Spreadsheets', 'description' => 'Spreadsheets for personal and business budgeting.'],
                            ['name' => 'Inventory Trackers', 'description' => 'Tools for tracking inventory and stock.'],
                            ['name' => 'Data Analysis Sheets', 'description' => 'Spreadsheets for analyzing data and trends.'],
                            ['name' => 'Financial Spreadsheets', 'description' => 'Spreadsheets for financial planning and analysis.'],
                            ['name' => 'Project Management', 'description' => 'Spreadsheets for project management and tracking.'],
                        ]
                    ],
                    [
                        'name' => 'Planners',
                        'description' => 'Digital planners for organizing tasks and schedules.',
                        'legacy_code' => 'planner',
                        'detailed_categories' => [
                            ['name' => 'Daily Planners', 'description' => 'Planners for daily task management.'],
                            ['name' => 'Weekly Planners', 'description' => 'Planners for weekly scheduling.'],
                            ['name' => 'Monthly Planners', 'description' => 'Tools for monthly planning and scheduling.'],
                            ['name' => 'Goal Trackers', 'description' => 'Tools for setting and tracking goals.'],
                            ['name' => 'Habit Trackers', 'description' => 'Tools for building and tracking habits.'],
                        ]
                    ],
                    [
                        'name' => 'Dashboards',
                        'description' => 'Interactive dashboards for data visualization.',
                        'legacy_code' => 'dashboard',
                        'detailed_categories' => [
                            ['name' => 'Business Dashboards', 'description' => 'Dashboards for business metrics and KPIs.'],
                            ['name' => 'Analytics Dashboards', 'description' => 'Dashboards for data analytics and insights.'],
                            ['name' => 'KPI Trackers', 'description' => 'Tools for tracking key performance indicators.'],
                        ]
                    ],
                    [
                        'name' => 'Worksheets',
                        'description' => 'Worksheets for various purposes.',
                        'legacy_code' => 'worksheet',
                        'detailed_categories' => [
                            ['name' => 'Educational Worksheets', 'description' => 'Worksheets for educational purposes.'],
                            ['name' => 'Business Worksheets', 'description' => 'Worksheets for business planning and analysis.'],
                            ['name' => 'Personal Development', 'description' => 'Worksheets for personal growth and development.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Design Assets',
                'icon' => 'paint-brush',
                'description' => 'Creative resources for designers and artists.',
                'subcategories' => [
                    [
                        'name' => 'Graphics',
                        'description' => 'Visual elements for design projects.',
                        'legacy_code' => 'graphic',
                        'detailed_categories' => [
                            ['name' => 'Social Media Templates', 'description' => 'Graphics for social media posts and ads.'],
                            ['name' => 'Icon Packs', 'description' => 'Collections of icons for UI and design.'],
                            ['name' => 'Mockups', 'description' => 'Mockups for showcasing products and designs.'],
                            ['name' => 'Branding Kits', 'description' => 'Complete branding packages with logos and graphics.'],
                        ]
                    ],
                    [
                        'name' => 'Fonts',
                        'description' => 'Typography resources for creative projects.',
                        'legacy_code' => 'font',
                        'detailed_categories' => [
                            ['name' => 'Serif Fonts', 'description' => 'Elegant serif typefaces.'],
                            ['name' => 'Sans-Serif Fonts', 'description' => 'Modern sans-serif typefaces.'],
                            ['name' => 'Script Fonts', 'description' => 'Handwritten-style fonts.'],
                            ['name' => 'Display Fonts', 'description' => 'Bold and decorative typefaces for headlines.'],
                        ]
                    ],
                    [
                        'name' => 'Illustrations',
                        'description' => 'Hand-drawn or digital illustrations.',
                        'legacy_code' => 'illustration',
                        'detailed_categories' => [
                            ['name' => 'Vector Illustrations', 'description' => 'Scalable vector artwork.'],
                            ['name' => 'Character Designs', 'description' => 'Illustrations of characters and avatars.'],
                            ['name' => 'Backgrounds', 'description' => 'Illustrated backgrounds for projects.'],
                            ['name' => 'Digital Art', 'description' => 'Digital artwork and illustrations.'],
                        ]
                    ],
                    [
                        'name' => 'UI Kits',
                        'description' => 'User interface design resources.',
                        'legacy_code' => 'ui_kit',
                        'detailed_categories' => [
                            ['name' => 'Mobile UI Kits', 'description' => 'UI components for mobile app design.'],
                            ['name' => 'Web UI Kits', 'description' => 'UI components for web design.'],
                            ['name' => 'Dashboard UI Kits', 'description' => 'UI components for dashboard design.'],
                            ['name' => 'Component Libraries', 'description' => 'Reusable UI component libraries.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Development Resources',
                'icon' => 'code',
                'description' => 'Resources for developers and programmers.',
                'subcategories' => [
                    [
                        'name' => 'Code Snippets',
                        'description' => 'Reusable code fragments for developers.',
                        'legacy_code' => 'code',
                        'detailed_categories' => [
                            ['name' => 'JavaScript Snippets', 'description' => 'Code snippets for JavaScript projects.'],
                            ['name' => 'PHP Snippets', 'description' => 'Code snippets for PHP applications.'],
                            ['name' => 'Python Snippets', 'description' => 'Code snippets for Python programming.'],
                            ['name' => 'CSS Snippets', 'description' => 'CSS code snippets and libraries.'],
                            ['name' => 'HTML Templates', 'description' => 'HTML templates and boilerplates.'],
                        ]
                    ],
                    [
                        'name' => 'Plugins & Extensions',
                        'description' => 'Add-ons for software and platforms.',
                        'legacy_code' => 'plugin',
                        'detailed_categories' => [
                            ['name' => 'WordPress Plugins', 'description' => 'Plugins for WordPress sites.'],
                            ['name' => 'Browser Extensions', 'description' => 'Extensions for web browsers.'],
                            ['name' => 'Shopify Apps', 'description' => 'Apps for Shopify stores.'],
                            ['name' => 'Adobe Plugins', 'description' => 'Plugins for Adobe software.'],
                        ]
                    ],
                    [
                        'name' => 'API Templates',
                        'description' => 'Templates and resources for API development.',
                        'legacy_code' => 'api_template',
                        'detailed_categories' => [
                            ['name' => 'REST API Templates', 'description' => 'Templates for REST API development.'],
                            ['name' => 'GraphQL Templates', 'description' => 'Templates for GraphQL API development.'],
                            ['name' => 'API Documentation', 'description' => 'Templates for API documentation.'],
                            ['name' => 'Webhook Setups', 'description' => 'Templates for webhook implementation.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Educational Content',
                'icon' => 'book',
                'description' => 'Learning materials and resources.',
                'subcategories' => [
                    [
                        'name' => 'E-books',
                        'description' => 'Digital books for various topics.',
                        'legacy_code' => 'ebook',
                        'detailed_categories' => [
                            ['name' => 'Fiction E-books', 'description' => 'Digital fiction books and novels.'],
                            ['name' => 'Non-Fiction E-books', 'description' => 'Informative non-fiction books.'],
                            ['name' => 'Technical Guides', 'description' => 'Guides for technical skills and knowledge.'],
                            ['name' => 'Self-Help Books', 'description' => 'Books for personal growth and development.'],
                            ['name' => 'Business Books', 'description' => 'Books on business and entrepreneurship.'],
                        ]
                    ],
                    [
                        'name' => 'Course Materials',
                        'description' => 'Resources for online courses.',
                        'legacy_code' => 'course_material',
                        'detailed_categories' => [
                            ['name' => 'Slide Decks', 'description' => 'Presentation slides for courses.'],
                            ['name' => 'Workbooks', 'description' => 'Interactive workbooks for learners.'],
                            ['name' => 'Lesson Plans', 'description' => 'Structured plans for educators.'],
                            ['name' => 'Teaching Resources', 'description' => 'Resources for teachers and educators.'],
                            ['name' => 'Study Guides', 'description' => 'Guides for studying and exam preparation.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Multimedia',
                'icon' => 'music',
                'description' => 'Audio, video, and interactive media resources.',
                'subcategories' => [
                    [
                        'name' => 'Music & Audio',
                        'description' => 'Music tracks, sound effects, and audio resources.',
                        'legacy_code' => 'audio',
                        'detailed_categories' => [
                            ['name' => 'Background Music', 'description' => 'Music tracks for videos and presentations.'],
                            ['name' => 'Sound Effects', 'description' => 'Sound effects for multimedia projects.'],
                            ['name' => 'Audio Loops', 'description' => 'Loopable audio clips for production.'],
                        ]
                    ],
                    [
                        'name' => 'Video Templates',
                        'description' => 'Editable video templates for creators.',
                        'legacy_code' => 'video',
                        'detailed_categories' => [
                            ['name' => 'Intro Templates', 'description' => 'Video intros for branding.'],
                            ['name' => 'Motion Graphics', 'description' => 'Animated graphics for videos.'],
                            ['name' => 'Slideshow Templates', 'description' => 'Templates for video slideshows.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Software & Apps',
                'icon' => 'desktop',
                'description' => 'Standalone software, scripts, and applications.',
                'subcategories' => [
                    [
                        'name' => 'Web Applications',
                        'description' => 'Web-based applications and tools.',
                        'legacy_code' => 'webapp',
                        'detailed_categories' => [
                            ['name' => 'SaaS Tools', 'description' => 'Software as a Service tools and platforms.'],
                            ['name' => 'Website Builders', 'description' => 'Tools for building websites.'],
                            ['name' => 'E-commerce Platforms', 'description' => 'Platforms for online stores.'],
                        ]
                    ],
                    [
                        'name' => 'Scripts & Bots',
                        'description' => 'Automation scripts and bots.',
                        'legacy_code' => 'script',
                        'detailed_categories' => [
                            ['name' => 'Automation Scripts', 'description' => 'Scripts for automating tasks.'],
                            ['name' => 'Chatbots', 'description' => 'Bots for customer interaction.'],
                            ['name' => 'Data Scrapers', 'description' => 'Tools for scraping data.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Printables',
                'icon' => 'print',
                'description' => 'Printable designs and crafts.',
                'subcategories' => [
                    [
                        'name' => 'Art Prints',
                        'description' => 'Printable artwork and designs.',
                        'legacy_code' => 'artprint',
                        'detailed_categories' => [
                            ['name' => 'Digital Art Prints', 'description' => 'Printable digital artwork.'],
                            ['name' => 'Typography Prints', 'description' => 'Printable typography designs.'],
                            ['name' => 'Photography Prints', 'description' => 'Printable photography art.'],
                        ]
                    ],
                    [
                        'name' => 'Planners & Calendars',
                        'description' => 'Printable planners and calendars.',
                        'legacy_code' => 'plannerprint',
                        'detailed_categories' => [
                            ['name' => 'Monthly Calendars', 'description' => 'Printable monthly calendars.'],
                            ['name' => 'Weekly Planners', 'description' => 'Printable weekly planners.'],
                            ['name' => 'Habit Trackers', 'description' => 'Printable trackers for habits.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Photography',
                'icon' => 'camera',
                'description' => 'Photography resources and assets.',
                'subcategories' => [
                    [
                        'name' => 'Stock Photos',
                        'description' => 'High-quality stock photography.',
                        'legacy_code' => 'stockphoto',
                        'detailed_categories' => [
                            ['name' => 'Nature & Landscape', 'description' => 'Photos of nature and landscapes.'],
                            ['name' => 'Business & Technology', 'description' => 'Photos for business and tech use.'],
                            ['name' => 'People & Lifestyle', 'description' => 'Photos of people and lifestyles.'],
                        ]
                    ],
                    [
                        'name' => 'Lightroom Presets',
                        'description' => 'Presets for photo editing in Lightroom.',
                        'legacy_code' => 'preset',
                        'detailed_categories' => [
                            ['name' => 'Portrait Presets', 'description' => 'Presets for portrait photography.'],
                            ['name' => 'Vintage Presets', 'description' => 'Presets for a vintage look.'],
                            ['name' => 'Travel Presets', 'description' => 'Presets for travel photography.'],
                        ]
                    ],
                ]
            ],
        ];

        // Create the categories
        $this->createCategories($categories);

        $this->command->info('Product categories created successfully!');
    }

    /**
     * Create categories with their subcategories and detailed categories.
     */
    private function createCategories(array $categories): void
    {
        foreach ($categories as $index => $categoryData) {
            $slug = Str::slug($categoryData['name']);

            // Check if the slug already exists
            $counter = 1;
            $originalSlug = $slug;
            while (ProductCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the main category
            $category = ProductCategory::create([
                'name' => $categoryData['name'],
                'slug' => $slug,
                'description' => $categoryData['description'] ?? null,
                'icon' => $categoryData['icon'] ?? null,
                'product_type' => 'digital', // Mark existing categories as digital product categories
                'sort_order' => $index,
                'is_active' => true,
            ]);

            // Create subcategories if they exist
            if (isset($categoryData['subcategories']) && is_array($categoryData['subcategories'])) {
                $this->createSubcategories($categoryData['subcategories'], $category->id);
            }
        }
    }

    /**
     * Create subcategories with their detailed categories.
     */
    private function createSubcategories(array $subcategories, string $categoryId): void
    {
        foreach ($subcategories as $index => $subcategoryData) {
            $slug = Str::slug($subcategoryData['name']);

            // Check if the slug already exists anywhere in the table
            $counter = 1;
            $originalSlug = $slug;
            while (ProductSubcategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the subcategory
            $subcategory = ProductSubcategory::create([
                'category_id' => $categoryId,
                'name' => $subcategoryData['name'],
                'slug' => $slug,
                'description' => $subcategoryData['description'] ?? null,
                'icon' => $subcategoryData['icon'] ?? null,
                'legacy_code' => $subcategoryData['legacy_code'] ?? null,
                'sort_order' => $index,
                'is_active' => true,
            ]);

            // Create detailed categories if they exist
            if (isset($subcategoryData['detailed_categories']) && is_array($subcategoryData['detailed_categories'])) {
                $this->createDetailedCategories($subcategoryData['detailed_categories'], $subcategory->id);
            }
        }
    }

    /**
     * Create detailed categories.
     */
    private function createDetailedCategories(array $detailedCategories, string $subcategoryId): void
    {
        foreach ($detailedCategories as $index => $detailedCategoryData) {
            $slug = Str::slug($detailedCategoryData['name']);

            // Check if the slug already exists anywhere in the table
            $counter = 1;
            $originalSlug = $slug;
            while (ProductDetailedCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the detailed category
            ProductDetailedCategory::create([
                'subcategory_id' => $subcategoryId,
                'name' => $detailedCategoryData['name'],
                'slug' => $slug,
                'description' => $detailedCategoryData['description'] ?? null,
                'sort_order' => $index,
                'is_active' => true,
            ]);
        }
    }
}
