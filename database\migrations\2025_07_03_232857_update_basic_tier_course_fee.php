<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update Basic tier course transaction fee from 10% to 8%
        DB::table('membership_tiers')
            ->where('slug', 'basic')
            ->update(['course_transaction_fee' => 8]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert Basic tier course transaction fee back to 10%
        DB::table('membership_tiers')
            ->where('slug', 'basic')
            ->update(['course_transaction_fee' => 10]);
    }
};
