/* Course Learning Interface - Enhanced Visual Design */
/* Modern, polished design patterns for premium course learning experience */

/* Base Course Learning Styles */
.course-learning-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
}

/* Course Header */
.course-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    padding: 1.25rem 0;
    position: sticky;
    top: 0;
    z-index: 50;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
}

.course-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1.5rem;
    position: relative;
}

.course-header-left {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    flex: 1;
}

.course-header-back {
    color: #64748b;
    text-decoration: none;
    transition: all 0.2s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-header-back:hover {
    color: #334155;
    background: rgba(100, 116, 139, 0.1);
    transform: translateX(-2px);
}

.course-header-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    letter-spacing: -0.025em;
}

.course-header-subtitle {
    font-size: 0.875rem;
    color: #64748b;
    margin: 0.25rem 0 0 0;
    font-weight: 500;
}

.course-difficulty-badge {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
    margin-top: 0.5rem;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
}

.sidebar-toggle:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.4);
}

.sidebar-toggle svg {
    width: 1rem;
    height: 1rem;
    transition: transform 0.3s ease;
}

.sidebar-toggle.active svg {
    transform: rotate(180deg);
}

.lesson-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.75rem;
}

.lesson-type-badge {
    display: inline-block;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.lesson-type-overview {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #475569;
    border: 1px solid #cbd5e1;
}

/* Course Overview Item Styling */
.course-overview-item {
    background: rgba(248, 250, 252, 0.8);
    border-left: 3px solid #e2e8f0;
    font-weight: 600;
}

.course-overview-item:hover {
    background: rgba(241, 245, 249, 0.9);
    transform: translateX(2px);
}

.course-overview-item.active {
    background: rgba(241, 245, 249, 0.9);
    border-left: 3px solid #94a3b8;
    color: #475569;
}

/* Simplified Course Overview Content */
.course-overview-simple {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.course-overview-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 1rem 0;
    line-height: 1.2;
}

.course-overview-description {
    font-size: 1.125rem;
    color: #64748b;
    margin: 0 0 2rem 0;
    line-height: 1.6;
}

.course-learning-list {
    margin: 2rem 0;
}

.course-learning-list h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #334155;
    margin: 0 0 1rem 0;
}

.course-learning-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.course-learning-list li {
    padding: 0.5rem 0;
    color: #475569;
    position: relative;
    padding-left: 1.5rem;
}

.course-learning-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: 600;
}

.course-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 0.75rem;
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.info-item {
    text-align: center;
}

.info-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.info-label {
    font-size: 0.875rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 500;
}

.lesson-type-video {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
}

.lesson-type-lecture {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
}

.lesson-type-pdf,
.lesson-type-document {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
}

.lesson-duration {
    font-size: 0.75rem;
    color: #64748b;
    background: rgba(248, 250, 252, 0.8);
    padding: 0.375rem 0.75rem;
    border-radius: 1rem;
    border: 1px solid rgba(226, 232, 240, 0.5);
    font-weight: 500;
}

.course-header-right {
    display: flex;
    align-items: center;
    gap: 1.25rem;
}

.course-progress-text {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 600;
}

.course-progress-bar {
    width: 10rem;
    height: 0.75rem;
    background: rgba(226, 232, 240, 0.6);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.course-progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 1rem;
    box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
}

/* Course Layout */
.course-layout {
    display: grid;
    grid-template-columns: 320px 1fr;
    max-width: 1280px;
    margin: 0 auto;
    min-height: calc(100vh - 100px);
    gap: 0;
    transition: grid-template-columns 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.course-layout.sidebar-hidden {
    grid-template-columns: 0 1fr;
}

/* Course Sidebar */
.course-sidebar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(226, 232, 240, 0.8);
    height: calc(100vh - 100px);
    overflow-y: auto;
    overflow-x: hidden;
    position: sticky;
    top: 100px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
    width: 320px;
}

.course-sidebar.hidden {
    width: 0;
    min-width: 0;
    border-right: none;
    box-shadow: none;
    overflow: hidden;
}

.course-sidebar-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #334155;
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    font-weight: 700;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.course-section {
    border-bottom: 1px solid rgba(241, 245, 249, 0.8);
    margin-bottom: 0.5rem;
}

.course-section-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    font-weight: 700;
    color: #334155;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.course-curriculum-item {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(241, 245, 249, 0.6);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;
    color: inherit;
    position: relative;
}

.course-curriculum-item:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    text-decoration: none;
    color: inherit;
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.course-curriculum-item.active {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-right: 4px solid #667eea;
    color: #1e40af;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.course-curriculum-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.curriculum-item-content {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 0.75rem;
}

.curriculum-item-icon {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
    transition: transform 0.2s ease;
}

.course-curriculum-item:hover .curriculum-item-icon {
    transform: scale(1.1);
}

.curriculum-item-details {
    flex: 1;
    min-width: 0;
}

.curriculum-item-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #334155;
    margin: 0;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.2s ease;
}

.course-curriculum-item.active .curriculum-item-title {
    color: #1e40af;
    font-weight: 600;
}

.curriculum-item-duration {
    font-size: 0.75rem;
    color: #64748b;
    margin: 0.25rem 0 0 0;
    font-weight: 500;
}

.curriculum-item-status {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
    transition: transform 0.2s ease;
}

.course-curriculum-item:hover .curriculum-item-status {
    transform: scale(1.1);
}

/* Content Type Icons */
.icon-lecture {
    color: #3b82f6;
    filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3));
}
.icon-video {
    color: #ef4444;
    filter: drop-shadow(0 1px 2px rgba(239, 68, 68, 0.3));
}
.icon-pdf {
    color: #059669;
    filter: drop-shadow(0 1px 2px rgba(5, 150, 105, 0.3));
}
.icon-document {
    color: #059669;
    filter: drop-shadow(0 1px 2px rgba(5, 150, 105, 0.3));
}
.icon-overview {
    color: #64748b;
    filter: drop-shadow(0 1px 2px rgba(100, 116, 139, 0.3));
}

/* Main Content Area */
.course-content-area {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    min-height: calc(100vh - 100px);
    overflow-y: auto;
    border-radius: 1rem 0 0 0;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    display: block !important; /* Ensure content area is always visible */
    visibility: visible !important; /* Ensure content area is always visible */
}

/* When sidebar is hidden, content area expands */
.course-layout.sidebar-hidden .course-content-area {
    border-radius: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-left: 0;
    width: 100% !important;
    display: block !important;
    visibility: visible !important;
    grid-column: 1 / -1; /* Take full width of grid */
    background: rgba(255, 255, 255, 0.98) !important; /* Slightly more opaque when expanded */
    border: 2px solid rgba(102, 126, 234, 0.1); /* Subtle border to indicate expanded state */
}

.course-content-padding {
    padding: 2.5rem;
    transition: padding 0.3s ease;
}

/* Enhanced padding when sidebar is hidden for better use of space */
.course-layout.sidebar-hidden .course-content-padding {
    padding: 3rem 4rem;
}

.course-content-header {
    margin-bottom: 2.5rem;
}

.course-content-title {
    font-size: 2rem;
    font-weight: 800;
    color: #1e293b;
    margin: 0 0 0.75rem 0;
    line-height: 1.2;
    letter-spacing: -0.025em;
}

.course-content-description {
    color: #64748b;
    margin: 0 0 1.5rem 0;
    line-height: 1.7;
    font-size: 1.125rem;
}

.course-download-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    border-radius: 0.75rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(5, 150, 105, 0.3);
}

.course-download-button:hover {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px -1px rgba(5, 150, 105, 0.4);
}

/* Video Container */
.course-video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    background: #000000;
    border-radius: 0.75rem;
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.course-video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 0.75rem;
}

/* Enhanced video container when sidebar is hidden */
.course-layout.sidebar-hidden .course-video-container {
    padding-bottom: 50%; /* Slightly different aspect ratio for wider view */
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.course-video-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 16rem;
    background-color: #f3f4f6;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

.course-video-placeholder-content {
    text-align: center;
}

.course-video-placeholder-icon {
    width: 3rem;
    height: 3rem;
    color: #9ca3af;
    margin: 0 auto 0.5rem;
}

.course-video-placeholder-text {
    color: #6b7280;
    font-size: 0.875rem;
}

.course-video-link {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: inline-block;
}

.course-video-link:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* Lecture Content */
.course-lecture-content {
    line-height: 1.7;
    color: #374151;
    margin-bottom: 2rem;
}

.course-lecture-content h1,
.course-lecture-content h2,
.course-lecture-content h3,
.course-lecture-content h4,
.course-lecture-content h5,
.course-lecture-content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #111827;
    font-weight: 600;
}

.course-lecture-content h1 { font-size: 1.875rem; }
.course-lecture-content h2 { font-size: 1.5rem; }
.course-lecture-content h3 { font-size: 1.25rem; }

.course-lecture-content p {
    margin-bottom: 1rem;
}

.course-lecture-content ul,
.course-lecture-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.course-lecture-content li {
    margin-bottom: 0.5rem;
}

.course-lecture-content blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6b7280;
}

.course-lecture-content code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875em;
}

.course-lecture-content pre {
    background-color: #f3f4f6;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
}

/* Document/PDF Placeholder */
.course-document-placeholder {
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 3rem;
    text-align: center;
    margin-bottom: 2rem;
}

.course-document-icon {
    width: 4rem;
    height: 4rem;
    color: #9ca3af;
    margin: 0 auto 1rem;
}

.course-document-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin: 0 0 0.5rem 0;
}

.course-document-text {
    color: #6b7280;
    margin: 0 0 1rem 0;
}

.course-document-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
}

/* Navigation */
.course-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 0;
    border-top: 1px solid #e5e7eb;
    margin-top: 2rem;
}

.course-nav-button {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background-color: #3b82f6;
    color: white;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
}

.course-nav-button:hover {
    background-color: #2563eb;
    color: white;
    text-decoration: none;
}

.course-nav-button.secondary {
    background-color: #6b7280;
}

.course-nav-button.secondary:hover {
    background-color: #4b5563;
}

.course-nav-button:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
}

.course-nav-icon {
    width: 1rem;
    height: 1rem;
}

.course-nav-icon.left {
    margin-right: 0.5rem;
}

.course-nav-icon.right {
    margin-left: 0.5rem;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .course-layout {
        grid-template-columns: 1fr;
        position: relative;
    }

    .course-sidebar {
        position: fixed;
        top: 100px;
        left: 0;
        width: 320px;
        height: calc(100vh - 100px);
        z-index: 40;
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .course-sidebar.show {
        transform: translateX(0);
        box-shadow: 4px 0 16px rgba(0, 0, 0, 0.15);
    }

    .course-sidebar.hidden {
        transform: translateX(-100%);
        width: 320px; /* Keep original width for mobile */
    }

    .course-content-padding {
        padding: 1.5rem;
    }

    .course-content-title {
        font-size: 1.75rem;
    }

    .sidebar-toggle {
        display: block;
    }

    /* Overlay for mobile sidebar */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 35;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
}

@media (min-width: 1025px) {
    .sidebar-toggle {
        display: block;
    }

    .sidebar-overlay {
        display: none;
    }

    /* Desktop specific sidebar behavior */
    .course-sidebar.hidden {
        width: 0;
        min-width: 0;
        border-right: none;
        box-shadow: none;
        overflow: hidden;
    }
}

@media (max-width: 768px) {
    .course-header-content {
        padding: 0 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .course-header-left {
        flex: 1;
        min-width: 0;
    }

    .course-header-right {
        gap: 0.75rem;
        flex-shrink: 0;
    }

    .course-progress-bar {
        width: 6rem;
    }

    .course-content-padding {
        padding: 1.25rem;
    }

    .course-layout.sidebar-hidden .course-content-padding {
        padding: 1.5rem 2rem;
    }

    .course-navigation {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .course-nav-button {
        justify-content: center;
    }

    .sidebar-toggle {
        padding: 0.625rem;
    }

    .sidebar-toggle svg {
        width: 1rem;
        height: 1rem;
    }
}

@media (max-width: 640px) {
    .course-header-content {
        padding: 0 0.75rem;
    }

    .course-header-title {
        font-size: 1.125rem;
    }

    .course-progress-text {
        display: none;
    }

    .course-overview-simple {
        padding: 1rem 0;
    }

    .course-overview-title {
        font-size: 1.75rem;
    }

    .course-overview-description {
        font-size: 1rem;
    }

    .course-info-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1rem;
        padding: 1rem;
    }

    .info-number {
        font-size: 1.25rem;
    }

    .course-progress-bar {
        width: 4rem;
    }

    .course-content-padding {
        padding: 1rem;
    }

    .course-layout.sidebar-hidden .course-content-padding {
        padding: 1.25rem 1.5rem;
    }

    .course-content-title {
        font-size: 1.5rem;
    }

    .course-layout.sidebar-hidden .course-video-container {
        padding-bottom: 56.25%; /* Keep standard aspect ratio on mobile */
    }
}

/* Enhanced Welcome Screen */
.course-welcome {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 32rem;
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 1rem;
    margin: 2rem;
}

.course-welcome-content {
    max-width: 32rem;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-welcome-icon {
    width: 4rem;
    height: 4rem;
    color: white;
    margin: 0 auto 1.5rem;
    filter: drop-shadow(0 4px 6px rgba(102, 126, 234, 0.3));
}

.course-welcome-title {
    font-size: 2rem;
    font-weight: 800;
    color: #1e293b;
    margin: 0 0 1rem 0;
    letter-spacing: -0.025em;
    line-height: 1.2;
}

.course-welcome-description {
    color: #64748b;
    margin: 0 0 2rem 0;
    line-height: 1.7;
    font-size: 1.125rem;
}

.course-welcome-instruction {
    font-size: 0.875rem;
    color: #64748b;
    margin: 2rem 0 0 0;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 0.75rem;
    border: 1px solid rgba(226, 232, 240, 0.6);
    font-weight: 500;
}

.digitora-brand-accent {
    margin-bottom: 1.5rem;
}

.digitora-brand-accent .course-welcome-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    padding: 1rem;
    color: white;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

.course-learning-objectives {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-left: 4px solid #667eea;
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0 0.75rem 0.75rem 0;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.course-learning-objectives h3 {
    font-size: 1.125rem;
    font-weight: 700;
    color: #334155;
    margin: 0 0 1rem 0;
    letter-spacing: -0.025em;
}

.course-learning-objectives ul {
    margin: 0;
    padding-left: 1.5rem;
    list-style-type: none;
}

.course-learning-objectives li {
    position: relative;
    margin-bottom: 0.75rem;
    color: #475569;
    font-size: 0.875rem;
    line-height: 1.6;
    font-weight: 500;
}

.course-learning-objectives li:before {
    content: "✓";
    position: absolute;
    left: -1.5rem;
    color: #059669;
    font-weight: bold;
    font-size: 1rem;
    top: 0.125rem;
}

.course-stats {
    display: flex;
    gap: 2.5rem;
    margin: 2rem 0;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
    border-radius: 1rem;
    justify-content: center;
    border: 1px solid rgba(226, 232, 240, 0.6);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item {
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-top: 0.5rem;
    font-weight: 600;
}

/* Loading States */
.course-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.course-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility */
.course-curriculum-item:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
}

.course-nav-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .course-sidebar,
    .course-navigation,
    .course-header {
        display: none;
    }
    
    .course-layout {
        grid-template-columns: 1fr;
    }
    
    .course-content-area {
        background: white;
    }
}
