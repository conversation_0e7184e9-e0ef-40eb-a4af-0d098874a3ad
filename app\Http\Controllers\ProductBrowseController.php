<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Cart;

class ProductBrowseController extends Controller
{
    /**
     * Display a listing of products with search and filtering.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = Product::where('status', 'active')
            ->whereHas('seller', function($q) {
                $q->whereHas('sellerApplication', function($q2) {
                    $q2->where('status', 'approved');
                });
            })
            ->with(['seller', 'seller.sellerApplication']);

        // Search functionality
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($category = $request->input('category')) {
            $query->where('category', $category);
        }

        // Sort functionality
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Paginate results
        $products = $query->paginate(10);

        // Get all categories for the filter dropdown
        $categories = Product::where('status', 'active')
            ->select('category')
            ->distinct()
            ->get()
            ->pluck('category');

        // Get cart item count for the user dashboard
        $cartItemCount = 0;
        if (Auth::check()) {
            $cart = Cart::where('user_id', Auth::id())->first();
            if ($cart) {
                $cartItemCount = $cart->items->count();
            }
        } else {
            // For guest users, get cart from session
            $sessionId = session()->get('cart_session_id');
            if ($sessionId) {
                $cart = Cart::where('session_id', $sessionId)->first();
                if ($cart) {
                    $cartItemCount = $cart->items->count();
                }
            }
        }

        return view('users.browse', compact('products', 'categories', 'cartItemCount'));
    }
}
