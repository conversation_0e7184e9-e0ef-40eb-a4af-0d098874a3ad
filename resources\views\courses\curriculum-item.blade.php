@extends('layouts.browse')

@section('title', $item->title . ' - ' . $course->title)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/course-learning.css') }}?v={{ time() }}">
@endpush

@push('scripts')
<script src="{{ asset('dev-js/course-learning.js') }}?v={{ time() }}" defer></script>
@endpush

@section('content')
<div class="course-learning-container">
    <!-- Course Header -->
    <div class="course-header">
        <div class="course-header-content">
            <div class="course-header-left">
                <a href="{{ route('browse.courses.access', $course) }}" class="course-header-back">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div>
                    <h1 class="course-header-title">{{ $course->title }}</h1>
                    <p class="course-header-subtitle">{{ $item->title }}</p>
                    <div class="lesson-meta">
                        <span class="lesson-type-badge lesson-type-{{ $item->type }}">
                            {{ ucfirst($item->type) }}
                        </span>
                        @if($item->estimated_duration)
                            <span class="lesson-duration">{{ $item->estimated_duration }} min</span>
                        @endif
                    </div>
                </div>
            </div>
            <div class="course-header-right">
                <span class="course-progress-text">Progress: {{ number_format($progress->progress_percentage, 0) }}%</span>
                <div class="course-progress-bar">
                    <div class="course-progress-fill" style="width: {{ $progress->progress_percentage }}%"></div>
                </div>

                <!-- Sidebar Toggle Button -->
                <button class="sidebar-toggle active" aria-label="Hide course content sidebar">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <div class="course-layout">
        <!-- Course Sidebar -->
        <div class="course-sidebar">
            <div class="course-sidebar-header">
                Course Content
            </div>

            <!-- Course Overview Section -->
            <div class="course-section">
                <a href="{{ route('browse.courses.access', $course) }}"
                   class="course-curriculum-item course-overview-item">
                    <div class="curriculum-item-content">
                        <div class="curriculum-item-details">
                            <p class="curriculum-item-title">Overview</p>
                        </div>
                    </div>
                </a>
            </div>

            @foreach($course->sections as $sectionIndex => $section)
                <div class="course-section">
                    <div class="course-section-header">
                        {{ $sectionIndex + 1 }}. {{ $section->title }}
                    </div>
                    @foreach($section->curriculumItems as $curriculumIndex => $curriculumItem)
                        <a href="{{ route('browse.courses.curriculum-item', [$course, $curriculumItem]) }}"
                           class="course-curriculum-item {{ $curriculumItem->id === $item->id ? 'active' : '' }} {{ $progress->isItemCompleted($curriculumItem->id) ? 'visited' : '' }}">
                            <div class="curriculum-item-content">
                                @if($curriculumItem->type === 'lecture')
                                    <svg class="curriculum-item-icon icon-lecture" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                @elseif($curriculumItem->type === 'video')
                                    <svg class="curriculum-item-icon icon-video" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                @else
                                    <svg class="curriculum-item-icon icon-document" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                @endif
                                <div class="curriculum-item-details">
                                    <p class="curriculum-item-title">
                                        {{ $sectionIndex + 1 }}.{{ $curriculumIndex + 1 }} {{ $curriculumItem->title }}
                                    </p>
                                    @if($curriculumItem->estimated_duration)
                                        <p class="curriculum-item-duration">{{ $curriculumItem->estimated_duration }} min</p>
                                    @endif
                                </div>
                            </div>
                            @if($curriculumItem->id === $item->id)
                                <svg class="curriculum-item-status" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </a>
                    @endforeach
                </div>
            @endforeach
        </div>

        <!-- Main Content Area -->
        <div class="course-content-area">
            <div class="course-content-padding">
                <!-- Content Header -->
                <div class="course-content-header">
                    <h1 class="course-content-title">{{ $item->title }}</h1>
                    @if($item->description)
                        <p class="course-content-description">{{ $item->description }}</p>
                    @endif

                    <!-- Download button for PDF/Document types -->
                    @if(in_array($item->type, ['pdf', 'document']) && $item->file_path)
                        <a href="{{ route('user.browse.courses.curriculum-item.download', [$course, $item]) }}"
                           class="course-download-button">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Download {{ ucfirst($item->type) }}
                        </a>
                    @endif
                </div>

                <!-- Content Display -->
                <div>
                    @if($item->type === 'video')
                        @if(str_contains($item->content, 'youtube.com') || str_contains($item->content, 'youtu.be'))
                            @php
                                $videoId = '';
                                if (str_contains($item->content, 'youtube.com/watch?v=')) {
                                    $videoId = substr($item->content, strpos($item->content, 'v=') + 2);
                                    $videoId = substr($videoId, 0, strpos($videoId, '&') ?: strlen($videoId));
                                } elseif (str_contains($item->content, 'youtu.be/')) {
                                    $videoId = substr($item->content, strrpos($item->content, '/') + 1);
                                }
                            @endphp
                            @if($videoId)
                                <div class="course-video-container">
                                    <iframe src="https://www.youtube.com/embed/{{ $videoId }}"
                                            frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            allowfullscreen>
                                    </iframe>
                                </div>
                            @else
                                <div class="course-video-placeholder">
                                    <div class="course-video-placeholder-content">
                                        <p class="course-video-placeholder-text">Invalid video URL</p>
                                    </div>
                                </div>
                            @endif
                        @else
                            <div class="course-video-placeholder">
                                <div class="course-video-placeholder-content">
                                    <svg class="course-video-placeholder-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    <p class="course-video-placeholder-text">Video content</p>
                                    @if($item->content)
                                        <a href="{{ $item->content }}" target="_blank" class="course-video-link">
                                            Open Video Link
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endif
                    @elseif($item->type === 'lecture')
                        <div class="course-lecture-content">
                            {!! nl2br(e($item->content)) !!}
                        </div>
                    @elseif(in_array($item->type, ['pdf', 'document']))
                        <div class="course-document-placeholder">
                            <svg class="course-document-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="course-document-title">{{ ucfirst($item->type) }} Resource</h3>
                            <p class="course-document-text">Click the download button above to access this resource.</p>
                            @if($item->content)
                                <p class="course-document-description">{{ $item->content }}</p>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Navigation -->
                <div class="course-navigation">
                    <div>
                        @if($previousItem)
                            <a href="{{ route('browse.courses.curriculum-item', [$course, $previousItem]) }}"
                               class="course-nav-button secondary">
                                <svg class="course-nav-icon left" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                Previous
                            </a>
                        @else
                            <a href="{{ route('browse.courses.access', $course) }}"
                               class="course-nav-button secondary">
                                <svg class="course-nav-icon left" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                Course Overview
                            </a>
                        @endif
                    </div>

                    <div class="flex gap-3">
                        @if($nextItem)
                            <a href="{{ route('browse.courses.curriculum-item', [$course, $nextItem]) }}"
                               class="course-nav-button">
                                Next
                                <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        @else
                            @if($progress->is_completed)
                                <div class="flex gap-3">
                                    <button class="course-nav-button" id="complete-course-btn" style="background-color: #10b981; cursor: default;" disabled>
                                        Course Completed!
                                        <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </button>
                                    <a href="{{ route('browse.courses') }}" class="course-nav-button secondary">
                                        Find Other Courses
                                        <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </a>
                                </div>
                            @else
                                <button onclick="markCourseComplete()" class="course-nav-button" id="complete-course-btn">
                                    Course Complete
                                    <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </button>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Course completion functionality
function markCourseComplete() {
    const button = document.getElementById('complete-course-btn');

    // Check if course is already completed
    if (button.disabled || button.style.backgroundColor === 'rgb(16, 185, 129)') {
        return;
    }

    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = 'Completing...';
    button.disabled = true;

    fetch(`/api/course/{{ $course->slug }}/complete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update progress display
            const progressText = document.querySelector('.course-progress-text');
            const progressFill = document.querySelector('.course-progress-fill');

            if (progressText) progressText.textContent = 'Progress: 100%';
            if (progressFill) progressFill.style.width = '100%';

            // Mark all curriculum items as completed
            document.querySelectorAll('.course-curriculum-item:not(.course-overview-item)').forEach(item => {
                item.classList.add('visited');
            });

            // Show success message and make permanent
            button.innerHTML = 'Course Completed!';
            button.style.backgroundColor = '#10b981';
            button.style.cursor = 'default';
            button.disabled = true;

            // Add "Find Other Courses" button next to the completed button
            const buttonContainer = button.parentElement;
            if (!buttonContainer.querySelector('.find-courses-btn')) {
                const findCoursesBtn = document.createElement('a');
                findCoursesBtn.href = '/browse/courses';
                findCoursesBtn.className = 'course-nav-button secondary find-courses-btn';
                findCoursesBtn.style.marginLeft = '12px';
                findCoursesBtn.innerHTML = `
                    Find Other Courses
                    <svg class="course-nav-icon right" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                `;
                buttonContainer.appendChild(findCoursesBtn);
            }
        } else {
            throw new Error(data.message || 'Failed to complete course');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalText;
        button.disabled = false;
        alert('Failed to mark course as complete. Please try again.');
    });
}

// Add CSRF token to page if not already present
if (!document.querySelector('meta[name="csrf-token"]')) {
    const meta = document.createElement('meta');
    meta.name = 'csrf-token';
    meta.content = '{{ csrf_token() }}';
    document.head.appendChild(meta);
}
</script>

@endsection
