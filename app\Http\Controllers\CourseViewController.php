<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseCurriculumItem;
use App\Models\Order;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\UserCourseProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CourseViewController extends Controller
{
    /**
     * Display a listing of all active courses
     */
    public function index(Request $request)
    {
        $query = Course::where('status', 'active')
            ->with(['seller.sellerApplication', 'category', 'subcategory', 'detailedCategory'])
            ->withCount(['sections', 'curriculumItems']);

        // Search functionality
        if ($search = $request->input('search')) {
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($category = $request->input('category')) {
            $query->where('detailed_category_id', $category);
        }

        // Difficulty filter
        if ($difficulty = $request->input('difficulty')) {
            $query->where('difficulty_level', $difficulty);
        }

        // Price range filter
        if ($minPrice = $request->input('min_price')) {
            $query->where('price', '>=', $minPrice * 1000); // Convert to IDR
        }
        if ($maxPrice = $request->input('max_price')) {
            $query->where('price', '<=', $maxPrice * 1000); // Convert to IDR
        }

        // Sorting
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'popular':
                $query->orderBy('students_count', 'desc');
                break;
            case 'rating':
                $query->orderBy('average_rating', 'desc');
                break;
            default:
                $query->orderBy('published_at', 'desc');
        }

        $courses = $query->paginate(12);

        // Get categories for filtering
        $categories = \App\Models\ProductDetailedCategory::where('is_active', true)
            ->whereHas('courses', function($q) {
                $q->where('status', 'active');
            })
            ->with(['subcategory.category'])
            ->orderBy('name')
            ->get();

        return view('users.browse-courses', compact('courses', 'categories'));
    }

    /**
     * Display the specified course details
     */
    public function show(Course $course)
    {
        // Only show active courses
        if ($course->status !== 'active') {
            abort(404);
        }

        $course->load([
            'seller.sellerApplication',
            'category',
            'subcategory',
            'detailedCategory',
            'sections' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'sections.curriculumItems' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }
        ]);

        // Check if user has purchased this course
        $hasPurchased = false;
        $isInCart = false;
        if (Auth::check()) {
            $order = Order::where('buyer_id', Auth::id())
                ->where('course_id', $course->id)
                ->where('status', 'success')
                ->first();

            $hasPurchased = (bool) $order;

            // Check if course is already in cart (only if not purchased)
            if (!$hasPurchased) {
                $cart = Cart::where('user_id', Auth::id())->first();
                if ($cart) {
                    $cartItem = CartItem::where('cart_id', $cart->id)
                        ->where('course_id', $course->id)
                        ->first();
                    $isInCart = (bool) $cartItem;
                }
            }
        }

        // Get preview curriculum items (available without purchase)
        $previewItems = CourseCurriculumItem::where('course_id', $course->id)
            ->where('is_preview', true)
            ->where('is_active', true)
            ->with(['section'])
            ->orderBy('sort_order')
            ->get();

        return view('courses.show', compact('course', 'hasPurchased', 'isInCart', 'previewItems'));
    }

    /**
     * Access course content (for purchased courses)
     */
    public function access(Course $course)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to access course content.');
        }

        // Check if user has purchased this course
        $order = Order::where('buyer_id', Auth::id())
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if (!$order) {
            return redirect()->route('browse.courses.access-denied', $course);
        }

        $course->load([
            'seller.sellerApplication',
            'sections' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'sections.curriculumItems' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }
        ]);

        // Get or create progress record for this user and course
        $progress = UserCourseProgress::getOrCreate(Auth::id(), $course->id);

        // Get first curriculum item for navigation
        $firstItem = null;
        foreach ($course->sections as $section) {
            if ($section->curriculumItems->count() > 0) {
                $firstItem = $section->curriculumItems->first();
                break;
            }
        }

        return view('courses.access', compact('course', 'order', 'firstItem', 'progress'));
    }

    /**
     * View specific course curriculum item
     */
    public function curriculumItem(Course $course, CourseCurriculumItem $item)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to access course content.');
        }

        // Check if curriculum item belongs to the course
        if ($item->course_id !== $course->id) {
            abort(404);
        }

        // Check if curriculum item is preview or user has purchased
        $canAccess = $item->is_preview;

        if (!$canAccess) {
            $order = Order::where('buyer_id', Auth::id())
                ->where('course_id', $course->id)
                ->where('status', 'success')
                ->first();

            $canAccess = (bool) $order;
        }

        if (!$canAccess) {
            return redirect()->route('browse.courses.access-denied', $course);
        }

        $item->load(['section']);

        // Get course structure for navigation
        $course->load([
            'sections' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'sections.curriculumItems' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }
        ]);

        // Get next and previous curriculum items for navigation
        $allItems = collect();
        foreach ($course->sections as $section) {
            $allItems = $allItems->merge($section->curriculumItems);
        }

        $currentIndex = $allItems->search(function($currItem) use ($item) {
            return $currItem->id === $item->id;
        });

        $previousItem = $currentIndex > 0 ? $allItems[$currentIndex - 1] : null;
        $nextItem = $currentIndex < $allItems->count() - 1 ? $allItems[$currentIndex + 1] : null;

        // Get or create progress record for this user and course
        $progress = UserCourseProgress::getOrCreate(Auth::id(), $course->id);

        // Mark this item as completed if user has purchased the course
        if (!$item->is_preview) {
            $progress->markItemCompleted($item->id);
        }

        return view('courses.curriculum-item', compact('course', 'item', 'previousItem', 'nextItem', 'progress'));
    }

    /**
     * Download course curriculum item file
     */
    public function downloadCurriculumItem(Course $course, CourseCurriculumItem $item)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to access course content.');
        }

        // Check if curriculum item belongs to the course
        if ($item->course_id !== $course->id) {
            abort(404);
        }

        // Check if curriculum item is preview or user has purchased
        $canAccess = $item->is_preview;

        if (!$canAccess) {
            $order = Order::where('buyer_id', Auth::id())
                ->where('course_id', $course->id)
                ->where('status', 'success')
                ->first();

            $canAccess = (bool) $order;
        }

        if (!$canAccess) {
            return redirect()->route('browse.courses.access-denied', $course);
        }

        // Check if curriculum item has a downloadable file
        if (!in_array($item->type, ['pdf', 'document']) || !$item->file_path) {
            return redirect()->back()->with('error', 'This curriculum item does not have a downloadable file.');
        }

        $filePath = storage_path('app/public/' . $item->file_path);

        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'File not found.');
        }

        return response()->download($filePath, $item->title);
    }

    /**
     * Show course purchase page (Buy Now functionality)
     */
    public function buyNow(Course $course)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to purchase this course.');
        }

        // Check if course is active
        if ($course->status !== 'active') {
            abort(404);
        }

        // Check if user has already purchased this course
        $existingOrder = Order::where('buyer_id', Auth::id())
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if ($existingOrder) {
            return redirect()->route('browse.courses.access-granted', $course);
        }

        // Check for pending orders
        $pendingOrder = Order::where('buyer_id', Auth::id())
            ->where('course_id', $course->id)
            ->where('status', 'pending')
            ->latest()
            ->first();

        $course->load(['seller.sellerApplication']);

        return view('courses.buy', compact('course', 'pendingOrder'));
    }

    /**
     * Process direct course purchase
     */
    public function processPurchase(Request $request, Course $course)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }

        // Check if course is active
        if ($course->status !== 'active') {
            return response()->json(['error' => 'Course not available'], 404);
        }

        // Check if user has already purchased this course
        $existingOrder = Order::where('buyer_id', Auth::id())
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if ($existingOrder) {
            return response()->json(['error' => 'Course already purchased'], 400);
        }

        // Configure Midtrans
        \Midtrans\Config::$serverKey = config('services.midtrans.serverKey');
        \Midtrans\Config::$isProduction = config('services.midtrans.isProduction');
        \Midtrans\Config::$isSanitized = config('services.midtrans.isSanitized');
        \Midtrans\Config::$is3ds = config('services.midtrans.is3ds');

        $response = [];

        try {
            DB::transaction(function () use ($course, &$response) {
                $buyer = Auth::user();

                // Calculate price (use discount price if available)
                $price = $course->discount_price > 0 ? $course->discount_price : $course->price;

                // Ensure price is an integer for Midtrans compatibility
                $price = (int)$price;

                // Make sure price is at least 1000 IDR
                if ($price < 1000) {
                    $price = 1000;
                }

                $orderId = Str::random(20) . time();

                // Create order
                $order = Order::create([
                    'order_id' => $orderId,
                    'buyer_id' => $buyer->id,
                    'course_id' => $course->id,
                    'amount' => $price,
                    'status' => 'pending',
                ]);

                // Prepare Midtrans payload
                $payload = [
                    'transaction_details' => [
                        'order_id' => $orderId,
                        'gross_amount' => $price,
                    ],
                    'customer_details' => [
                        'first_name' => $buyer->name,
                        'email' => $buyer->email,
                    ],
                    'item_details' => [
                        [
                            'id' => $course->id,
                            'price' => $course->price,
                            'quantity' => 1,
                            'name' => $course->title,
                        ]
                    ]
                ];

                // Add discount item if applicable
                if ($course->discount_price > 0 && $course->discount_price < $course->price) {
                    $payload['item_details'][] = [
                        'id' => 'discount',
                        'price' => -($course->price - $course->discount_price),
                        'quantity' => 1,
                        'name' => 'Discount'
                    ];
                }

                // Get Snap token
                $snapToken = \Midtrans\Snap::getSnapToken($payload);

                // Update order with snap token
                $order->update(['snap_token' => $snapToken]);

                $response['snap_token'] = $snapToken;
                $response['order_id'] = $orderId;
            });

            return response()->json($response);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Payment processing failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Show access denied page for courses
     */
    public function accessDenied(Course $course)
    {
        // Only show active courses
        if ($course->status !== 'active') {
            abort(404);
        }

        $course->load(['seller.sellerApplication']);

        return view('courses.access-denied', compact('course'));
    }

    /**
     * Show access granted page for courses
     */
    public function accessGranted(Course $course)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to access course content.');
        }

        // Only show active courses
        if ($course->status !== 'active') {
            abort(404);
        }

        // Check if user has purchased this course
        $order = Order::where('buyer_id', Auth::id())
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if (!$order) {
            return redirect()->route('browse.courses.access-denied', $course);
        }

        $course->load([
            'seller.sellerApplication',
            'sections' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'sections.curriculumItems' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }
        ]);

        return view('courses.access-granted', compact('course', 'order'));
    }

    /**
     * API: Update course progress
     */
    public function updateProgress(Request $request, Course $course)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Check if user has purchased this course
        $order = Order::where('buyer_id', Auth::id())
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if (!$order) {
            return response()->json(['error' => 'Course not purchased'], 403);
        }

        $request->validate([
            'curriculum_item_id' => 'required|exists:course_curriculum_items,id',
            'completed' => 'boolean'
        ]);

        $progress = UserCourseProgress::getOrCreate(Auth::id(), $course->id);

        if ($request->completed) {
            $progress->markItemCompleted($request->curriculum_item_id);
        }

        return response()->json([
            'success' => true,
            'progress_percentage' => $progress->progress_percentage,
            'is_completed' => $progress->is_completed,
            'completed_items' => $progress->completed_items
        ]);
    }

    /**
     * API: Get course progress
     */
    public function getProgress(Course $course)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $progress = UserCourseProgress::getOrCreate(Auth::id(), $course->id);

        return response()->json([
            'progress_percentage' => $progress->progress_percentage,
            'is_completed' => $progress->is_completed,
            'completed_items' => $progress->completed_items ?? [],
            'current_curriculum_item_id' => $progress->current_curriculum_item_id,
            'last_accessed_at' => $progress->last_accessed_at
        ]);
    }

    /**
     * API: Mark course as complete
     */
    public function markComplete(Course $course)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Check if user has purchased this course
        $order = Order::where('buyer_id', Auth::id())
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if (!$order) {
            return response()->json(['error' => 'Course not purchased'], 403);
        }

        $progress = UserCourseProgress::getOrCreate(Auth::id(), $course->id);

        // Mark all curriculum items as completed
        $allItems = $course->activeCurriculumItems()->pluck('id')->toArray();
        $progress->completed_items = $allItems;
        $progress->progress_percentage = 100.00;
        $progress->is_completed = true;
        $progress->completed_at = now();
        $progress->save();

        return response()->json([
            'success' => true,
            'message' => 'Course marked as complete',
            'progress_percentage' => 100.00,
            'is_completed' => true
        ]);
    }

    // Removed getSubchapterContent method - no longer needed in simplified structure
}
