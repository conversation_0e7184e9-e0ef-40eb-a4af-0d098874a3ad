document.addEventListener("DOMContentLoaded", function () {
    // Load Sortable.js library dynamically
    function loadSortableJS() {
        return new Promise((resolve, reject) => {
            if (window.Sortable) {
                resolve(window.Sortable);
                return;
            }

            const script = document.createElement("script");
            script.src =
                "https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js";
            script.onload = () => resolve(window.Sortable);
            script.onerror = () =>
                reject(new Error("Failed to load Sortable.js"));
            document.head.appendChild(script);
        });
    }

    // Initialize sortable after the library is loaded
    loadSortableJS()
        .then((Sortable) => {
            // Initialize Sortable for existing images if the element exists
            const sortableImagesContainer =
                document.getElementById("sortable-images");
            if (sortableImagesContainer) {
                new Sortable(sortableImagesContainer, {
                    animation: 150,
                    handle: ".handle",
                    ghostClass: "bg-gray-100",
                    onEnd: function () {
                        // Update the hidden inputs for image order
                        updateImageOrder();
                        // Update the primary image (first image)
                        updatePrimaryImageAfterSort();
                    },
                });
            }

            // Initialize Sortable for newly uploaded images
            const imagesList = document.getElementById("images-list");
            if (imagesList) {
                new Sortable(imagesList, {
                    animation: 150,
                    ghostClass: "bg-gray-100",
                    onEnd: function () {
                        // Reorder the selectedImages array
                        reorderSelectedImages();
                        // Update the UI to reflect the new order
                        updateImagesPreview(false);
                    },
                });
            }
        })
        .catch((error) => {
            console.error("Error loading Sortable.js:", error);
        });

    // Function to update image order hidden inputs
    function updateImageOrder() {
        const imageItems = document.querySelectorAll(
            "#sortable-images .existing-image-item"
        );
        const orderInputs = document.querySelectorAll(
            'input[name="image_order[]"]'
        );

        // Clear existing inputs
        orderInputs.forEach((input) => input.remove());

        // Create new inputs with updated order
        imageItems.forEach((item, index) => {
            const imageId = item.getAttribute("data-image-id");
            const orderInput = document.createElement("input");
            orderInput.type = "hidden";
            orderInput.name = "image_order[]";
            orderInput.value = imageId;
            item.appendChild(orderInput);

            // Update sort order attribute
            item.setAttribute("data-sort-order", index);
        });
    }

    // Function to update primary image after sorting
    function updatePrimaryImageAfterSort() {
        const imageItems = document.querySelectorAll(
            "#sortable-images .existing-image-item"
        );
        if (imageItems.length === 0) return;

        // Create hidden input for primary image if it doesn't exist
        let primaryImageInput = document.querySelector(
            'input[name="primary_image_id"]'
        );
        if (!primaryImageInput) {
            primaryImageInput = document.createElement("input");
            primaryImageInput.type = "hidden";
            primaryImageInput.name = "primary_image_id";
            document
                .getElementById("product-form")
                .appendChild(primaryImageInput);
        }

        // Set the first image as primary
        const firstImageId = imageItems[0].getAttribute("data-image-id");
        primaryImageInput.value = firstImageId;

        // Update UI to show first image as primary
        imageItems.forEach((item, index) => {
            const label = item.querySelector(".image-label");
            const setPrimaryBtn = item.querySelector(".set-primary-image");

            if (index === 0) {
                label.textContent = "📌 Cover Image";
                if (setPrimaryBtn) setPrimaryBtn.classList.add("hidden");
            } else {
                label.textContent = "Product Image";
                if (setPrimaryBtn) setPrimaryBtn.classList.remove("hidden");
            }
        });
    }

    // Set primary image button click handler for existing images
    document.querySelectorAll(".set-primary-image").forEach((button) => {
        button.addEventListener("click", function () {
            const imageItem = this.closest(".existing-image-item");
            if (!imageItem) return;

            // Move this item to the top of the list
            const parent = imageItem.parentNode;
            if (!parent) return;

            parent.prepend(imageItem);

            // Update order and primary image
            updateImageOrder();
            updatePrimaryImageAfterSort();
        });
    });
    // Function to reorder selectedImages array based on the DOM order
    function reorderSelectedImages() {
        if (!imagesList) return;

        // Get all image items in the current DOM order
        const imageItems = imagesList.querySelectorAll(
            ".flex.items-center.justify-between"
        );
        if (imageItems.length === 0) return;

        // Create a new array to hold the reordered images
        const reorderedImages = [];

        // For each image item in the DOM, find the corresponding image in selectedImages
        imageItems.forEach((item) => {
            // Get the image name from the DOM
            const imageName = item
                .querySelector(".text-sm.font-medium.text-gray-900")
                .textContent.replace("📌 ", "")
                .replace(" (Cover)", "");

            // Find the image in selectedImages
            const imageIndex = selectedImages.findIndex(
                (img) => img.name === imageName
            );
            if (imageIndex !== -1) {
                reorderedImages.push(selectedImages[imageIndex]);
            }
        });

        // Replace selectedImages with the reordered array
        if (reorderedImages.length === selectedImages.length) {
            selectedImages = reorderedImages;

            // Update the input files
            updateImagesInput();
        }
    }

    // Multiple Images Upload with Drag-and-Drop and Size Validation
    const imagesInput = document.getElementById("images");
    const imagesPreview = document.getElementById("images-preview");
    const imagesList = document.getElementById("images-list");
    const imagesUploadArea = document.querySelector(
        ".upload-area:has(#images)"
    );
    let selectedImages = [];

    // Count existing images (for edit page)
    function countExistingImages() {
        // Count images in the sortable-images container (edit page)
        const existingImageItems = document.querySelectorAll(
            ".existing-image-item"
        );
        // Also check for single image (legacy format)
        const singleExistingImage = document.querySelector(".existing-image");

        return existingImageItems.length + (singleExistingImage ? 1 : 0);
    }

    if (imagesUploadArea) {
        const imagesError = document.createElement("p");
        imagesError.className =
            "mt-1 text-sm font-bold text-red-600 images-error";
        imagesUploadArea.appendChild(imagesError);

        document.querySelectorAll(".choose-images").forEach((button) => {
            button.addEventListener("click", () => imagesInput.click());
        });

        if (imagesInput) {
            // Clear the images input on page load to ensure we start fresh
            imagesInput.value = "";
            selectedImages = [];

            imagesInput.addEventListener("change", function () {
                imagesError.textContent = "";
                if (this.files.length > 0) {
                    const maxSizeMB = 2; // 2MB limit
                    const maxImagesAllowed = 10; // Maximum 10 images
                    let allFilesValid = true;
                    const newImages = Array.from(this.files);

                    // Reset the file input value to clear the selection
                    this.value = "";

                    // Check if adding these images would exceed the 10 image limit
                    const existingImagesCount = countExistingImages();
                    const currentImageCount =
                        selectedImages.length + existingImagesCount;
                    const remainingSlots = maxImagesAllowed - currentImageCount;

                    if (remainingSlots <= 0) {
                        const message = `⚠️ LIMIT REACHED: You can only upload a maximum of ${maxImagesAllowed} images per product. This product already has ${currentImageCount} images.`;
                        imagesError.textContent = message;
                        showGlobalError(
                            `You can only upload a maximum of ${maxImagesAllowed} images per product. This product already has ${currentImageCount} images.`
                        );
                        return;
                    }

                    // Check if the new images would exceed the limit
                    if (
                        currentImageCount + newImages.length >
                        maxImagesAllowed
                    ) {
                        const message = `⚠️ LIMIT EXCEEDED: Adding ${newImages.length} images would exceed the limit of ${maxImagesAllowed} images per product. Only ${remainingSlots} more image(s) can be added.`;
                        imagesError.textContent = message;
                        showGlobalError(
                            `Adding ${newImages.length} images would exceed the limit of ${maxImagesAllowed} images per product. Only ${remainingSlots} more image(s) can be added.`
                        );
                        return;
                    }

                    // Validate image sizes
                    newImages.forEach((file) => {
                        const fileSizeMB = file.size / 1024 / 1024;
                        if (fileSizeMB > maxSizeMB) {
                            const message = `⚠️ SIZE LIMIT: Image "${
                                file.name
                            }" size (${fileSizeMB.toFixed(
                                2
                            )} MB) exceeds the ${maxSizeMB} MB limit.`;
                            imagesError.textContent = message;
                            showGlobalError(
                                `Image "${
                                    file.name
                                }" size (${fileSizeMB.toFixed(
                                    2
                                )} MB) exceeds the ${maxSizeMB} MB limit.`
                            );
                            allFilesValid = false;
                        }
                    });

                    if (allFilesValid) {
                        // Only add images if we're under the limit
                        const imagesToAdd = newImages.slice(0, remainingSlots);
                        selectedImages = [...selectedImages, ...imagesToAdd];
                        updateImagesPreview();
                        updateImagesInput();
                    }
                }
            });

            // Drag-and-Drop for Images
            imagesUploadArea.addEventListener("dragover", (e) => {
                e.preventDefault();
                imagesUploadArea.classList.add(
                    "border-indigo-500",
                    "bg-gray-50"
                );
            });

            imagesUploadArea.addEventListener("dragleave", () => {
                imagesUploadArea.classList.remove(
                    "border-indigo-500",
                    "bg-gray-50"
                );
            });

            imagesUploadArea.addEventListener("drop", (e) => {
                e.preventDefault();
                imagesUploadArea.classList.remove(
                    "border-indigo-500",
                    "bg-gray-50"
                );
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    imagesError.textContent = "";
                    const maxSizeMB = 2; // 2MB limit
                    const maxImagesAllowed = 10; // Maximum 10 images
                    let allFilesValid = true;
                    const newImages = Array.from(files);

                    // Check if adding these images would exceed the 10 image limit
                    const existingImagesCount = countExistingImages();
                    const currentImageCount =
                        selectedImages.length + existingImagesCount;
                    const remainingSlots = maxImagesAllowed - currentImageCount;

                    if (remainingSlots <= 0) {
                        const message = `⚠️ LIMIT REACHED: You can only upload a maximum of ${maxImagesAllowed} images per product. This product already has ${currentImageCount} images.`;
                        imagesError.textContent = message;
                        showGlobalError(
                            `You can only upload a maximum of ${maxImagesAllowed} images per product. This product already has ${currentImageCount} images.`
                        );
                        return;
                    }

                    // Check if the new images would exceed the limit
                    if (
                        currentImageCount + newImages.length >
                        maxImagesAllowed
                    ) {
                        const message = `⚠️ LIMIT EXCEEDED: Adding ${newImages.length} images would exceed the limit of ${maxImagesAllowed} images per product. Only ${remainingSlots} more image(s) can be added.`;
                        imagesError.textContent = message;
                        showGlobalError(
                            `Adding ${newImages.length} images would exceed the limit of ${maxImagesAllowed} images per product. Only ${remainingSlots} more image(s) can be added.`
                        );
                        return;
                    }

                    // Validate image sizes
                    newImages.forEach((file) => {
                        const fileSizeMB = file.size / 1024 / 1024;
                        if (fileSizeMB > maxSizeMB) {
                            const message = `⚠️ SIZE LIMIT: Image "${
                                file.name
                            }" size (${fileSizeMB.toFixed(
                                2
                            )} MB) exceeds the ${maxSizeMB} MB limit.`;
                            imagesError.textContent = message;
                            showGlobalError(
                                `Image "${
                                    file.name
                                }" size (${fileSizeMB.toFixed(
                                    2
                                )} MB) exceeds the ${maxSizeMB} MB limit.`
                            );
                            allFilesValid = false;
                        }
                    });

                    if (allFilesValid) {
                        // Only add images if we're under the limit
                        const imagesToAdd = newImages.slice(0, remainingSlots);
                        selectedImages = [...selectedImages, ...imagesToAdd];
                        updateImagesPreview();
                        updateImagesInput();
                    }
                }
            });
        }

        function updateImagesPreview(clearList = true) {
            if (!imagesList) return;

            // Only clear the list if requested (default is true)
            if (clearList) {
                imagesList.innerHTML = "";
            }

            if (selectedImages.length > 0) {
                if (imagesPreview) imagesPreview.classList.remove("hidden");

                // If we're not clearing the list, we're reordering, so don't recreate items
                if (clearList) {
                    selectedImages.forEach((image, index) => {
                        const imageItem = document.createElement("div");
                        imageItem.className =
                            "flex items-center justify-between rounded-lg border border-gray-200 p-3";
                        imageItem.setAttribute("data-image-name", image.name);

                        // Add drag handle
                        const dragHandle = document.createElement("div");
                        dragHandle.className = "cursor-move handle mr-2";
                        dragHandle.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-400">
                                <line x1="21" y1="10" x2="3" y2="10"></line>
                                <line x1="21" y1="6" x2="3" y2="6"></line>
                                <line x1="21" y1="14" x2="3" y2="14"></line>
                                <line x1="21" y1="18" x2="3" y2="18"></line>
                            </svg>
                        `;

                        const imageInfo = document.createElement("div");
                        imageInfo.className = "flex items-center gap-3";

                        // Create image preview
                        const img = document.createElement("img");
                        img.src = URL.createObjectURL(image);
                        img.alt = image.name;
                        img.className =
                            "h-12 w-12 rounded-lg object-cover shadow-sm";
                        img.onload = () => URL.revokeObjectURL(img.src);

                        const textDiv = document.createElement("div");
                        textDiv.innerHTML = `
                            <p class="text-sm font-medium text-gray-900">${
                                index === 0
                                    ? "📌 " + image.name + " (Cover)"
                                    : image.name
                            }</p>
                            <p class="text-xs text-gray-500">${(
                                image.size /
                                1024 /
                                1024
                            ).toFixed(2)} MB</p>
                        `;

                        imageInfo.appendChild(img);
                        imageInfo.appendChild(textDiv);

                        // Create button container
                        const buttonContainer = document.createElement("div");
                        buttonContainer.className = "flex items-center gap-2";

                        // Add set primary button for non-primary images
                        if (index !== 0) {
                            const setPrimaryButton =
                                document.createElement("button");
                            setPrimaryButton.type = "button";
                            setPrimaryButton.className =
                                "set-primary-button rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-indigo-600 transition-colors";
                            setPrimaryButton.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                    <path d="M12 2L15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2z"></path>
                                </svg>
                            `;
                            setPrimaryButton.onclick = (e) => {
                                e.stopPropagation();
                                // Move this image to the beginning of the array
                                const imageToMove = selectedImages.splice(
                                    index,
                                    1
                                )[0];
                                selectedImages.unshift(imageToMove);
                                // Update the UI
                                updateImagesPreview();
                                updateImagesInput();
                            };
                            buttonContainer.appendChild(setPrimaryButton);
                        }

                        const removeButton = document.createElement("button");
                        removeButton.type = "button";
                        removeButton.className =
                            "rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-red-600 transition-colors";
                        removeButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                <line x1="10" y1="11" x2="10" y2="17"></line>
                                <line x1="14" y1="11" x2="14" y2="17"></line>
                            </svg>
                        `;
                        removeButton.onclick = (e) => {
                            e.stopPropagation();
                            selectedImages.splice(index, 1);
                            updateImagesPreview();
                            updateImagesInput();
                            if (imagesError) imagesError.textContent = "";
                        };

                        buttonContainer.appendChild(removeButton);

                        // Assemble the item
                        imageItem.appendChild(dragHandle);
                        imageItem.appendChild(imageInfo);
                        imageItem.appendChild(buttonContainer);
                        imagesList.appendChild(imageItem);
                    });
                } else {
                    // Just update the labels to reflect the current order
                    const imageItems =
                        imagesList.querySelectorAll("[data-image-name]");
                    imageItems.forEach((item, index) => {
                        const label = item.querySelector(
                            ".text-sm.font-medium.text-gray-900"
                        );
                        const imageName = item.getAttribute("data-image-name");
                        const setPrimaryButton = item.querySelector(
                            ".set-primary-button"
                        );

                        if (index === 0) {
                            label.textContent = `📌 ${imageName} (Cover)`;
                            if (setPrimaryButton)
                                setPrimaryButton.style.display = "none";
                        } else {
                            label.textContent = imageName;
                            if (setPrimaryButton)
                                setPrimaryButton.style.display = "";
                        }
                    });
                }
            } else {
                if (imagesPreview) imagesPreview.classList.add("hidden");
            }
        }

        function updateImagesInput() {
            // Update the file input
            const dt = new DataTransfer();
            const maxImagesAllowed = 10; // Maximum 10 images
            const existingImagesCount = countExistingImages();

            // Calculate how many new images we can add
            const remainingSlots = Math.max(
                0,
                maxImagesAllowed - existingImagesCount
            );

            // Only add up to the remaining slots
            const imagesToAdd = selectedImages.slice(0, remainingSlots);

            // Show warning if images were truncated
            if (selectedImages.length > remainingSlots && imagesError) {
                const message = `⚠️ LIMIT ENFORCED: Only the first ${remainingSlots} new images will be uploaded. ${
                    selectedImages.length - remainingSlots
                } image(s) were removed. Each product can have a maximum of ${maxImagesAllowed} images.`;
                imagesError.textContent = message;
                showGlobalError(
                    `Only the first ${remainingSlots} new images will be uploaded. ${
                        selectedImages.length - remainingSlots
                    } image(s) were removed. Each product can have a maximum of ${maxImagesAllowed} images.`
                );
                // Update the selectedImages array to match what's actually being used
                selectedImages = imagesToAdd;
                // Update the preview to reflect the changes
                updateImagesPreview();
            }

            imagesToAdd.forEach((image) => dt.items.add(image));
            imagesInput.files = dt.files;

            // Add hidden inputs for image order
            const form = document.getElementById("product-form");
            if (!form) return;

            // Remove any existing image order inputs
            document
                .querySelectorAll('input[name="image_order[]"]')
                .forEach((input) => {
                    input.remove();
                });

            // Add new image order inputs
            selectedImages.forEach((image) => {
                const orderInput = document.createElement("input");
                orderInput.type = "hidden";
                orderInput.name = "image_order[]";
                orderInput.value = image.name;
                form.appendChild(orderInput);
            });
        }
    }
    // Discount Price Toggle
    const hasDiscountCheckbox = document.getElementById("has_discount");
    const discountPriceContainer = document.getElementById(
        "discount-price-container"
    );
    const discountPriceInput = document.getElementById("discount_price");
    const priceInput = document.getElementById("price");

    if (hasDiscountCheckbox) {
        hasDiscountCheckbox.addEventListener("change", function () {
            discountPriceContainer.classList.toggle("hidden", !this.checked);
            if (!this.checked) {
                discountPriceInput.value = "";
            }
        });

        discountPriceInput.addEventListener("input", function () {
            const price = parseFloat(priceInput.value) || 0;
            const discountPrice = parseFloat(this.value) || 0;
            if (discountPrice >= price && price > 0) {
                this.setCustomValidity(
                    "Discount price must be less than the original price."
                );
            } else {
                this.setCustomValidity("");
            }
        });

        priceInput.addEventListener("input", function () {
            const price = parseFloat(this.value) || 0;
            const discountPrice = parseFloat(discountPriceInput.value) || 0;
            if (discountPrice >= price && price > 0 && discountPrice > 0) {
                discountPriceInput.setCustomValidity(
                    "Discount price must be less than the original price."
                );
            } else {
                discountPriceInput.setCustomValidity("");
            }
        });
    }

    // Image Upload with Drag-and-Drop and Size Validation
    const imageInput = document.getElementById("image");
    const imagePreview = document.getElementById("image-preview");
    const imageList = document.getElementById("image-list");
    const imageUploadArea = document.querySelector(".upload-area:has(#image)");
    let imageError = null;

    if (imageUploadArea) {
        imageError = document.createElement("p");
        imageError.className =
            "mt-1 text-sm font-bold text-red-600 image-error";
        imageUploadArea.appendChild(imageError);
    }
    let selectedImage = null;

    document.querySelectorAll(".choose-image").forEach((button) => {
        button.addEventListener("click", () => {
            if (imageInput) imageInput.click();
        });
    });

    if (imageInput) {
        imageInput.addEventListener("change", function () {
            if (imageError) imageError.textContent = "";
            if (this.files.length > 0) {
                const file = this.files[0];
                const maxSizeMB = 2; // 2MB limit
                const fileSizeMB = file.size / 1024 / 1024;
                if (fileSizeMB > maxSizeMB) {
                    if (imageError) {
                        const message = `⚠️ SIZE LIMIT: Image size (${fileSizeMB.toFixed(
                            2
                        )} MB) exceeds the ${maxSizeMB} MB limit.`;
                        imageError.textContent = message;
                        showGlobalError(
                            `Image size (${fileSizeMB.toFixed(
                                2
                            )} MB) exceeds the ${maxSizeMB} MB limit.`
                        );
                    }
                    this.value = "";
                    selectedImage = null;
                    if (imagePreview) imagePreview.classList.add("hidden");
                } else {
                    selectedImage = file;
                    updateImagePreview();
                }
            }
        });

        // Drag-and-Drop for Image
        if (imageUploadArea) {
            imageUploadArea.addEventListener("dragover", (e) => {
                e.preventDefault();
                imageUploadArea.classList.add(
                    "border-indigo-500",
                    "bg-gray-50"
                );
            });

            imageUploadArea.addEventListener("dragleave", () => {
                imageUploadArea.classList.remove(
                    "border-indigo-500",
                    "bg-gray-50"
                );
            });

            imageUploadArea.addEventListener("drop", (e) => {
                e.preventDefault();
                imageUploadArea.classList.remove(
                    "border-indigo-500",
                    "bg-gray-50"
                );
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    imageInput.files = files;
                    const file = files[0];
                    const maxSizeMB = 2; // 2MB limit
                    const fileSizeMB = file.size / 1024 / 1024;
                    if (fileSizeMB > maxSizeMB) {
                        if (imageError) {
                            const message = `⚠️ SIZE LIMIT: Image size (${fileSizeMB.toFixed(
                                2
                            )} MB) exceeds the ${maxSizeMB} MB limit.`;
                            imageError.textContent = message;
                            showGlobalError(
                                `Image size (${fileSizeMB.toFixed(
                                    2
                                )} MB) exceeds the ${maxSizeMB} MB limit.`
                            );
                        }
                        imageInput.value = "";
                        selectedImage = null;
                        if (imagePreview) imagePreview.classList.add("hidden");
                    } else {
                        selectedImage = file;
                        updateImagePreview();
                    }
                }
            });
        }
    }

    function updateImagePreview() {
        if (!imageList) return;
        imageList.innerHTML = "";

        if (selectedImage) {
            if (imagePreview) imagePreview.classList.remove("hidden");

            const imageItem = document.createElement("div");
            imageItem.className =
                "flex items-center justify-between rounded-lg border border-gray-200 p-3";

            const imageInfo = document.createElement("div");
            imageInfo.className = "flex items-center gap-3";

            const imageIcon = document.createElement("div");
            imageIcon.className =
                "flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100";
            imageIcon.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-500">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                    <polyline points="21 15 16 10 5 21"></polyline>
                </svg>
            `;

            const imageDetails = document.createElement("div");
            imageDetails.innerHTML = `
                <p class="text-sm font-medium text-gray-900">${
                    selectedImage.name
                }</p>
                <p class="text-xs text-gray-500">${(
                    selectedImage.size /
                    1024 /
                    1024
                ).toFixed(2)} MB</p>
            `;

            imageInfo.appendChild(imageIcon);
            imageInfo.appendChild(imageDetails);

            const removeButton = document.createElement("button");
            removeButton.type = "button";
            removeButton.className =
                "rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors";
            removeButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    <line x1="10" y1="11" x2="10" y2="17"></line>
                    <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
            `;
            removeButton.onclick = () => {
                selectedImage = null;
                if (imageInput) imageInput.value = "";
                if (imageError) imageError.textContent = "";
                if (imagePreview) imagePreview.classList.add("hidden");
            };

            imageItem.appendChild(imageInfo);
            imageItem.appendChild(removeButton);
            imageList.appendChild(imageItem);
        } else {
            if (imagePreview) imagePreview.classList.add("hidden");
        }
    }

    // Remove Existing Image
    document.querySelectorAll(".remove-image").forEach((button) => {
        button.addEventListener("click", function () {
            const imageItem = this.closest(".existing-image");
            if (!imageItem) return;

            const removeInput = imageItem.querySelector(".remove-image-input");
            if (removeInput) removeInput.value = "1";
            imageItem.style.display = "none";
        });
    });

    // Remove Existing Images
    document.querySelectorAll(".remove-existing-image").forEach((button) => {
        button.addEventListener("click", function () {
            const imageId = this.getAttribute("data-image-id");
            const imageItem = this.closest(".existing-image-item");
            if (!imageItem) return;

            const productForm = document.getElementById("product-form");
            if (!productForm) return;

            // Check if we're trying to remove the last image
            const visibleImageItems = document.querySelectorAll(
                ".existing-image-item:not([style*='display: none'])"
            );
            if (visibleImageItems.length <= 1) {
                // If this is the last image and no new images are selected, show a warning
                const selectedImages =
                    document.querySelectorAll("#images-list > div").length;
                if (selectedImages === 0) {
                    if (
                        !confirm(
                            "Are you sure you want to remove the last image? You'll need to upload at least one new image before saving."
                        )
                    ) {
                        return; // User cancelled the removal
                    }
                }
            }

            const removeInput = document.createElement("input");
            removeInput.type = "hidden";
            removeInput.name = "remove_images[]";
            removeInput.value = imageId;
            productForm.appendChild(removeInput);
            imageItem.style.display = "none";

            // Check if this was the primary image and update UI accordingly
            if (
                imageItem
                    .querySelector(".text-sm.font-medium.text-gray-900")
                    .textContent.includes("Cover Image")
            ) {
                // Find the next visible image and mark it as primary if available
                const nextImage = document.querySelector(
                    ".existing-image-item:not([style*='display: none'])"
                );
                if (nextImage) {
                    const label = nextImage.querySelector(
                        ".text-sm.font-medium.text-gray-900"
                    );
                    if (label) label.textContent = "📌 Cover Image";
                }
            }
        });
    });

    // File Upload with Drag-and-Drop and Size Validation
    const fileInput = document.getElementById("files");
    const filePreview = document.getElementById("file-preview");
    const fileList = document.getElementById("file-list");
    const fileUploadArea = document.querySelector(".upload-area:has(#files)");
    let fileError = null;

    // Count existing files (for edit page)
    function countExistingFiles() {
        // Count files in the existing-file-list container (edit page)
        const existingFileItems = document.querySelectorAll(
            ".existing-file-item"
        );
        // Only count files that aren't marked for removal
        let visibleCount = 0;
        existingFileItems.forEach((item) => {
            if (item.style.display !== "none") {
                visibleCount++;
            }
        });
        return visibleCount;
    }

    if (fileUploadArea) {
        fileError = document.createElement("p");
        fileError.className = "mt-1 text-sm font-bold text-red-600 file-error";
        fileUploadArea.appendChild(fileError);
    }
    let selectedFiles = [];

    document.querySelectorAll(".choose-files").forEach((button) => {
        button.addEventListener("click", () => {
            if (fileInput) fileInput.click();
        });
    });

    if (fileInput) {
        // Clear the file input on page load to ensure we start fresh
        fileInput.value = "";
        selectedFiles = [];

        fileInput.addEventListener("change", function () {
            if (fileError) fileError.textContent = "";
            const newFiles = Array.from(this.files);
            const maxSizeMB = 20; // 20MB limit
            const maxFilesAllowed = 5; // Maximum 5 files
            let allFilesValid = true;

            // Reset the file input value to clear the selection
            this.value = "";

            // Check if adding these files would exceed the 5 file limit
            const existingFilesCount = countExistingFiles();
            const currentFileCount = selectedFiles.length + existingFilesCount;
            const remainingSlots = maxFilesAllowed - currentFileCount;

            if (remainingSlots <= 0) {
                if (fileError) {
                    const message = `⚠️ LIMIT REACHED: You can only upload a maximum of ${maxFilesAllowed} files per product. This product already has ${currentFileCount} files.`;
                    fileError.textContent = message;
                    showGlobalError(
                        `You can only upload a maximum of ${maxFilesAllowed} files per product. This product already has ${currentFileCount} files.`
                    );
                }
                return;
            }

            // Check if the new files would exceed the limit
            if (currentFileCount + newFiles.length > maxFilesAllowed) {
                if (fileError) {
                    const message = `⚠️ LIMIT EXCEEDED: Adding ${newFiles.length} files would exceed the limit of ${maxFilesAllowed} files per product. Only ${remainingSlots} more file(s) can be added.`;
                    fileError.textContent = message;
                    showGlobalError(
                        `Adding ${newFiles.length} files would exceed the limit of ${maxFilesAllowed} files per product. Only ${remainingSlots} more file(s) can be added.`
                    );
                }
                return;
            }

            // Validate file sizes
            newFiles.forEach((file) => {
                const fileSizeMB = file.size / 1024 / 1024;
                if (fileSizeMB > maxSizeMB) {
                    if (fileError) {
                        const message = `⚠️ SIZE LIMIT: File "${
                            file.name
                        }" size (${fileSizeMB.toFixed(
                            2
                        )} MB) exceeds the ${maxSizeMB} MB limit.`;
                        fileError.textContent = message;
                        showGlobalError(
                            `File "${file.name}" size (${fileSizeMB.toFixed(
                                2
                            )} MB) exceeds the ${maxSizeMB} MB limit.`
                        );
                    }
                    allFilesValid = false;
                }
            });

            if (allFilesValid) {
                // Only add files if we're under the limit
                const filesToAdd = newFiles.slice(0, remainingSlots);
                selectedFiles = [...selectedFiles, ...filesToAdd];
                updateFilePreview();
                updateFileInput();
            }
        });

        // Drag-and-Drop for Files
        if (fileUploadArea) {
            fileUploadArea.addEventListener("dragover", (e) => {
                e.preventDefault();
                fileUploadArea.classList.add("border-indigo-500", "bg-gray-50");
            });

            fileUploadArea.addEventListener("dragleave", () => {
                fileUploadArea.classList.remove(
                    "border-indigo-500",
                    "bg-gray-50"
                );
            });

            fileUploadArea.addEventListener("drop", (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove(
                    "border-indigo-500",
                    "bg-gray-50"
                );
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    if (fileError) fileError.textContent = "";
                    const newFiles = Array.from(files);
                    const maxSizeMB = 20; // 20MB limit
                    const maxFilesAllowed = 5; // Maximum 5 files
                    let allFilesValid = true;

                    // Check if adding these files would exceed the 5 file limit
                    const existingFilesCount = countExistingFiles();
                    const currentFileCount =
                        selectedFiles.length + existingFilesCount;
                    const remainingSlots = maxFilesAllowed - currentFileCount;

                    if (remainingSlots <= 0) {
                        if (fileError) {
                            const message = `⚠️ LIMIT REACHED: You can only upload a maximum of ${maxFilesAllowed} files per product. This product already has ${currentFileCount} files.`;
                            fileError.textContent = message;
                            showGlobalError(
                                `You can only upload a maximum of ${maxFilesAllowed} files per product. This product already has ${currentFileCount} files.`
                            );
                        }
                        return;
                    }

                    // Check if the new files would exceed the limit
                    if (currentFileCount + newFiles.length > maxFilesAllowed) {
                        if (fileError) {
                            const message = `⚠️ LIMIT EXCEEDED: Adding ${newFiles.length} files would exceed the limit of ${maxFilesAllowed} files per product. Only ${remainingSlots} more file(s) can be added.`;
                            fileError.textContent = message;
                            showGlobalError(
                                `Adding ${newFiles.length} files would exceed the limit of ${maxFilesAllowed} files per product. Only ${remainingSlots} more file(s) can be added.`
                            );
                        }
                        return;
                    }

                    // Validate file sizes
                    newFiles.forEach((file) => {
                        const fileSizeMB = file.size / 1024 / 1024;
                        if (fileSizeMB > maxSizeMB) {
                            if (fileError) {
                                const message = `⚠️ SIZE LIMIT: File "${
                                    file.name
                                }" size (${fileSizeMB.toFixed(
                                    2
                                )} MB) exceeds the ${maxSizeMB} MB limit.`;
                                fileError.textContent = message;
                                showGlobalError(
                                    `File "${
                                        file.name
                                    }" size (${fileSizeMB.toFixed(
                                        2
                                    )} MB) exceeds the ${maxSizeMB} MB limit.`
                                );
                            }
                            allFilesValid = false;
                        }
                    });

                    if (allFilesValid) {
                        // Only add files if we're under the limit
                        const filesToAdd = newFiles.slice(0, remainingSlots);
                        selectedFiles = [...selectedFiles, ...filesToAdd];
                        updateFilePreview();
                        updateFileInput();
                    }
                }
            });
        }
    }

    function updateFilePreview() {
        if (!fileList) return;
        fileList.innerHTML = "";

        if (selectedFiles.length > 0) {
            if (filePreview) filePreview.classList.remove("hidden");

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement("div");
                fileItem.className =
                    "flex items-center justify-between rounded-lg border border-gray-200 p-3";

                const fileInfo = document.createElement("div");
                fileInfo.className = "flex items-center gap-3";

                const fileIcon = document.createElement("div");
                fileIcon.className =
                    "flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100";
                fileIcon.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-gray-500">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <line x1="10" y1="9" x2="8" y2="9"></line>
                    </svg>
                `;

                const fileDetails = document.createElement("div");
                fileDetails.innerHTML = `
                    <p class="text-sm font-medium text-gray-900">${
                        file.name
                    }</p>
                    <p class="text-xs text-gray-500">${(
                        file.size /
                        1024 /
                        1024
                    ).toFixed(2)} MB</p>
                `;

                fileInfo.appendChild(fileIcon);
                fileInfo.appendChild(fileDetails);

                const removeButton = document.createElement("button");
                removeButton.type = "button";
                removeButton.className =
                    "rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors";
                removeButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                        <path d="M3 6h18"></path>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        <line x1="10" y1="11" x2="10" y2="17"></line>
                        <line x1="14" y1="11" x2="14" y2="17"></line>
                    </svg>
                `;
                removeButton.onclick = () => {
                    selectedFiles.splice(index, 1);
                    updateFilePreview();
                    updateFileInput();
                    if (fileError) fileError.textContent = "";
                };

                fileItem.appendChild(fileInfo);
                fileItem.appendChild(removeButton);
                fileList.appendChild(fileItem);
            });
        } else {
            if (filePreview) filePreview.classList.add("hidden");
        }
    }

    function updateFileInput() {
        if (!fileInput) return;
        const dt = new DataTransfer();
        const maxFilesAllowed = 5; // Maximum 5 files
        const existingFilesCount = countExistingFiles();

        // Calculate how many new files we can add
        const remainingSlots = Math.max(
            0,
            maxFilesAllowed - existingFilesCount
        );

        // Only add up to the remaining slots
        const filesToAdd = selectedFiles.slice(0, remainingSlots);

        // Show warning if files were truncated
        if (selectedFiles.length > remainingSlots && fileError) {
            const message = `⚠️ LIMIT ENFORCED: Only the first ${remainingSlots} new files will be uploaded. ${
                selectedFiles.length - remainingSlots
            } file(s) were removed. Each product can have a maximum of ${maxFilesAllowed} files.`;
            fileError.textContent = message;
            showGlobalError(
                `Only the first ${remainingSlots} new files will be uploaded. ${
                    selectedFiles.length - remainingSlots
                } file(s) were removed. Each product can have a maximum of ${maxFilesAllowed} files.`
            );
            // Update the selectedFiles array to match what's actually being used
            selectedFiles = filesToAdd;
            // Update the preview to reflect the changes
            updateFilePreview();
        }

        filesToAdd.forEach((file) => dt.items.add(file));
        fileInput.files = dt.files;
    }

    // Remove Existing Files
    document.querySelectorAll(".remove-existing-file").forEach((button) => {
        button.addEventListener("click", function () {
            const index = this.getAttribute("data-index");
            const fileItem = this.closest(".existing-file-item");
            const removeInput = document.querySelector(
                `.remove-file-input[data-index="${index}"]`
            );

            removeInput.value = index;
            fileItem.style.display = "none";
        });
    });

    // Function to show a global error message
    function showGlobalError(message) {
        const productForm = document.getElementById("product-form");
        if (!productForm) return;

        // Remove any existing global errors
        document
            .querySelectorAll(".global-error")
            .forEach((error) => error.remove());

        // Create and show the error message
        const errorDiv = document.createElement("div");
        errorDiv.className =
            "mb-4 p-4 rounded-lg bg-red-50 text-red-700 border border-red-200 global-error";
        errorDiv.innerHTML = `<strong>⚠️ ERROR:</strong> ${message}`;
        productForm.prepend(errorDiv);

        // Scroll to the error message
        errorDiv.scrollIntoView({ behavior: "smooth", block: "center" });

        // Add a close button
        const closeButton = document.createElement("button");
        closeButton.type = "button";
        closeButton.className = "ml-2 text-red-700 hover:text-red-900";
        closeButton.innerHTML = "×";
        closeButton.onclick = () => errorDiv.remove();
        errorDiv.appendChild(closeButton);
    }

    // AJAX Form Submission with Error Handling
    const productForm = document.getElementById("product-form");
    if (productForm) {
        productForm.addEventListener("submit", function (e) {
            e.preventDefault();

            // Clear previous errors
            document
                .querySelectorAll(".form-error")
                .forEach((error) => error.remove());

            // Validate minimum price
            const priceInput = document.getElementById("price");
            const discountPriceInput =
                document.getElementById("discount_price");
            const hasDiscountCheckbox = document.getElementById("has_discount");

            if (priceInput && parseFloat(priceInput.value) < 5000) {
                showGlobalError("The minimum price must be at least Rp 5,000");
                return;
            }

            if (
                hasDiscountCheckbox &&
                hasDiscountCheckbox.checked &&
                discountPriceInput &&
                parseFloat(discountPriceInput.value) < 5000
            ) {
                showGlobalError(
                    "The minimum discount price must be at least Rp 5,000"
                );
                return;
            }

            // Check file limits before submission
            const maxImagesAllowed = 10;
            const maxFilesAllowed = 5;

            // Check image count
            const imagesInput = document.getElementById("images");
            const existingImagesCount = countExistingImages();
            const totalImagesCount =
                (imagesInput ? imagesInput.files.length : 0) +
                existingImagesCount;

            if (totalImagesCount > maxImagesAllowed) {
                showGlobalError(
                    `You can only upload a maximum of ${maxImagesAllowed} images per product. Current total: ${totalImagesCount} images. Please remove some images to continue.`
                );
                return;
            }

            // Check file count
            const filesInput = document.getElementById("files");
            const existingFilesCount = countExistingFiles();
            const totalFilesCount =
                (filesInput ? filesInput.files.length : 0) + existingFilesCount;

            if (totalFilesCount > maxFilesAllowed) {
                showGlobalError(
                    `You can only upload a maximum of ${maxFilesAllowed} files per product. Current total: ${totalFilesCount} files. Please remove some files to continue.`
                );
                return;
            }

            const formData = new FormData(this);
            const submitButton = productForm.querySelector(
                'button[type="submit"]'
            );
            const originalButtonText = submitButton.textContent;
            submitButton.disabled = true;
            submitButton.textContent = "Processing...";

            fetch(this.action, {
                method: this.method,
                body: formData,
                headers: {
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                    Accept: "application/json",
                },
            })
                .then((response) => response.json())
                .then((data) => {
                    submitButton.disabled = false;
                    submitButton.textContent = originalButtonText;

                    if (data.success) {
                        // Show success message
                        const successMessage = document.createElement("div");
                        successMessage.className =
                            "mb-4 p-4 rounded-lg bg-green-50 text-green-700 border border-green-200";
                        successMessage.textContent = data.message;
                        productForm.prepend(successMessage);

                        // Redirect after a short delay
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 1500);
                    } else {
                        // Show validation errors
                        if (data.errors) {
                            Object.keys(data.errors).forEach((field) => {
                                const input =
                                    productForm.querySelector(
                                        `[name="${field}"]`
                                    ) ||
                                    productForm.querySelector(
                                        `[name="${field}[]"]`
                                    );
                                if (input) {
                                    const errorDiv =
                                        document.createElement("p");
                                    errorDiv.className =
                                        "mt-1 text-sm text-red-600 form-error";
                                    errorDiv.textContent =
                                        data.errors[field][0];
                                    input.parentElement.appendChild(errorDiv);
                                    input.classList.add("border-red-500");
                                }
                            });

                            // Scroll to the first error
                            const firstError =
                                document.querySelector(".form-error");
                            if (firstError) {
                                firstError.scrollIntoView({
                                    behavior: "smooth",
                                    block: "center",
                                });
                            }
                        } else {
                            // General error
                            const errorDiv = document.createElement("div");
                            errorDiv.className =
                                "mb-4 p-4 rounded-lg bg-red-50 text-red-700 border border-red-200 form-error";
                            errorDiv.textContent =
                                data.message ||
                                "An error occurred. Please try again.";
                            productForm.prepend(errorDiv);
                        }
                    }
                })
                .catch((error) => {
                    submitButton.disabled = false;
                    submitButton.textContent = originalButtonText;

                    const errorDiv = document.createElement("div");
                    errorDiv.className =
                        "mb-4 p-4 rounded-lg bg-red-50 text-red-700 border border-red-200 form-error";
                    errorDiv.textContent =
                        "An unexpected error occurred. Please try again.";
                    productForm.prepend(errorDiv);
                    console.error("Error:", error);
                });
        });

        // Remove red border when user starts typing
        productForm
            .querySelectorAll("input, select, textarea")
            .forEach((input) => {
                input.addEventListener("input", function () {
                    this.classList.remove("border-red-500");
                    const error =
                        this.parentElement.querySelector(".form-error");
                    if (error) error.remove();
                });
            });
    }
});

tinymce.init({
    selector: "textarea#description",
    plugins: "autolink lists table lists",
    toolbar:
        "a11ycheck addcomment showcomments casechange checklist code export formatpainter pageembed permanentpen table tableofcontents numlist bullist",
    toolbar_mode: "floating",
    tinycomments_mode: "embedded",
    tinycomments_author: "Author name",
    setup: function (editor) {
        // Update the hidden textarea on change
        editor.on("change", function () {
            editor.save(); // This will update the textarea with the content
        });

        // Handle form submission
        editor.on("submit", function () {
            editor.save();
        });
    },
});
