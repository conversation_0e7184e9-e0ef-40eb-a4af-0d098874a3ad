<?php

namespace App\Http\Controllers;

use App\Models\MembershipTier;
use App\Models\UserMembership;
use App\Models\AiUsageLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MembershipController extends Controller
{
    /**
     * Display membership tiers and current user's membership status.
     */
    public function index()
    {
        $tiers = MembershipTier::active()->get();
        $currentMembership = null;
        $currentTier = null;
        $todayUsage = 0;
        $remainingPrompts = 0;

        if (Auth::check()) {
            $user = Auth::user();
            $currentMembership = $user->currentMembership;
            $currentTier = $user->getCurrentMembershipTier();
            $todayUsage = AiUsageLog::getTotalTodayUsage($user->id);
            $remainingPrompts = $user->getRemainingAiPrompts();
        }

        return view('membership.index', compact(
            'tiers', 
            'currentMembership', 
            'currentTier', 
            'todayUsage', 
            'remainingPrompts'
        ));
    }

    /**
     * Get current user's membership status via API.
     */
    public function status()
    {
        if (!Auth::check()) {
            return response()->json([
                'authenticated' => false,
                'message' => 'User not authenticated'
            ]);
        }

        $user = Auth::user();
        $currentTier = $user->getCurrentMembershipTier();
        $todayUsage = AiUsageLog::getTotalTodayUsage($user->id);
        $remainingPrompts = $user->getRemainingAiPrompts();

        return response()->json([
            'authenticated' => true,
            'current_tier' => $currentTier ? [
                'name' => $currentTier->name,
                'slug' => $currentTier->slug,
                'daily_ai_prompts' => $currentTier->daily_ai_prompts,
                'chatbot_products_limit' => $currentTier->chatbot_products_limit,
                'ai_tools_access' => $currentTier->ai_tools_access,
                'formatted_price' => $currentTier->formatted_price,
            ] : null,
            'usage' => [
                'today_usage' => $todayUsage,
                'remaining_prompts' => $remainingPrompts,
                'can_use_ai' => $user->canUseAi(),
                'can_activate_chatbot' => $user->canActivateChatbot(),
            ]
        ]);
    }

    /**
     * Upgrade user membership (placeholder for payment integration).
     */
    public function upgrade(Request $request)
    {
        $request->validate([
            'tier_slug' => 'required|string|exists:membership_tiers,slug'
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }

        $user = Auth::user();
        $newTier = MembershipTier::where('slug', $request->tier_slug)->first();

        if (!$newTier) {
            return response()->json(['error' => 'Invalid membership tier'], 400);
        }

        // For now, just create the membership (in real implementation, integrate with payment)
        // Deactivate current membership
        $user->memberships()->where('status', 'active')->update(['status' => 'cancelled']);

        // Create new membership
        $membership = UserMembership::create([
            'user_id' => $user->id,
            'membership_tier_id' => $newTier->id,
            'started_at' => now(),
            'expires_at' => $newTier->price > 0 ? now()->addMonth() : null, // Free tier doesn't expire
            'status' => 'active',
            'amount_paid' => $newTier->price,
            'payment_method' => 'manual', // Placeholder
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Membership upgraded successfully',
            'new_tier' => $newTier->name,
            'membership' => $membership
        ]);
    }
}
