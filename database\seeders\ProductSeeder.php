<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductDetailedCategory;
use App\Models\ProductImage;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    /**
     * List of file extensions for random files
     */
    protected $fileExtensions = [
        'pdf',
        'zip',
        'psd',
        'ai',
        'eps',
        'svg',
        'xd',
        'sketch',
        'fig',
        'doc',
        'docx',
        'ppt',
        'pptx',
        'xls',
        'xlsx'
    ];

    /**
     * List of image categories for Unsplash API
     */
    protected $imageCategories = [
        'business',
        'work',
        'technology',
        'design',
        'office',
        'marketing',
        'computer',
        'data',
        'template',
        'digital'
    ];

    public function run(): void
    {
        // Ensure storage directories exist
        Storage::disk('public')->makeDirectory('products/images');
        Storage::disk('public')->makeDirectory('products/files');

        // Get all sellers
        $sellers = User::whereHas('activeRoles', function ($query) {
            $query->where('slug', 'seller');
        })->get();

        if ($sellers->isEmpty()) {
            $this->command->info('No sellers found. Please run UserSeeder first.');
            return;
        }

        $this->command->info('Creating products for ' . $sellers->count() . ' sellers...');

        // Get all detailed categories
        $detailedCategories = ProductDetailedCategory::with(['subcategory.category'])->where('is_active', true)->get();

        if ($detailedCategories->isEmpty()) {
            $this->command->info('No detailed categories found. Please run ProductCategorySeeder first.');
            return;
        }

        // Legacy categories mapping to subcategory legacy_code
        $legacyToSubcategoryMap = [
            'template' => 'templates',
            'spreadsheet' => 'spreadsheets',
            'dashboard' => 'dashboards',
            'planner' => 'planners',
            'worksheet' => 'worksheets',
            'graphic' => 'graphics',
            'font' => 'fonts',
            'illustration' => 'illustrations',
            'ui_kit' => 'ui-kits',
            'code' => 'code',
            'plugin' => 'plugins',
            'api_template' => 'api-templates',
            'course_material' => 'course-materials',
            'ebook' => 'e-books',
        ];

        // Sample product descriptions
        $descriptions = [
            'This premium digital product is designed to help you streamline your workflow and boost productivity. It includes comprehensive documentation and regular updates.',
            'A professionally designed digital asset that will elevate your projects. Perfect for both beginners and professionals looking for high-quality resources.',
            'This versatile digital product can be customized to fit your specific needs. It comes with detailed instructions and customer support.',
            'An essential tool for any digital creator. This product saves you time and effort while delivering professional results every time.',
            'Created with attention to detail, this digital product offers exceptional value. It\'s easy to use and delivers consistent results.',
        ];

        // Create products for each seller
        $productsCreated = 0;
        foreach ($sellers as $seller) {
            // Create 3-7 products per seller
            $numProducts = rand(3, 7);

            for ($i = 1; $i <= $numProducts; $i++) {
                $name = "Product " . $i . " by " . $seller->name;
                $baseSlug = Str::slug($name);

                // Ensure slug uniqueness by adding a random string
                $slug = $baseSlug . '-' . Str::random(6);
                $description = $descriptions[array_rand($descriptions)];

                // Get a random detailed category
                $detailedCategory = $detailedCategories->random();
                $subcategory = $detailedCategory->subcategory;
                $category = $subcategory->category;

                // Get the legacy category code from the subcategory
                $legacyCategory = array_search($subcategory->slug, $legacyToSubcategoryMap) ?: 'template';

                $price = rand(5000, 100000) / 100; // Between Rp 50.00 and Rp 1,000.00

                // 70% chance of having a discount
                $hasDiscount = (rand(1, 10) <= 7);
                $discountPrice = $hasDiscount ? round($price * (rand(70, 95) / 100), 2) : null;

                // 80% chance of being active, 20% chance of being draft
                $status = (rand(1, 10) <= 8) ? 'active' : 'draft';

                // 30% chance of being featured
                $isFeatured = (rand(1, 10) <= 3);

                // Random rating between 3.5 and 5.0
                $averageRating = $status === 'active' ? (rand(35, 50) / 10) : null;

                // Category IDs are already available from the relationships above
                // $detailedCategory, $subcategory, and $category are already defined

                // Create the product with a random main image
                $mainImagePath = $this->downloadRandomImage($legacyCategory);
                if (!$mainImagePath) {
                    $mainImagePath = null; // In case download fails
                }

                // Create the product
                $product = Product::create([
                    'id' => (string) Str::uuid(),
                    'seller_id' => $seller->id,
                    'name' => $name,
                    'slug' => $slug,
                    'description' => $description,
                    'category' => $legacyCategory, // Keep the legacy category for backward compatibility
                    'category_id' => $category->id,
                    'subcategory_id' => $subcategory->id,
                    'detailed_category_id' => $detailedCategory->id,
                    'price' => $price,
                    'discount_price' => $discountPrice,
                    'status' => $status,
                    'image' => $mainImagePath, // Main image from Unsplash
                    'files' => json_encode($this->generateProductFiles(rand(1, 3))), // Generate 1-3 random files
                    'is_featured' => $isFeatured,
                    'average_rating' => $averageRating,
                    'reviews_count' => $averageRating ? rand(5, 50) : 0,
                    'created_at' => now()->subDays(rand(1, 60)),
                    'updated_at' => now()->subDays(rand(0, 30)),
                ]);

                // Generate additional product images (2-5 images)
                $imageCount = rand(2, 5);
                $this->generateProductImages($product->id, $legacyCategory, $imageCount);

                $productsCreated++;
            }
        }

        $this->command->info("Created $productsCreated products successfully!");
    }

    /**
     * Download a random image from Unsplash API
     *
     * @param string|null $category
     * @return string|null Path to the saved image
     */
    protected function downloadRandomImage(?string $category = null): ?string
    {
        try {
            // If no category is provided, use a random one from our list
            if (!$category) {
                $category = $this->imageCategories[array_rand($this->imageCategories)];
            }

            // Generate a random width and height between 800 and 1200
            $width = rand(800, 1200);
            $height = rand(800, 1200);

            // Use Unsplash Source API to get a random image
            $imageUrl = "https://source.unsplash.com/random/{$width}x{$height}/?{$category}";

            // Generate a unique filename
            $filename = 'product_' . Str::uuid() . '.jpg';
            $path = 'products/images/' . $filename;

            // Download the image
            $imageContents = file_get_contents($imageUrl);
            if (!$imageContents) {
                return null;
            }

            // Save the image to storage
            Storage::disk('public')->put($path, $imageContents);

            return $path;
        } catch (\Exception $e) {
            // Log the error but don't fail the seeder
            Log::error('Failed to download random image: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Generate multiple random images for a product
     *
     * @param string $productId
     * @param string $category
     * @param int $count
     * @return array Array of image paths
     */
    protected function generateProductImages(string $productId, string $category, int $count = 3): array
    {
        $images = [];

        for ($i = 0; $i < $count; $i++) {
            $path = $this->downloadRandomImage($category);
            if ($path) {
                // Create a product image record
                $isPrimary = ($i === 0); // First image is primary

                ProductImage::create([
                    'product_id' => $productId,
                    'path' => $path,
                    'is_primary' => $isPrimary,
                    'sort_order' => $i,
                ]);

                $images[] = $path;
            }
        }

        return $images;
    }

    /**
     * Generate a random file for a product
     *
     * @param string|null $extension
     * @param int|null $sizeKB
     * @return array File information
     */
    protected function generateRandomFile(?string $extension = null, ?int $sizeKB = null): array
    {
        // If no extension is provided, use a random one from our list
        if (!$extension) {
            $extension = $this->fileExtensions[array_rand($this->fileExtensions)];
        }

        // If no size is provided, generate a random one between 100KB and 5MB
        if (!$sizeKB) {
            $sizeKB = rand(100, 5000);
        }

        // Generate a random filename
        $filename = 'sample_' . Str::random(8) . '.' . $extension;
        $path = 'products/files/' . $filename;

        // Create a random file with the specified size
        $content = Str::random($sizeKB * 1024); // Convert KB to bytes

        // Save the file to storage
        Storage::disk('public')->put($path, $content);

        return [
            'name' => $filename,
            'path' => $path,
            'size' => $sizeKB * 1024, // Size in bytes
        ];
    }

    /**
     * Generate multiple random files for a product
     *
     * @param int $count
     * @return array Array of file information
     */
    protected function generateProductFiles(int $count = 2): array
    {
        $files = [];

        for ($i = 0; $i < $count; $i++) {
            $files[] = $this->generateRandomFile();
        }

        return $files;
    }
}
