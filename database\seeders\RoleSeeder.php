<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $roles = [
            [
                'name' => 'User',
                'slug' => 'user',
                'description' => 'Regular user with basic access to browse and purchase products',
                'is_active' => true,
            ],
            [
                'name' => 'Seller',
                'slug' => 'seller',
                'description' => 'Seller with access to manage their own products and store',
                'is_active' => true,
            ],
            [
                'name' => 'Admin',
                'slug' => 'admin',
                'description' => 'Administrator with access to manage users and approve seller applications',
                'is_active' => true,
            ],
            [
                'name' => 'Super Admin',
                'slug' => 'superadmin',
                'description' => 'Super administrator with full system access including financial systems',
                'is_active' => true,
            ],
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['slug' => $roleData['slug']],
                $roleData
            );
        }

        // Create permissions
        $permissions = [
            // User permissions
            ['name' => 'Browse Products', 'slug' => 'browse-products', 'category' => 'product', 'description' => 'View and browse products'],
            ['name' => 'Purchase Products', 'slug' => 'purchase-products', 'category' => 'order', 'description' => 'Purchase products'],
            ['name' => 'Manage Profile', 'slug' => 'manage-profile', 'category' => 'user', 'description' => 'Update own profile'],
            
            // Seller permissions
            ['name' => 'Manage Products', 'slug' => 'manage-products', 'category' => 'product', 'description' => 'Create, update, delete own products'],
            ['name' => 'View Orders', 'slug' => 'view-orders', 'category' => 'order', 'description' => 'View orders for own products'],
            ['name' => 'Manage Store', 'slug' => 'manage-store', 'category' => 'store', 'description' => 'Manage store information'],
            ['name' => 'View Analytics', 'slug' => 'view-analytics', 'category' => 'analytics', 'description' => 'View sales analytics'],
            
            // Admin permissions
            ['name' => 'Approve Sellers', 'slug' => 'approve-sellers', 'category' => 'admin', 'description' => 'Approve or reject seller applications'],
            ['name' => 'Manage Categories', 'slug' => 'manage-categories', 'category' => 'admin', 'description' => 'Manage product categories'],
            ['name' => 'Manage Users', 'slug' => 'manage-users', 'category' => 'admin', 'description' => 'Manage user accounts'],
            
            // Super Admin permissions
            ['name' => 'Access Financial Systems', 'slug' => 'access-financial-systems', 'category' => 'superadmin', 'description' => 'Access financial and payment systems'],
            ['name' => 'Manage System Settings', 'slug' => 'manage-system-settings', 'category' => 'superadmin', 'description' => 'Manage system-wide settings'],
            ['name' => 'Full System Access', 'slug' => 'full-system-access', 'category' => 'superadmin', 'description' => 'Complete access to all system features'],
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['slug' => $permissionData['slug']],
                $permissionData
            );
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Assign permissions to roles.
     */
    private function assignPermissionsToRoles(): void
    {
        $userRole = Role::findBySlug('user');
        $sellerRole = Role::findBySlug('seller');
        $adminRole = Role::findBySlug('admin');
        $superAdminRole = Role::findBySlug('superadmin');

        // User permissions
        $userPermissions = ['browse-products', 'purchase-products', 'manage-profile'];
        foreach ($userPermissions as $permissionSlug) {
            $permission = Permission::findBySlug($permissionSlug);
            if ($permission) {
                $userRole->assignPermission($permission);
            }
        }

        // Seller permissions (includes user permissions)
        $sellerPermissions = array_merge($userPermissions, [
            'manage-products', 'view-orders', 'manage-store', 'view-analytics'
        ]);
        foreach ($sellerPermissions as $permissionSlug) {
            $permission = Permission::findBySlug($permissionSlug);
            if ($permission) {
                $sellerRole->assignPermission($permission);
            }
        }

        // Admin permissions (includes seller permissions)
        $adminPermissions = array_merge($sellerPermissions, [
            'approve-sellers', 'manage-categories', 'manage-users'
        ]);
        foreach ($adminPermissions as $permissionSlug) {
            $permission = Permission::findBySlug($permissionSlug);
            if ($permission) {
                $adminRole->assignPermission($permission);
            }
        }

        // Super Admin permissions (includes all permissions)
        $allPermissions = Permission::pluck('slug')->toArray();
        foreach ($allPermissions as $permissionSlug) {
            $permission = Permission::findBySlug($permissionSlug);
            if ($permission) {
                $superAdminRole->assignPermission($permission);
            }
        }
    }
}
