<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductDetailedCategory;
use Illuminate\Support\Str;

class CourseCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define course-specific categories
        $courseCategories = [
            [
                'name' => 'Technology & Programming',
                'icon' => 'code',
                'description' => 'Programming, software development, and technology courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Web Development',
                        'description' => 'Frontend, backend, and full-stack web development.',
                        'legacy_code' => 'web_dev',
                        'detailed_categories' => [
                            ['name' => 'Frontend Development', 'description' => 'HTML, CSS, JavaScript, React, Vue.js courses.'],
                            ['name' => 'Backend Development', 'description' => 'Node.js, PHP, Python, Java backend courses.'],
                            ['name' => 'Full-Stack Development', 'description' => 'Complete web development bootcamps.'],
                            ['name' => 'WordPress Development', 'description' => 'WordPress theme and plugin development.'],
                        ]
                    ],
                    [
                        'name' => 'Mobile Development',
                        'description' => 'iOS, Android, and cross-platform mobile app development.',
                        'legacy_code' => 'mobile_dev',
                        'detailed_categories' => [
                            ['name' => 'iOS Development', 'description' => 'Swift and iOS app development courses.'],
                            ['name' => 'Android Development', 'description' => 'Kotlin and Java Android development.'],
                            ['name' => 'React Native', 'description' => 'Cross-platform mobile development with React Native.'],
                            ['name' => 'Flutter Development', 'description' => 'Cross-platform development with Flutter.'],
                        ]
                    ],
                    [
                        'name' => 'Programming Languages',
                        'description' => 'Specific programming language courses.',
                        'legacy_code' => 'programming_lang',
                        'detailed_categories' => [
                            ['name' => 'Python Programming', 'description' => 'Python fundamentals and advanced concepts.'],
                            ['name' => 'JavaScript Programming', 'description' => 'JavaScript from basics to advanced.'],
                            ['name' => 'Java Programming', 'description' => 'Java programming and enterprise development.'],
                            ['name' => 'C++ Programming', 'description' => 'C++ programming and system development.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other technology and programming topics.',
                        'legacy_code' => 'tech_other',
                        'detailed_categories' => [
                            ['name' => 'Other', 'description' => 'Miscellaneous technology and programming topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Artificial Intelligence & Data Science',
                'icon' => 'brain',
                'description' => 'AI, machine learning, data science, and analytics courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Machine Learning',
                        'description' => 'Machine learning algorithms and applications.',
                        'legacy_code' => 'machine_learning',
                        'detailed_categories' => [
                            ['name' => 'Supervised Learning', 'description' => 'Classification and regression algorithms.'],
                            ['name' => 'Unsupervised Learning', 'description' => 'Clustering and dimensionality reduction.'],
                            ['name' => 'Deep Learning', 'description' => 'Neural networks and deep learning frameworks.'],
                            ['name' => 'Natural Language Processing', 'description' => 'NLP and text processing techniques.'],
                        ]
                    ],
                    [
                        'name' => 'Data Science',
                        'description' => 'Data analysis, visualization, and statistical modeling.',
                        'legacy_code' => 'data_science',
                        'detailed_categories' => [
                            ['name' => 'Data Analysis', 'description' => 'Statistical analysis and data exploration.'],
                            ['name' => 'Data Visualization', 'description' => 'Creating charts, graphs, and dashboards.'],
                            ['name' => 'Big Data', 'description' => 'Working with large datasets and distributed systems.'],
                            ['name' => 'Business Intelligence', 'description' => 'BI tools and business analytics.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other AI and data science topics.',
                        'legacy_code' => 'ai_other',
                        'detailed_categories' => [
                            ['name' => 'Other', 'description' => 'Miscellaneous AI and data science topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Business & Entrepreneurship',
                'icon' => 'briefcase',
                'description' => 'Business skills, entrepreneurship, and management courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Digital Marketing',
                        'description' => 'Online marketing strategies and techniques.',
                        'legacy_code' => 'digital_marketing',
                        'detailed_categories' => [
                            ['name' => 'Social Media Marketing', 'description' => 'Facebook, Instagram, LinkedIn marketing.'],
                            ['name' => 'Search Engine Optimization', 'description' => 'SEO strategies and techniques.'],
                            ['name' => 'Content Marketing', 'description' => 'Content creation and marketing strategies.'],
                            ['name' => 'Email Marketing', 'description' => 'Email campaigns and automation.'],
                        ]
                    ],
                    [
                        'name' => 'Business Strategy',
                        'description' => 'Strategic planning and business development.',
                        'legacy_code' => 'business_strategy',
                        'detailed_categories' => [
                            ['name' => 'Startup Fundamentals', 'description' => 'Starting and growing a business.'],
                            ['name' => 'Project Management', 'description' => 'Project planning and execution.'],
                            ['name' => 'Leadership Skills', 'description' => 'Management and leadership development.'],
                            ['name' => 'Financial Planning', 'description' => 'Business finance and budgeting.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other business and entrepreneurship topics.',
                        'legacy_code' => 'business_other',
                        'detailed_categories' => [
                            ['name' => 'Other', 'description' => 'Miscellaneous business and entrepreneurship topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Design & Creative',
                'icon' => 'palette',
                'description' => 'Graphic design, UI/UX, and creative skills courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Graphic Design',
                        'description' => 'Visual design and graphic creation.',
                        'legacy_code' => 'graphic_design',
                        'detailed_categories' => [
                            ['name' => 'Logo Design', 'description' => 'Creating professional logos and branding.'],
                            ['name' => 'Print Design', 'description' => 'Designing for print media and materials.'],
                            ['name' => 'Digital Art', 'description' => 'Digital illustration and artwork.'],
                            ['name' => 'Typography', 'description' => 'Font design and typography principles.'],
                        ]
                    ],
                    [
                        'name' => 'UI/UX Design',
                        'description' => 'User interface and user experience design.',
                        'legacy_code' => 'ui_ux_design',
                        'detailed_categories' => [
                            ['name' => 'User Interface Design', 'description' => 'Creating intuitive user interfaces.'],
                            ['name' => 'User Experience Design', 'description' => 'Designing user-centered experiences.'],
                            ['name' => 'Prototyping', 'description' => 'Creating interactive prototypes.'],
                            ['name' => 'Design Systems', 'description' => 'Building consistent design systems.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other design and creative topics.',
                        'legacy_code' => 'design_other',
                        'detailed_categories' => [
                            ['name' => 'Other', 'description' => 'Miscellaneous design and creative topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'Finance & Investment',
                'icon' => 'dollar-sign',
                'description' => 'Personal finance, investing, and financial literacy courses.',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'Personal Finance',
                        'description' => 'Managing personal finances and budgeting.',
                        'legacy_code' => 'personal_finance',
                        'detailed_categories' => [
                            ['name' => 'Budgeting & Saving', 'description' => 'Creating budgets and saving strategies.'],
                            ['name' => 'Debt Management', 'description' => 'Managing and eliminating debt.'],
                            ['name' => 'Retirement Planning', 'description' => 'Planning for retirement and pensions.'],
                            ['name' => 'Insurance Planning', 'description' => 'Understanding and choosing insurance.'],
                        ]
                    ],
                    [
                        'name' => 'Investment & Trading',
                        'description' => 'Stock market, crypto, and investment strategies.',
                        'legacy_code' => 'investment_trading',
                        'detailed_categories' => [
                            ['name' => 'Stock Market Investing', 'description' => 'Stock analysis and investment strategies.'],
                            ['name' => 'Cryptocurrency', 'description' => 'Understanding and trading cryptocurrencies.'],
                            ['name' => 'Real Estate Investment', 'description' => 'Property investment strategies.'],
                            ['name' => 'Trading Strategies', 'description' => 'Day trading and swing trading techniques.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other finance and investment topics.',
                        'legacy_code' => 'finance_other',
                        'detailed_categories' => [
                            ['name' => 'Other', 'description' => 'Miscellaneous finance and investment topics.'],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'UMKM & Small Business',
                'icon' => 'store',
                'description' => 'Courses specifically designed for small and medium enterprises (UMKM).',
                'product_type' => 'course',
                'subcategories' => [
                    [
                        'name' => 'WhatsApp Bot',
                        'description' => 'Learn to create and manage WhatsApp bots for business automation.',
                        'legacy_code' => 'whatsapp_bot',
                        'detailed_categories' => [
                            ['name' => 'WhatsApp Bot Setup', 'description' => 'Setting up WhatsApp bots for business.'],
                            ['name' => 'Bot Automation', 'description' => 'Automating customer service with bots.'],
                            ['name' => 'Bot Marketing', 'description' => 'Using bots for marketing campaigns.'],
                            ['name' => 'Bot Analytics', 'description' => 'Tracking and analyzing bot performance.'],
                        ]
                    ],
                    [
                        'name' => 'AI Automation',
                        'description' => 'Implementing AI solutions for small business automation.',
                        'legacy_code' => 'ai_automation',
                        'detailed_categories' => [
                            ['name' => 'AI Customer Service', 'description' => 'Automating customer service with AI.'],
                            ['name' => 'AI Content Creation', 'description' => 'Using AI for content generation.'],
                            ['name' => 'AI Data Analysis', 'description' => 'Leveraging AI for business insights.'],
                            ['name' => 'AI Process Automation', 'description' => 'Automating business processes with AI.'],
                        ]
                    ],
                    [
                        'name' => 'Digital Marketing for UMKM',
                        'description' => 'Digital marketing strategies tailored for small businesses.',
                        'legacy_code' => 'umkm_digital_marketing',
                        'detailed_categories' => [
                            ['name' => 'Social Media for UMKM', 'description' => 'Social media strategies for small businesses.'],
                            ['name' => 'E-commerce Setup', 'description' => 'Setting up online stores for UMKM.'],
                            ['name' => 'Local SEO', 'description' => 'Local search optimization for small businesses.'],
                            ['name' => 'Budget Marketing', 'description' => 'Cost-effective marketing strategies.'],
                        ]
                    ],
                    [
                        'name' => 'Business Operations',
                        'description' => 'Essential business operations for small enterprises.',
                        'legacy_code' => 'umkm_operations',
                        'detailed_categories' => [
                            ['name' => 'Inventory Management', 'description' => 'Managing stock and inventory efficiently.'],
                            ['name' => 'Financial Management', 'description' => 'Basic accounting and financial planning.'],
                            ['name' => 'Customer Relationship', 'description' => 'Building and maintaining customer relationships.'],
                            ['name' => 'Supply Chain', 'description' => 'Managing suppliers and distribution.'],
                        ]
                    ],
                    [
                        'name' => 'Other',
                        'description' => 'Other UMKM-related topics and specialized courses.',
                        'legacy_code' => 'umkm_other',
                        'detailed_categories' => [
                            ['name' => 'Other', 'description' => 'Miscellaneous UMKM topics and specialized courses.'],
                        ]
                    ],
                ]
            ],
        ];

        $this->createCourseCategories($courseCategories);
    }

    /**
     * Create course categories with their subcategories and detailed categories.
     */
    private function createCourseCategories(array $categories): void
    {
        foreach ($categories as $index => $categoryData) {
            $slug = Str::slug($categoryData['name']);

            // Check if the slug already exists
            $counter = 1;
            $originalSlug = $slug;
            while (ProductCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create the main category
            $category = ProductCategory::create([
                'name' => $categoryData['name'],
                'slug' => $slug,
                'description' => $categoryData['description'] ?? null,
                'icon' => $categoryData['icon'] ?? null,
                'product_type' => $categoryData['product_type'] ?? 'course',
                'sort_order' => $index + 100, // Start from 100 to avoid conflicts with existing categories
                'is_active' => true,
            ]);

            // Create subcategories
            if (isset($categoryData['subcategories']) && is_array($categoryData['subcategories'])) {
                $this->createSubcategories($categoryData['subcategories'], $category->id);
            }
        }
    }

    /**
     * Create subcategories for a given category.
     */
    private function createSubcategories(array $subcategories, string $categoryId): void
    {
        foreach ($subcategories as $index => $subcategoryData) {
            $slug = Str::slug($subcategoryData['name']);

            // Check if the slug already exists
            $counter = 1;
            $originalSlug = $slug;
            while (ProductSubcategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-course-' . $counter;
                $counter++;
            }

            // Create the subcategory
            $subcategory = ProductSubcategory::create([
                'category_id' => $categoryId,
                'name' => $subcategoryData['name'],
                'slug' => $slug,
                'description' => $subcategoryData['description'] ?? null,
                'icon' => $subcategoryData['icon'] ?? null,
                'legacy_code' => $subcategoryData['legacy_code'] ?? null,
                'sort_order' => $index,
                'is_active' => true,
            ]);

            // Create detailed categories if they exist
            if (isset($subcategoryData['detailed_categories']) && is_array($subcategoryData['detailed_categories'])) {
                $this->createDetailedCategories($subcategoryData['detailed_categories'], $subcategory->id);
            }
        }
    }

    /**
     * Create detailed categories for a given subcategory.
     */
    private function createDetailedCategories(array $detailedCategories, string $subcategoryId): void
    {
        foreach ($detailedCategories as $index => $detailedCategoryData) {
            $slug = Str::slug($detailedCategoryData['name']);

            // Check if the slug already exists globally (not just within subcategory)
            $counter = 1;
            $originalSlug = $slug;
            while (ProductDetailedCategory::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-course-' . $counter;
                $counter++;
            }

            // Create the detailed category
            ProductDetailedCategory::create([
                'subcategory_id' => $subcategoryId,
                'name' => $detailedCategoryData['name'],
                'slug' => $slug,
                'description' => $detailedCategoryData['description'] ?? null,
                'sort_order' => $index,
                'is_active' => true,
            ]);
        }
    }
}
