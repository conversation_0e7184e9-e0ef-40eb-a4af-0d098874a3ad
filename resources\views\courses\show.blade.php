@extends('layouts.browse')

@section('title', $course->title . ' - Digitora')

@section('content')
<!-- Course Header -->
<div class="bg-gradient-to-r from-gray-900 to-gray-800 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Breadcrumb -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('browse.courses') }}" class="text-gray-300 hover:text-white transition-colors">Courses</a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-300">{{ $course->title }}</span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Category Badge -->
        <div class="mb-4">
            <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-indigo-100 text-indigo-800">
                {{ $course->detailedCategory->name ?? 'Course' }}
            </span>
        </div>

        <!-- Course Title and Meta -->
        <h1 class="text-4xl font-bold mb-4 leading-tight">{{ $course->title }}</h1>
        <p class="text-xl text-gray-300 mb-6 leading-relaxed">{{ $course->short_description }}</p>

        <!-- Course Stats -->
        <div class="flex flex-wrap items-center gap-6 text-sm mb-6">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                {{ $course->sections->count() }} sections
            </div>
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                {{ $course->curriculumItems->count() }} lessons
            </div>
            @if($course->estimated_duration)
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ floor($course->estimated_duration / 60) }}h {{ $course->estimated_duration % 60 }}m
                </div>
            @endif
            <div class="flex items-center">
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full
                    {{ $course->difficulty_level === 'beginner' ? 'bg-green-100 text-green-800' :
                       ($course->difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                    {{ ucfirst($course->difficulty_level) }}
                </span>
            </div>
        </div>

        <!-- Instructor -->
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span class="text-lg font-medium text-white">
                            {{ substr($course->seller->name, 0, 1) }}
                        </span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-lg font-medium">{{ $course->seller->name }}</p>
                    @if($course->seller->sellerApplication)
                        <p class="text-gray-300">{{ $course->seller->sellerApplication->store_name }}</p>
                    @endif
                </div>
            </div>
            @if($course->seller->sellerApplication && $course->seller->sellerApplication->store_name_slug)
                <a href="{{ route('store.show', $course->seller->sellerApplication->store_name_slug) }}"
                   class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white text-sm font-medium rounded-lg transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-4 4"></path>
                    </svg>
                    Visit Store
                </a>
            @endif
        </div>
    </div>
</div>

<!-- Course Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Content - Course Information (Primary Focus) -->
        <div class="lg:col-span-3 space-y-8">
            <!-- 1. About this course (Description) -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">About this course</h2>
                <div class="prose max-w-none text-gray-600">
                    {!! nl2br(e($course->description)) !!}
                </div>
            </div>

            <!-- 2. Course Content Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Course Content</h2>
                    <div class="text-sm text-gray-500">
                        {{ $course->sections->count() }} sections • {{ $course->curriculumItems->count() }} lessons
                        @if($course->estimated_duration)
                            • {{ floor($course->estimated_duration / 60) }}h {{ $course->estimated_duration % 60 }}m total
                        @endif
                    </div>
                </div>

                <div class="space-y-4">
                    @foreach($course->sections as $section)
                        <div class="border border-gray-200 rounded-lg overflow-hidden">
                            <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-gray-900">{{ $section->title }}</h3>
                                    <span class="text-sm text-gray-500">{{ $section->curriculumItems->count() }} lessons</span>
                                </div>
                                @if($section->description)
                                    <p class="text-sm text-gray-600 mt-1">{{ $section->description }}</p>
                                @endif
                            </div>
                            <div class="divide-y divide-gray-100">
                                @foreach($section->curriculumItems as $item)
                                    <div class="px-6 py-4 hover:bg-gray-50 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <!-- Content Type Icon -->
                                                @if($item->type === 'video')
                                                    <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                    </svg>
                                                @elseif($item->type === 'pdf')
                                                    <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                @elseif($item->type === 'document')
                                                    <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                @else
                                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                    </svg>
                                                @endif
                                                <div>
                                                    <h4 class="text-sm font-medium text-gray-900">{{ $item->title }}</h4>
                                                    @if($item->description)
                                                        <p class="text-xs text-gray-500 mt-1">{{ Str::limit($item->description, 100) }}</p>
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                @if($item->estimated_duration)
                                                    <span class="text-xs text-gray-500">{{ $item->estimated_duration }}min</span>
                                                @endif
                                                @if($item->is_preview)
                                                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800">Preview</span>
                                                @elseif($hasPurchased)
                                                    <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Available</span>
                                                @else
                                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                                    </svg>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- 3. What You'll Learn -->
            @if($course->what_you_will_learn && is_array($course->what_you_will_learn) && count($course->what_you_will_learn) > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">What you'll learn</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        @foreach($course->what_you_will_learn as $item)
                            <div class="flex items-start">
                                <svg class="w-5 h-5 mr-3 mt-0.5 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700">{{ $item }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- 4. Requirements -->
            @if($course->requirements && is_array($course->requirements) && count($course->requirements) > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                    <ul class="space-y-2">
                        @foreach($course->requirements as $requirement)
                            <li class="flex items-start">
                                <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <span class="text-gray-700">{{ $requirement }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- 5. Target Audience -->
            @if($course->target_audience && is_array($course->target_audience) && count($course->target_audience) > 0)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Who this course is for</h2>
                    <ul class="space-y-2">
                        @foreach($course->target_audience as $audience)
                            <li class="flex items-start">
                                <svg class="w-5 h-5 mr-3 mt-0.5 text-indigo-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span class="text-gray-700">{{ $audience }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
            @endif
        </div>

        <!-- Sidebar - Course Purchase Card -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-lg p-6 sticky top-6">
                <!-- Course Thumbnail -->
                <div class="aspect-w-16 aspect-h-9 mb-4">
                    <img src="{{ $course->thumbnail_url ?? asset('images/course-placeholder.jpg') }}"
                         alt="{{ $course->title }}"
                         class="w-full h-48 object-cover rounded-lg">
                </div>

                <!-- Price -->
                <div class="text-center mb-6">
                    @if($course->discount_price)
                        <div class="text-3xl font-bold text-gray-900">
                            Rp {{ number_format($course->discount_price) }}
                        </div>
                        <div class="text-lg text-gray-500 line-through">
                            Rp {{ number_format($course->price) }}
                        </div>
                        <div class="text-sm text-green-600 font-medium">
                            Save {{ number_format((($course->price - $course->discount_price) / $course->price) * 100, 0) }}%
                        </div>
                    @else
                        <div class="text-3xl font-bold text-gray-900">
                            Rp {{ number_format($course->price) }}
                        </div>
                    @endif
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    @if($hasPurchased)
                        <a href="{{ route('browse.courses.access', $course) }}"
                           class="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium text-center block transition-colors">
                            Access Course
                        </a>
                    @else
                        @auth
                            @if($isInCart)
                                <a href="{{ route('cart.index') }}"
                                   class="w-full bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium text-center block transition-colors">
                                    Already in Cart - View Cart
                                </a>
                            @else
                                <button onclick="addToCart('{{ $course->id }}')"
                                        class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    Add to Cart
                                </button>
                            @endif

                            <button onclick="buyNow('{{ $course->slug }}')"
                                    class="w-full bg-gray-900 hover:bg-gray-800 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                Buy Now
                            </button>
                        @else
                            <a href="{{ route('login') }}"
                               class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium text-center block transition-colors">
                                Login to Purchase
                            </a>
                        @endauth
                    @endif
                </div>

                <!-- Course Features -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h4 class="font-medium text-gray-900 mb-3">This course includes:</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ $course->sections->count() }} sections
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ $course->curriculumItems->count() }} learning materials
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Lifetime access
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Mobile and desktop access
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function addToCart(courseId) {
    // Create a form and submit it to add course to cart
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("cart.add-course") }}';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    // Add course ID
    const courseIdInput = document.createElement('input');
    courseIdInput.type = 'hidden';
    courseIdInput.name = 'course_id';
    courseIdInput.value = courseId;
    form.appendChild(courseIdInput);

    document.body.appendChild(form);
    form.submit();
}

function buyNow(courseSlug) {
    // Buy now functionality - redirect to direct purchase
    window.location.href = '{{ route("course.buy", ":courseSlug") }}'.replace(':courseSlug', courseSlug);
}
</script>
@endpush
@endsection
