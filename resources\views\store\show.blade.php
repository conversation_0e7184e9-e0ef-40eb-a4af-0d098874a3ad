@extends('store.layout')

@section('head')
    <!-- Page Context Meta Tags for DigiAI -->
    <meta name="store-slug" content="{{ $seller->store_name_slug }}">
    <meta name="store-id" content="{{ $seller->id }}">
@endsection

@section('content')
    <!-- Store Hero Section -->
    <div class="hero-gradient py-16 md:py-20">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 items-center">
                <div class="animate-on-scroll">
                    <div class="flex flex-col md:flex-row md:items-center gap-4 mb-4">
                        <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight">Welcome to <span class="text-white">{{ $seller->store_name }}</span></h1>
                        @if($sellerMembershipTier && $sellerMembershipTier->slug !== 'starter')
                            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-bold shadow-lg transform hover:scale-105 transition-all duration-200 {{ $sellerMembershipTier->slug === 'basic' ? 'bg-gradient-to-r from-gray-400 to-gray-600 text-white border-2 border-gray-300' : 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 border-2 border-yellow-300' }}">
                                @if($sellerMembershipTier->slug === 'basic')
                                    <!-- Silver Badge Icon -->
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                    <span class="text-white">SILVER VERIFIED</span>
                                @else
                                    <!-- Gold Badge Icon -->
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                    <span class="text-yellow-900">GOLD VERIFIED</span>
                                @endif
                                <!-- Verification checkmark -->
                                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        @endif
                    </div>
                    @if($sellerMembershipTier && $sellerMembershipTier->slug !== 'starter')
                        <div class="mb-4">
                            <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white/90 backdrop-blur-sm">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                {{ $sellerMembershipTier->slug === 'basic' ? 'Trusted Silver Partner' : 'Premium Gold Partner' }} • Enhanced Support • AI-Powered Tools
                            </div>
                        </div>
                    @endif
                    <p class="mt-4 md:mt-6 text-lg md:text-xl text-indigo-100 leading-relaxed">
                        {{ $seller->store_description ?? 'Discover our collection of premium digital products designed to help you succeed.' }}
                    </p>
                    <div class="mt-8 md:mt-10 flex flex-col sm:flex-row gap-4">
                        <a href="#products"
                            class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                            Digital Products
                        </a>
                        <a href="#courses"
                            class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-purple-600 hover:bg-purple-700 transition-colors duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            Online Courses
                        </a>
                        <a href="#about-store"
                            class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 transition-colors duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            About Store
                        </a>
                    </div>
                </div>
                <div class="hidden md:block animate-on-scroll" style="animation-delay: 200ms;">
                    <div class="relative flex justify-center">
                        <div class="absolute -inset-0.5 bg-white rounded-lg blur opacity-30"></div>
                        <div class="relative bg-white/10 backdrop-blur-sm p-8 rounded-lg shadow-xl border border-white/20 flex items-center justify-center">
                            @if ($seller->store_logo)
                                <img src="{{ route('store.logo', $seller->store_slug) }}" alt="{{ $seller->store_name }}"
                                    class="h-48 w-48 rounded-lg object-cover shadow-lg">
                            @else
                                <div class="flex h-48 w-48 items-center justify-center rounded-lg hero-gradient text-white shadow-lg">
                                    <span class="text-7xl font-bold">{{ substr($seller->store_name, 0, 1) }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI-Powered Products Section -->
    @if (isset($aiPoweredProducts) && $aiPoweredProducts->count() > 0)
        <div class="py-12 bg-gradient-to-br from-blue-50 to-indigo-100">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-10 animate-on-scroll">
                    <div class="flex items-center justify-center mb-4">
                        <div class="flex items-center bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                                <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                            </svg>
                            AI-POWERED PRODUCTS
                        </div>
                    </div>
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900">Products with DigiAI Assistant</h2>
                    <p class="mt-3 text-lg text-gray-600 max-w-3xl mx-auto">
                        Get instant answers and personalized assistance for these products with our AI chatbot
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6 md:gap-8">
                    @foreach ($aiPoweredProducts as $product)
                    <div class="group relative animate-on-scroll bg-white rounded-xl overflow-hidden shadow-lg border-2 border-blue-200 hover:border-blue-400 transition-all duration-300 transform hover:scale-105" style="animation-delay: {{ $loop->index * 100 }}ms">
                        <!-- AI Badge -->
                        <div class="absolute top-3 left-3 z-10 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg flex items-center">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                                <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                            </svg>
                            AI ASSISTANT
                        </div>

                        <div class="aspect-w-4 aspect-h-3 overflow-hidden bg-gray-100">
                            <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                    alt="{{ $product->name }}"
                                    class="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-110">
                            </a>
                            @if ($product->discount_price)
                                <div class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                                    SALE {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% OFF
                                </div>
                            @endif
                        </div>

                        <div class="p-5">
                            <h3 class="text-lg font-semibold text-gray-900 line-clamp-1">
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}" class="hover:text-blue-600 transition-colors duration-300">
                                    {{ $product->name }}
                                </a>
                            </h3>
                            <p class="mt-1 text-sm text-gray-500">
                                @if(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory)
                                    {{ $product->productDetailedCategory->name }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                    {{ $product->productSubcategory->name }}
                                @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                    {{ $product->productCategory->name }}
                                @else
                                    {{ ucfirst($product->category) }}
                                @endif
                            </p>

                            <div class="mt-3">
                                @if ($product->discount_price)
                                    <p class="text-lg font-bold text-blue-600">
                                        Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                    <p class="text-sm text-gray-500 line-through">
                                        Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @else
                                    <p class="text-lg font-bold text-blue-600">
                                        Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @endif
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-3 flex gap-2">
                                <!-- View Details Button (Left) -->
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}"
                                   class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                                    View Details
                                </a>

                                <!-- DigiAI Button (Right) -->
                                @if($product->hasActiveChatbot())
                                <button onclick="openProductChatFromStore('{{ $product->name }}', '{{ $seller->store_slug }}', '{{ $product->slug }}')"
                                        class="inline-flex justify-center items-center px-3 py-2 border border-blue-200 text-xs font-medium rounded-lg text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-300">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                                        <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                                    </svg>
                                    <span class="hidden sm:inline">Ask DigiAI</span>
                                    <span class="sm:hidden">AI</span>
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Featured Products Section -->
    @if (isset($featuredProducts) && $featuredProducts->count() > 0)
        <div class="py-12 bg-white">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-10 animate-on-scroll">
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900">Featured Products</h2>
                    <p class="mt-3 text-lg text-gray-600 max-w-3xl mx-auto">
                        Explore our most popular digital products
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
                    @foreach ($featuredProducts as $product)
                    <div class="group relative animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden bg-gray-100">
                            <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}" alt="{{ $product->name }}" class="w-full h-full object-center object-cover group-hover:opacity-75">
                            @if ($product->discount_price)
                            <div class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                                SALE
                            </div>
                            @endif
                        </div>
                        <div class="mt-4 flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-900">
                                    <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                        <span aria-hidden="true" class="absolute inset-0"></span>
                                        {{ $product->name }}
                                    </a>
                                </h3>
                                <p class="mt-1 text-sm text-gray-500">
                                    @if(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory)
                                        {{ $product->productDetailedCategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                        {{ $product->productSubcategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                        {{ $product->productCategory->name }}
                                    @else
                                        {{ ucfirst($product->category) }}
                                    @endif
                                </p>
                            </div>
                            <div>
                                @if ($product->discount_price)
                                    <p class="text-sm font-medium text-indigo-600">Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                    <p class="text-xs text-gray-500 line-through">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @else
                                    <p class="text-sm font-medium text-indigo-600">Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                @endif
                                @if($product->hasActiveChatbot())
                                <div class="mt-1 flex items-center text-xs text-blue-600">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                                        <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                                    </svg>
                                    <span>AI Assistant</span>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Digital Products Section -->
    <div class="py-12 bg-gray-50" id="products">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-10 animate-on-scroll">
                <div class="flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-indigo-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                    </svg>
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900">Digital Products</h2>
                    @if($products->count() > 0)
                        <span class="ml-3 bg-indigo-100 text-indigo-800 text-sm font-semibold px-3 py-1 rounded-full">{{ $products->total() }} Products</span>
                    @endif
                </div>
                <p class="mt-3 text-lg text-gray-600 max-w-3xl mx-auto">
                    Discover our collection of premium digital products designed to help you succeed
                </p>
            </div>
                @if($products->count() > 0)
                    <div class="mb-8">
                        <!-- Categories Filter -->
                        <div class="flex flex-wrap gap-2 justify-center mb-8 animate-on-scroll">
                            <a href="{{ route('store.show', $seller->store_slug) }}"
                                class="px-4 py-2 rounded-full text-sm font-medium {{ !request()->route('category') ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                                All
                            </a>
                            @if(isset($hasNewCategories) && $hasNewCategories)
                                @foreach ($categories as $catSlug => $catName)
                                    <a href="{{ route('store.category', [$seller->store_slug, $catSlug]) }}"
                                        class="px-4 py-2 rounded-full text-sm font-medium {{ request()->route('category') == $catSlug ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                                        {{ $catName }}
                                    </a>
                                @endforeach
                            @else
                                @foreach ($categories as $category)
                                    <a href="{{ route('store.category', [$seller->store_slug, $category]) }}"
                                        class="px-4 py-2 rounded-full text-sm font-medium {{ request()->route('category') == $category ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100' }} transition-colors duration-300 shadow-sm">
                                        {{ $category }}
                                    </a>
                                @endforeach
                            @endif
                        </div>

                <!-- Products Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
                    @foreach ($products as $product)
                        <div class="group relative card-hover bg-white rounded-xl overflow-hidden shadow-md border border-gray-100 animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                            <div class="aspect-w-4 aspect-h-3 overflow-hidden bg-gray-100">
                                <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}">
                                    <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/placeholder.jpg') }}"
                                        alt="{{ $product->name }}"
                                        class="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-110">
                                </a>
                                @if ($product->discount_price)
                                    <div
                                        class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                                        SALE {{ round((($product->price - $product->discount_price) / $product->price) * 100) }}% OFF
                                    </div>
                                @endif
                            </div>
                            <div class="p-5">
                                <h3 class="text-lg font-semibold text-gray-900 line-clamp-1">
                                    <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}" class="hover:text-indigo-600 transition-colors duration-300">
                                        {{ $product->name }}
                                    </a>
                                </h3>
                                <p class="mt-1 text-sm text-gray-500">
                                    @if(isset($hasNewCategories) && $hasNewCategories && $product->productDetailedCategory)
                                        {{ $product->productDetailedCategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productSubcategory)
                                        {{ $product->productSubcategory->name }}
                                    @elseif(isset($hasNewCategories) && $hasNewCategories && $product->productCategory)
                                        {{ $product->productCategory->name }}
                                    @else
                                        {{ ucfirst($product->category) }}
                                    @endif
                                </p>
                                <div class="mt-3 flex justify-between items-center">
                                    <div class="flex items-center">
                                        @for ($i = 1; $i <= 5; $i++)
                                            @if ($i <= ($product->average_rating ?? 5))
                                                <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            @else
                                                <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 0 20 20" fill="currentColor">
                                                    <path
                                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                </svg>
                                            @endif
                                        @endfor
                                        <span class="text-xs text-gray-500 ml-1">({{ $product->reviews_count ?? 0 }})</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    @if ($product->discount_price)
                                        <p class="text-lg font-bold text-indigo-600">
                                            Rp {{ number_format($product->discount_price, 0, ',', '.') }}</p>
                                        <p class="text-sm text-gray-500 line-through">
                                            Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                    @else
                                        <p class="text-lg font-bold text-indigo-600">
                                            Rp {{ number_format($product->price, 0, ',', '.') }}</p>
                                    @endif
                                </div>
                                <!-- Action Buttons -->
                                <div class="mt-4 flex gap-2">
                                    <!-- View Details Button (Left) -->
                                    <a href="{{ route('store.product', [$seller->store_slug, $product->slug]) }}"
                                       class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                                        View Details
                                    </a>

                                    <!-- DigiAI Button (Right) -->
                                    @if($product->hasActiveChatbot())
                                    <button onclick="openProductChatFromStore('{{ $product->name }}', '{{ $seller->store_slug }}', '{{ $product->slug }}')"
                                            class="inline-flex justify-center items-center px-3 py-2 border border-blue-200 text-xs font-medium rounded-lg text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-300">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"/>
                                            <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"/>
                                        </svg>
                                        <span class="hidden sm:inline">Ask DigiAI</span>
                                        <span class="sm:hidden">AI</span>
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                    <!-- Pagination -->
                    <div class="mt-12">
                        <div class="pagination-container">
                            {{ $products->links('vendor.pagination.custom-tailwind') }}
                        </div>
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No digital products yet</h3>
                        <p class="mt-1 text-sm text-gray-500">This seller hasn't added any digital products yet.</p>
                    </div>
                @endif
        </div>
    </div>

    <!-- Online Courses Section -->
    <div class="py-12 bg-white" id="courses">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-10 animate-on-scroll">
                <div class="flex items-center justify-center mb-4">
                    <svg class="w-8 h-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                    </svg>
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900">Online Courses</h2>
                    @if($courses->count() > 0)
                        <span class="ml-3 bg-purple-100 text-purple-800 text-sm font-semibold px-3 py-1 rounded-full">{{ $courses->total() }} Courses</span>
                    @endif
                </div>
                <p class="mt-3 text-lg text-gray-600 max-w-3xl mx-auto">
                    Learn new skills with our comprehensive online courses and tutorials
                </p>
            </div>

            @if($courses->count() > 0)

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                        @foreach ($courses as $course)
                            <div class="group relative card-hover bg-white rounded-xl overflow-hidden shadow-md border border-gray-100 animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                                <div class="relative overflow-hidden bg-gray-100" style="aspect-ratio: 16/9;">
                                    <a href="{{ route('browse.courses.show', $course->slug) }}">
                                        <img src="{{ $course->thumbnail_url }}"
                                            alt="{{ $course->title }}"
                                            class="w-full h-full object-center object-cover transition-transform duration-500 group-hover:scale-110">
                                    </a>
                                    @if ($course->discount_price)
                                        <div class="absolute top-3 right-3 bg-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                                            SAVE {{ round((($course->price - $course->discount_price) / $course->price) * 100) }}% OFF
                                        </div>
                                    @endif
                                    <!-- Course Badge -->
                                    <div class="absolute top-3 left-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg flex items-center">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                                        </svg>
                                        COURSE
                                    </div>
                                </div>
                                <div class="p-5">
                                    <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">
                                        <a href="{{ route('browse.courses.show', $course->slug) }}" class="hover:text-indigo-600 transition-colors duration-300">
                                            {{ $course->title }}
                                        </a>
                                    </h3>
                                    <p class="mt-1 text-sm text-gray-500">
                                        {{ $course->detailedCategory->name ?? $course->subcategory->name ?? $course->category->name ?? 'Course' }}
                                    </p>
                                    <p class="mt-2 text-sm text-gray-600 line-clamp-2">{{ $course->short_description }}</p>

                                    <!-- Course Stats -->
                                    <div class="mt-3 flex items-center text-xs text-gray-500 space-x-4">
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                            {{ $course->sections->count() }} sections
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                            {{ $course->curriculumItems->count() }} lessons
                                        </span>
                                        @if($course->estimated_duration)
                                            <span class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                {{ floor($course->estimated_duration / 60) }}h {{ $course->estimated_duration % 60 }}m
                                            </span>
                                        @endif
                                    </div>

                                    <div class="mt-4">
                                        @if ($course->discount_price)
                                            <p class="text-lg font-bold text-indigo-600">
                                                Rp {{ number_format($course->discount_price, 0, ',', '.') }}</p>
                                            <p class="text-sm text-gray-500 line-through">
                                                Rp {{ number_format($course->price, 0, ',', '.') }}</p>
                                        @else
                                            <p class="text-lg font-bold text-indigo-600">
                                                Rp {{ number_format($course->price, 0, ',', '.') }}</p>
                                        @endif
                                    </div>

                                    <!-- Action Button -->
                                    <div class="mt-4">
                                        <a href="{{ route('browse.courses.show', $course->slug) }}"
                                           class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-300">
                                            View Course
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Courses Pagination -->
                    <div class="mt-12">
                        <div class="pagination-container">
                            {{ $courses->links('vendor.pagination.custom-tailwind') }}
                        </div>
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No courses yet</h3>
                        <p class="mt-1 text-sm text-gray-500">This seller hasn't created any courses yet.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Testimonials -->
    @if(isset($testimonials) && count($testimonials) > 0)
    <div class="py-16 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 animate-on-scroll">
                <h2 class="text-3xl font-bold text-gray-900 sm:text-4xl">What Our Customers Say</h2>
                <p class="mt-3 text-xl text-gray-600 max-w-3xl mx-auto">
                    Read testimonials from our satisfied customers who have used our digital products
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-{{ count($testimonials) >= 3 ? '3' : (count($testimonials) == 2 ? '2' : '1') }} gap-8 max-w-5xl mx-auto">
                @foreach ($testimonials as $testimonial)
                    <div class="bg-white p-6 rounded-xl border border-gray-100 shadow-sm card-hover animate-on-scroll" style="animation-delay: {{ $loop->index * 100 }}ms">
                        <div class="flex items-center mb-3">
                            <div class="flex">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $testimonial['rating'])
                                        <svg class="h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @else
                                        <svg class="h-4 w-4 text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endif
                                @endfor
                            </div>
                        </div>
                        <p class="text-gray-600 italic mb-5 text-lg leading-relaxed">"{{ $testimonial['quote'] }}"</p>
                        <div class="flex items-center">
                            <div class="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center text-purple-700 font-bold text-xl mr-3">
                                {{ substr($testimonial['author'], 0, 1) }}
                            </div>
                            <div>
                                <p class="text-gray-900 font-semibold">{{ $testimonial['author'] }}</p>
                                <p class="text-gray-500 text-sm">{{ $testimonial['role'] }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- About Store Section -->
    <div class="py-16 bg-white" id="about-store">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-4xl mx-auto">
                <!-- Section Header -->
                <div class="text-center mb-12 animate-on-scroll">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">About {{ $seller->store_name }}</h2>
                    <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                        Learn more about our story, mission, and the quality digital products we offer
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <!-- Store Story -->
                    <div class="animate-on-scroll">
                        <div class="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-8 border border-indigo-100">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-xl flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-900 ml-4">Our Story</h3>
                            </div>
                            <p class="text-gray-700 leading-relaxed">
                                @if($seller->sellerApplication && !empty($seller->sellerApplication->store_description))
                                    {{ $seller->sellerApplication->store_description }}
                                @else
                                    {{ $seller->store_name }} was founded with a mission to provide top-notch digital solutions for professionals, entrepreneurs, and hobbyists alike. We specialize in creating products that are easy to use, highly functional, and designed to save you time and effort.
                                @endif
                            </p>
                        </div>
                    </div>

                    <!-- Store Stats & Info -->
                    <div class="animate-on-scroll" style="animation-delay: 200ms;">
                        <div class="space-y-6">
                            <!-- Product Categories -->
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900 ml-3">Our Products</h4>
                                </div>
                                <p class="text-gray-600 mb-4">
                                    We offer a wide range of digital products
                                    @if(isset($categories) && count($categories) > 0)
                                        including {{ implode(', ', array_slice($categories, 0, -1)) }}{{ count($categories) > 1 ? ' and ' . end($categories) : (count($categories) == 1 ? ' ' . end($categories) : '') }}
                                    @else
                                        for various needs
                                    @endif.
                                </p>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center p-3 bg-indigo-50 rounded-lg">
                                        <div class="text-2xl font-bold text-indigo-600">{{ $products->total() ?? 0 }}</div>
                                        <div class="text-sm text-gray-600">Digital Products</div>
                                    </div>
                                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                                        <div class="text-2xl font-bold text-purple-600">{{ $courses->total() ?? 0 }}</div>
                                        <div class="text-sm text-gray-600">Online Courses</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-900 ml-3">Get in Touch</h4>
                                </div>
                                <p class="text-gray-600 mb-4">
                                    Have questions or need support? We're here to help!
                                </p>
                                <div class="space-y-3">
                                    <div class="flex items-center text-sm">
                                        <span class="font-medium text-gray-900 w-20">Email:</span>
                                        <span class="text-gray-600">{{ $seller->email }}</span>
                                    </div>
                                    <div class="flex items-center text-sm">
                                        <span class="font-medium text-gray-900 w-20">Hours:</span>
                                        <span class="text-gray-600">Monday - Friday, 9 AM - 5 PM</span>
                                    </div>
                                </div>
                                <a href="mailto:{{ $seller->email }}" class="mt-4 inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-sm font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    Contact Us
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


@endsection

@push('scripts')
<script>
// Tab switching functionality
function switchTab(tabName) {
    console.log('🔄 Switching to tab:', tabName);

    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
        console.log('🙈 Hiding:', content.id);
    });

    // Remove active styles from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('bg-indigo-600', 'text-white');
        button.classList.add('text-gray-700', 'hover:text-indigo-600');
    });

    // Show selected tab content
    const targetContent = document.getElementById(tabName + '-content');
    console.log('🎯 Target content element:', targetContent);
    if (targetContent) {
        targetContent.classList.remove('hidden');
        console.log('👁️ Showing:', tabName + '-content');
    } else {
        console.error('❌ Target content not found:', tabName + '-content');
    }

    // Add active styles to selected tab button
    const activeButton = document.getElementById(tabName + '-tab');
    console.log('🔘 Active button:', activeButton);
    if (activeButton) {
        activeButton.classList.add('bg-indigo-600', 'text-white');
        activeButton.classList.remove('text-gray-700', 'hover:text-indigo-600');
        console.log('✅ Button activated for:', tabName);
    } else {
        console.error('❌ Button not found:', tabName + '-tab');
    }
}

// Make function globally available
window.switchTab = switchTab;

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileMenuButton = document.querySelector('[data-mobile-menu-toggle]');
    const mobileMenu = document.getElementById('mobile-menu');
    const menuIcon = mobileMenuButton?.querySelector('.menu-icon');
    const closeIcon = mobileMenuButton?.querySelector('.close-icon');

    if (mobileMenuButton && mobileMenu) {
        const toggleMenu = function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isHidden = mobileMenu.classList.contains('hidden');

            if (isHidden) {
                mobileMenu.classList.remove('hidden');
                menuIcon?.classList.add('hidden');
                closeIcon?.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            } else {
                mobileMenu.classList.add('hidden');
                menuIcon?.classList.remove('hidden');
                closeIcon?.classList.add('hidden');
                document.body.style.overflow = '';
            }
        };

        mobileMenuButton.addEventListener('click', toggleMenu);
        mobileMenuButton.addEventListener('touchstart', toggleMenu, { passive: false });

        // Close menu when clicking on links
        const mobileMenuLinks = mobileMenu.querySelectorAll('a');
        mobileMenuLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
                menuIcon?.classList.remove('hidden');
                closeIcon?.classList.add('hidden');
                document.body.style.overflow = '';
            });
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                    menuIcon?.classList.remove('hidden');
                    closeIcon?.classList.add('hidden');
                    document.body.style.overflow = '';
                }
            }
        });
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    switchTab('products');
    initializeMobileMenu();
});
</script>
@endpush

@push('scripts')
<!-- Store-specific JavaScript -->
<script src="{{ asset('dev-js/store.js') }}" defer></script>
@endpush





