<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupAiChat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:ai-chat';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the AI chat system by running migrations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up AI Chat system...');
        
        // Run the migrations
        $this->info('Running migrations for AI Chat tables...');
        Artisan::call('migrate', ['--path' => 'database/migrations/2024_07_01_000001_create_ai_conversations_table.php']);
        $this->info(Artisan::output());
        
        Artisan::call('migrate', ['--path' => 'database/migrations/2024_07_01_000002_create_ai_messages_table.php']);
        $this->info(Artisan::output());
        
        $this->info('AI Chat system setup complete!');
        
        // Remind about environment variables
        $this->info('');
        $this->info('IMPORTANT: Make sure to add the following to your .env file:');
        $this->info('GEMINI_API_KEY=AIzaSyBGd-8ZtsevUl4lr1Kqrg9WEMPC83F7gfQ');
        $this->info('GEMINI_PROJECT_NUMBER=7712555130');
        $this->info('GEMINI_MODEL=gemini-2.0-flash');
        
        return Command::SUCCESS;
    }
}
