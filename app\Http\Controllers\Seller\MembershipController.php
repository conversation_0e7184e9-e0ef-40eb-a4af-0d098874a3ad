<?php

namespace App\Http\Controllers\Seller;

use App\Http\Controllers\Controller;
use App\Models\MembershipTier;
use App\Models\UserMembership;
use App\Models\AiUsageLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MembershipController extends Controller
{
    /**
     * Display membership tiers and current user's membership status for sellers.
     */
    public function index()
    {
        $tiers = MembershipTier::active()->get();
        $currentMembership = null;
        $currentTier = null;
        $todayUsage = 0;
        $remainingPrompts = 0;

        if (Auth::check()) {
            $user = Auth::user();
            $currentMembership = $user->currentMembership;
            $currentTier = $user->getCurrentMembershipTier();
            $todayUsage = AiUsageLog::getTotalTodayUsage($user->id);
            $remainingPrompts = $user->getRemainingAiPrompts();
        }

        return view('seller.membership.index', compact(
            'tiers', 
            'currentMembership', 
            'currentTier', 
            'todayUsage', 
            'remainingPrompts'
        ));
    }

    /**
     * Upgrade user membership (placeholder for payment integration).
     */
    public function upgrade(Request $request)
    {
        $request->validate([
            'tier_slug' => 'required|string|exists:membership_tiers,slug'
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }

        $user = Auth::user();
        $newTier = MembershipTier::where('slug', $request->tier_slug)->first();

        if (!$newTier) {
            return response()->json(['error' => 'Invalid membership tier'], 400);
        }

        // For now, just create the membership (in real implementation, integrate with payment)
        // Deactivate current membership
        $user->memberships()->where('status', 'active')->update(['status' => 'cancelled']);

        // Create new membership
        $membership = UserMembership::create([
            'user_id' => $user->id,
            'membership_tier_id' => $newTier->id,
            'started_at' => now(),
            'expires_at' => $newTier->price > 0 ? now()->addMonth() : null, // Free tier doesn't expire
            'status' => 'active',
            'amount_paid' => $newTier->price,
            'payment_method' => 'manual', // Placeholder
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Membership upgraded successfully',
            'new_tier' => $newTier->name,
            'membership' => $membership
        ]);
    }
}
