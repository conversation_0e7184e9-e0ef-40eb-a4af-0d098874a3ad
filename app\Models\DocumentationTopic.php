<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

class DocumentationTopic extends Model
{
    use HasUuids;

    protected $fillable = ['section_id', 'title', 'slug', 'content', 'last_updated', 'helpful_yes', 'helpful_no'];

    protected $casts = [
        'last_updated' => 'date',
    ];

    public function section()
    {
        return $this->belongsTo(DocumentationSection::class);
    }

    public function relatedTopics()
    {
        return $this->belongsToMany(DocumentationTopic::class, 'documentation_topic_related', 'topic_id', 'related_topic_id');
    }
}
