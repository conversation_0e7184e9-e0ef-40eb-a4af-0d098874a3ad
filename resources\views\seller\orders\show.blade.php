@extends('seller.layouts.app')

@section('content')
<div class="space-y-6">
    <div class="flex items-center gap-4">
        <a href="{{ route('seller.orders.index', [], false) ?? '#' }}" class="rounded-md p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                <path d="m15 18-6-6 6-6"></path>
            </svg>
            <span class="sr-only">Back</span>
        </a>
        <!-- TODO: Revisit route 'seller.orders.index' if it fails -->
        <div>
            <h1 class="text-3xl font-bold tracking-tight">Order #{{ $order->order_id ?? 'N/A' }}</h1>
            <p class="text-gray-500">{{ $order->created_at->format('F d, Y \a\t h:i A') ?? 'N/A' }}</p>
        </div>
    </div>

    <div class="grid gap-6 md:grid-cols-3">
        <div class="md:col-span-2 space-y-6">
            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Order Details</h3>
                </div>
                <div class="p-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Order Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">#{{ $order->order_id ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Date Placed</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order->created_at->format('F d, Y') ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Order Status</dt>
                            <dd class="mt-1 text-sm">
                                <span class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium
                                    @if(($order->status ?? 'pending') === 'success')
                                        border-green-100 bg-green-50 text-green-700
                                    @elseif(($order->status ?? 'pending') === 'pending')
                                        border-amber-100 bg-amber-50 text-amber-700
                                    @elseif(($order->status ?? 'pending') === 'cancel')
                                        border-red-100 bg-red-50 text-red-700
                                    @elseif(($order->status ?? 'pending') === 'expired')
                                        border-gray-100 bg-gray-50 text-gray-700
                                    @elseif(($order->status ?? 'pending') === 'failed')
                                        border-red-100 bg-red-50 text-red-700
                                    @endif">
                                    {{ ucfirst($order->status ?? 'pending') }}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Payment Status</dt>
                            <dd class="mt-1 text-sm">
                                <span class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium border-green-100 bg-green-50 text-green-700">
                                    Paid
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Customer</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order->user->name ?? 'Unknown Customer' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Customer Email</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $order->user->email ?? 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Product Information</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center gap-4">
                        <div class="h-16 w-16 flex-shrink-0 rounded-md bg-gray-100">
                            <img src="{{ asset('images/placeholder.jpg') }}" alt="{{ $order->item_name }}" class="h-16 w-16 rounded-md object-cover">
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium">{{ $order->item_name }}</h4>
                            <p class="text-sm text-gray-500">{{ $order->item_type }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium">Rp {{ number_format($order->amount ?? 0, 0, ',', '.') }}</p>
                            <p class="text-sm text-gray-500">x1</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="space-y-6">
            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Order Summary</h3>
                </div>
                <div class="p-6">
                    @php
                        $revenueData = $order->calculateSellerRevenue();
                    @endphp
                    <dl class="space-y-4">
                        <div class="flex items-center justify-between">
                            <dt class="text-sm text-gray-500">Gross Amount</dt>
                            <dd class="text-sm font-medium text-gray-900">Rp {{ number_format($order->amount ?? 0, 0, ',', '.') }}</dd>
                        </div>
                        <div class="flex items-center justify-between">
                            <dt class="text-sm text-gray-500">Platform Base Fee ({{ $revenueData['platform_base_fee_rate'] }}%)</dt>
                            <dd class="text-sm font-medium text-red-600">-Rp {{ number_format($revenueData['platform_base_fee'], 0, ',', '.') }}</dd>
                        </div>
                        <div class="flex items-center justify-between">
                            <dt class="text-sm text-gray-500">{{ ucfirst($revenueData['order_type']) }} Transaction Fee ({{ $revenueData['transaction_fee_rate'] }}%)</dt>
                            <dd class="text-sm font-medium text-red-600">-Rp {{ number_format($revenueData['transaction_fee'], 0, ',', '.') }}</dd>
                        </div>
                        <div class="border-t border-gray-200 pt-4 flex items-center justify-between">
                            <dt class="text-base font-medium text-gray-900">Your Net Earnings</dt>
                            <dd class="text-base font-medium text-green-600">Rp {{ number_format($revenueData['net_amount'], 0, ',', '.') }}</dd>
                        </div>
                        <div class="border-t border-gray-200 pt-4 flex items-center justify-between">
                            <dt class="text-base font-medium text-gray-900">Your Earnings</dt>
                            <dd class="text-base font-medium text-gray-900">Rp {{ number_format(($order->amount ?? 0) * 0.95, 0, ',', '.') }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <div class="rounded-lg border bg-white shadow-sm">
                <div class="border-b p-6">
                    <h3 class="text-lg font-medium">Actions</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Status update actions removed - only showing successful orders -->

                        <a href="{{ route('seller.orders.downloadInvoice', $order, false) ?? '#' }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            Download Invoice
                        </a>
                        <!-- TODO: Revisit route 'seller.orders.downloadInvoice' if it fails -->

                        <a href="mailto:{{ $order->user->email ?? '' }}" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                            Contact Customer
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
