<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Course;
use App\Models\User;
use App\Models\Review;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class HomeController extends Controller
{
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Get stats for the homepage
        $productCount = Product::where('status', 'active')->count();
        $courseCount = Course::where('status', 'active')->count();
        $sellerCount = User::whereHas('userRoles', function($query) {
            $query->where('is_active', true)
                  ->whereHas('role', function($roleQuery) {
                      $roleQuery->where('slug', 'seller');
                  });
        })->count();
        $userCount = User::count();
        $avgRating = Review::avg('rating') ?? 4.5;
        $avgRating = number_format($avgRating, 1);

        // Get featured content for three main sections with error handling
        try {
            $featuredProducts = $this->getFeaturedProducts();
        } catch (\Exception $e) {
            Log::error('Error fetching featured products: ' . $e->getMessage());
            $featuredProducts = collect([]);
        }

        try {
            $featuredCourses = $this->getFeaturedCourses();
        } catch (\Exception $e) {
            Log::error('Error fetching featured courses: ' . $e->getMessage());
            $featuredCourses = collect([]);
        }

        try {
            $featuredStores = $this->getFeaturedStores();
        } catch (\Exception $e) {
            Log::error('Error fetching featured stores: ' . $e->getMessage());
            $featuredStores = [];
        }

        // Get top 5 product categories with the most products
        $categories = \App\Models\ProductCategory::where('is_active', true)
            ->withCount(['products' => function ($query) {
                $query->where('status', 'active');
            }])
            ->having('products_count', '>', 0) // Only include categories with products
            ->orderByDesc('products_count')
            ->take(5) // Limit to top 5 categories
            ->get()
            ->map(function ($category) {
                return [
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'count' => $category->products_count,
                    'icon' => $this->getCategoryIcon($category->slug),
                ];
            });

        // Get categories for display (keeping existing logic)
        $categories = collect([
            ['name' => 'eBooks', 'slug' => 'ebooks', 'count' => 150, 'icon' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>'],
            ['name' => 'Templates', 'slug' => 'templates', 'count' => 200, 'icon' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>'],
            ['name' => 'Software', 'slug' => 'software', 'count' => 75, 'icon' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path></svg>'],
            ['name' => 'Graphics', 'slug' => 'graphics', 'count' => 120, 'icon' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>'],
            ['name' => 'Audio', 'slug' => 'audio', 'count' => 90, 'icon' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path></svg>']
        ]);

        // Get testimonials from real reviews
        $testimonials = Review::with(['user', 'product'])
            ->where('rating', '>=', 4) // Only use reviews with high ratings (4 or 5 stars)
            ->whereNotNull('comment') // Only use reviews with comments
            ->where(DB::raw('LENGTH(comment)'), '>=', 50) // Only use reviews with substantial comments
            ->inRandomOrder() // Get random reviews
            ->take(3) // Limit to 3 reviews
            ->get()
            ->map(function ($review) {
                return [
                    'quote' => $review->comment,
                    'author' => $review->user->name,
                    'role' => $review->product ? 'Customer of ' . $review->product->name : 'Customer',
                    'rating' => $review->rating
                ];
            })
            ->toArray();

        // Define the steps for the how it works section
        $steps = [
            [
                'step' => 1,
                'title' => 'Create an Account',
                'description' => 'Sign up for free and set up your profile in minutes with our streamlined process.',
            ],
            [
                'step' => 2,
                'title' => 'Browse & Discover',
                'description' => 'Explore premium digital products, online courses, and creator stores from Indonesian entrepreneurs.',
            ],
            [
                'step' => 3,
                'title' => 'Secure Payment',
                'description' => 'Use our trusted secure payment methods for safe transactions in IDR currency.',
            ],
            [
                'step' => 4,
                'title' => 'Learn & Grow',
                'description' => 'Get instant access to digital products and start learning with structured online courses.',
            ],
        ];

        return view('welcome', compact(
            'productCount',
            'courseCount',
            'sellerCount',
            'userCount',
            'avgRating',
            'categories',
            'featuredProducts',
            'featuredCourses',
            'testimonials',
            'steps',
            'featuredStores'
        ));
    }

    /**
     * Get featured stores with their most common product category
     *
     * @return array
     */
    private function getFeaturedStores()
    {
        // Get top sellers with approved applications and active products
        $topSellers = User::whereHas('userRoles', function($query) {
                $query->where('is_active', true)
                      ->whereHas('role', function($roleQuery) {
                          $roleQuery->where('slug', 'seller');
                      });
            })
            ->whereHas('sellerApplication', function ($query) {
                $query->where('status', 'approved');
            })
            ->withCount(['products' => function ($query) {
                $query->where('status', 'active');
            }])
            ->having('products_count', '>', 0)
            ->orderByDesc('products_count')
            ->take(4)
            ->get();

        $featuredStores = [];

        foreach ($topSellers as $seller) {
            // Get the seller's store information
            $store = $seller->sellerApplication;
            if (!$store) continue;

            // Get the most common category for this seller's products
            $categoryData = Product::where('seller_id', $seller->id)
                ->where('status', 'active')
                ->with(['productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->get();

            // Track category counts
            $categoryCounts = [];

            foreach ($categoryData as $product) {
                // Use only the main category (not detailed or subcategory)
                $categoryName = null;
                $categorySlug = null;

                if ($product->productCategory) {
                    $categoryName = $product->productCategory->name;
                    $categorySlug = $product->productCategory->slug;
                } else {
                    $categoryName = ucfirst($product->category); // Legacy fallback
                    $categorySlug = \Illuminate\Support\Str::slug($product->category);
                }

                if ($categoryName) {
                    if (!isset($categoryCounts[$categoryName])) {
                        $categoryCounts[$categoryName] = [
                            'count' => 1,
                            'slug' => $categorySlug
                        ];
                    } else {
                        $categoryCounts[$categoryName]['count']++;
                    }
                }
            }

            // Find the most common category
            $topCategory = null;
            $topCount = 0;

            foreach ($categoryCounts as $name => $data) {
                if ($data['count'] > $topCount) {
                    $topCount = $data['count'];
                    $topCategory = [
                        'name' => $name,
                        'slug' => $data['slug']
                    ];
                }
            }

            // Add to featured stores array
            $featuredStores[] = [
                'name' => $store->store_name,
                'slug' => $store->store_name_slug,
                'description' => \Illuminate\Support\Str::limit($store->store_description, 100),
                'logo' => $store->store_logo ?? null,
                'product_count' => $seller->products_count,
                'main_category' => $topCategory,
                'user_avatar' => $seller->avatar ?? null,
                'user_name' => $seller->name
            ];
        }

        return $featuredStores;
    }

    /**
     * Get the appropriate icon for a category
     *
     * @param string $categorySlug
     * @return string
     */
    private function getCategoryIcon($categorySlug)
    {
        $icons = [
            'productivity-tools' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>',
            'design-assets' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>',
            'development-resources' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path></svg>',
            'educational-content' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>',
            'multimedia' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>',
            'software-apps' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>',
            'printables' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path></svg>',
            'photography' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
            // Legacy category icons for backward compatibility
            'template' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7h6v10m-6 0h6m-3-14v14m-9 0h18"></path></svg>',
            'spreadsheet' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7h6v10m-6 0h6m-3-14v14m-9 0h18"></path></svg>',
            'graphic' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4l2 4h4a2 2 0 012 2v12a4 4 0 01-4 4H7z"></path></svg>',
            'font' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>',
            'default' => '<svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>',
        ];

        return $icons[$categorySlug] ?? $icons['default'];
    }

    /**
     * Get featured products for homepage
     *
     * @return array
     */
    private function getFeaturedProducts()
    {
        // Get featured products - first try to get featured products, if not enough, get regular products
        $featuredQuery = Product::where('status', 'active')
            ->where('is_featured', true)
            ->with(['seller', 'sellerApplication', 'productCategory', 'productSubcategory', 'productDetailedCategory'])
            ->whereHas('sellerApplication') // Make sure the product has a sellerApplication
            ->orderByDesc('created_at');

        $featuredCount = $featuredQuery->count();

        // If we don't have enough featured products, get some regular products too
        if ($featuredCount < 4) {
            $featuredProducts = $featuredQuery->get();

            // Get additional regular products if needed
            $regularProducts = Product::where('status', 'active')
                ->where(function ($query) {
                    $query->where('is_featured', false)
                        ->orWhereNull('is_featured');
                })
                ->with(['seller', 'sellerApplication', 'productCategory', 'productSubcategory', 'productDetailedCategory'])
                ->whereHas('sellerApplication') // Make sure the product has a sellerApplication
                ->orderByDesc('created_at')
                ->take(4 - $featuredCount)
                ->get();

            // Combine the collections
            $allProducts = $featuredProducts->concat($regularProducts);
        } else {
            // We have enough featured products
            $allProducts = $featuredQuery->take(4)->get();
        }

        // Map the products to the format we need
        return $allProducts->map(function ($product) {
            // Make sure the product has a seller and sellerApplication
            if (!$product->seller || !$product->sellerApplication) {
                return null;
            }

            return [
                'name' => $product->name,
                'creator' => $product->seller->name,
                'store_name_slug' => $product->sellerApplication->store_name_slug,
                'price' => $product->price,
                'discount_price' => $product->discount_price,
                'rating' => $product->average_rating ?? 4.5,
                'reviews' => $product->reviews_count ?? 0,
                'image' => $product->image ? asset('storage/' . $product->image) : 'https://via.placeholder.com/300x200?text=' . urlencode($product->name),
                'slug' => $product->slug,
                'discount_percentage' => $product->getDiscountPercentageAttribute(),
                'category' => $product->getCategoryNameAttribute(),
                'subcategory' => $product->getSubcategoryNameAttribute(),
                'detailed_category' => $product->getDetailedCategoryNameAttribute(),
            ];
        })->filter()->values(); // Remove null values and reindex the array
    }

    /**
     * Get featured courses for homepage
     *
     * @return array
     */
    private function getFeaturedCourses()
    {
        // Get featured courses - first try to get featured courses, if not enough, get regular courses
        $featuredQuery = Course::where('status', 'active')
            ->where('is_featured', true)
            ->with(['seller.sellerApplication', 'category', 'subcategory', 'detailedCategory'])
            ->whereHas('seller.sellerApplication', function($query) {
                $query->where('status', 'approved');
            })
            ->orderByDesc('created_at');

        $featuredCount = $featuredQuery->count();

        // If we don't have enough featured courses, get some regular courses too
        if ($featuredCount < 4) {
            $featuredCourses = $featuredQuery->get();

            // Get additional regular courses if needed
            $regularCourses = Course::where('status', 'active')
                ->where(function ($query) {
                    $query->where('is_featured', false)
                        ->orWhereNull('is_featured');
                })
                ->with(['seller.sellerApplication', 'category', 'subcategory', 'detailedCategory'])
                ->whereHas('seller.sellerApplication', function($query) {
                    $query->where('status', 'approved');
                })
                ->orderByDesc('created_at')
                ->take(4 - $featuredCount)
                ->get();

            // Combine the collections
            $allCourses = $featuredCourses->concat($regularCourses);
        } else {
            // We have enough featured courses
            $allCourses = $featuredQuery->take(4)->get();
        }

        // Map the courses to the format we need
        return $allCourses->map(function ($course) {
            // Make sure the course has a seller and sellerApplication
            if (!$course->seller || !$course->seller->sellerApplication) {
                return null;
            }

            return [
                'title' => $course->title,
                'instructor' => $course->seller->name,
                'store_name_slug' => $course->seller->sellerApplication->store_name_slug,
                'price' => $course->price,
                'discount_price' => $course->discount_price,
                'rating' => $course->average_rating ?? 4.5,
                'students' => $course->students_count ?? 0,
                'thumbnail' => $course->thumbnail ? asset('storage/' . $course->thumbnail) : 'https://via.placeholder.com/300x200?text=' . urlencode($course->title),
                'slug' => $course->slug,
                'difficulty' => ucfirst($course->difficulty_level),
                'duration' => $course->estimated_duration ? $course->estimated_duration . ' hours' : 'Self-paced',
                'category' => $course->detailedCategory ? $course->detailedCategory->name : ($course->subcategory ? $course->subcategory->name : ($course->category ? $course->category->name : 'Course')),
                'short_description' => $course->short_description,
            ];
        })->filter()->values(); // Remove null values and reindex the array
    }
}
